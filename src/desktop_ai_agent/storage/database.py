"""
Database management for Desktop AI Agent.

This module provides database connectivity, schema management,
and data persistence capabilities using SQLAlchemy.
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Float,
    Integer,
    String,
    Text,
    create_engine,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from desktop_ai_agent.core.config import Settings

Base = declarative_base()


class TaskRecord(Base):
    """Task record table."""
    __tablename__ = "tasks"
    
    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    type = Column(String, default="simple")
    status = Column(String, default="queued")
    priority = Column(String, default="normal")
    parameters = Column(Text)  # JSON
    dependencies = Column(Text)  # JSON
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    progress = Column(Float, default=0.0)
    result = Column(Text)  # JSON
    error_message = Column(Text)


class SessionRecord(Base):
    """Session record table."""
    __tablename__ = "sessions"
    
    id = Column(String, primary_key=True)
    user_id = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_activity = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    metadata = Column(Text)  # JSON


class AuditRecord(Base):
    """Audit record table."""
    __tablename__ = "audit_events"
    
    id = Column(String, primary_key=True)
    event_type = Column(String, nullable=False)
    user_id = Column(String)
    operation = Column(String)
    resource = Column(String)
    success = Column(Boolean, default=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    details = Column(Text)  # JSON


class ConfigRecord(Base):
    """Configuration record table."""
    __tablename__ = "configurations"
    
    key = Column(String, primary_key=True)
    value = Column(Text)
    type = Column(String, default="string")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)


class DatabaseManager:
    """Database management system."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        
        # Create database engine
        database_url = settings.get_database_url()
        self.engine = create_engine(
            database_url,
            echo=settings.database.echo,
            pool_size=settings.database.pool_size,
            max_overflow=settings.database.max_overflow
        )
        
        # Create session factory
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        self.logger.info("Database manager initialized", url=database_url)
    
    async def initialize(self) -> None:
        """Initialize database schema."""
        try:
            # Create all tables
            Base.metadata.create_all(bind=self.engine)
            
            self.logger.info("Database schema initialized")
        except Exception as e:
            self.logger.error("Database initialization failed", error=str(e))
            raise
    
    def get_session(self):
        """Get database session."""
        return self.SessionLocal()
    
    # Task operations
    
    def save_task(self, task_data: Dict[str, Any]) -> bool:
        """Save task to database."""
        try:
            with self.get_session() as session:
                task_record = TaskRecord(
                    id=task_data["task_id"],
                    name=task_data["name"],
                    description=task_data.get("description"),
                    type=task_data.get("type", "simple"),
                    status=task_data.get("status", "queued"),
                    priority=task_data.get("priority", "normal"),
                    parameters=json.dumps(task_data.get("parameters", {})),
                    dependencies=json.dumps(task_data.get("dependencies", [])),
                    progress=task_data.get("progress", 0.0),
                    result=json.dumps(task_data.get("result")) if task_data.get("result") else None,
                    error_message=task_data.get("error_message")
                )
                
                # Handle timestamps
                if task_data.get("created_at"):
                    task_record.created_at = task_data["created_at"]
                if task_data.get("started_at"):
                    task_record.started_at = task_data["started_at"]
                if task_data.get("completed_at"):
                    task_record.completed_at = task_data["completed_at"]
                
                session.merge(task_record)
                session.commit()
                
                return True
                
        except Exception as e:
            self.logger.error("Failed to save task", task_id=task_data.get("task_id"), error=str(e))
            return False
    
    def load_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Load task from database."""
        try:
            with self.get_session() as session:
                task_record = session.query(TaskRecord).filter(TaskRecord.id == task_id).first()
                
                if not task_record:
                    return None
                
                return {
                    "task_id": task_record.id,
                    "name": task_record.name,
                    "description": task_record.description,
                    "type": task_record.type,
                    "status": task_record.status,
                    "priority": task_record.priority,
                    "parameters": json.loads(task_record.parameters) if task_record.parameters else {},
                    "dependencies": json.loads(task_record.dependencies) if task_record.dependencies else [],
                    "created_at": task_record.created_at,
                    "started_at": task_record.started_at,
                    "completed_at": task_record.completed_at,
                    "progress": task_record.progress,
                    "result": json.loads(task_record.result) if task_record.result else None,
                    "error_message": task_record.error_message
                }
                
        except Exception as e:
            self.logger.error("Failed to load task", task_id=task_id, error=str(e))
            return None
    
    def list_tasks(self, status: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """List tasks from database."""
        try:
            with self.get_session() as session:
                query = session.query(TaskRecord)
                
                if status:
                    query = query.filter(TaskRecord.status == status)
                
                query = query.order_by(TaskRecord.created_at.desc()).limit(limit)
                
                tasks = []
                for record in query.all():
                    tasks.append({
                        "task_id": record.id,
                        "name": record.name,
                        "status": record.status,
                        "priority": record.priority,
                        "created_at": record.created_at,
                        "progress": record.progress
                    })
                
                return tasks
                
        except Exception as e:
            self.logger.error("Failed to list tasks", error=str(e))
            return []
    
    # Session operations
    
    def save_session(self, session_data: Dict[str, Any]) -> bool:
        """Save session to database."""
        try:
            with self.get_session() as session:
                session_record = SessionRecord(
                    id=session_data["session_id"],
                    user_id=session_data.get("user_id"),
                    is_active=session_data.get("is_active", True),
                    metadata=json.dumps(session_data.get("metadata", {}))
                )
                
                if session_data.get("created_at"):
                    session_record.created_at = session_data["created_at"]
                if session_data.get("last_activity"):
                    session_record.last_activity = session_data["last_activity"]
                
                session.merge(session_record)
                session.commit()
                
                return True
                
        except Exception as e:
            self.logger.error("Failed to save session", session_id=session_data.get("session_id"), error=str(e))
            return False
    
    def load_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Load session from database."""
        try:
            with self.get_session() as session:
                session_record = session.query(SessionRecord).filter(SessionRecord.id == session_id).first()
                
                if not session_record:
                    return None
                
                return {
                    "session_id": session_record.id,
                    "user_id": session_record.user_id,
                    "created_at": session_record.created_at,
                    "last_activity": session_record.last_activity,
                    "is_active": session_record.is_active,
                    "metadata": json.loads(session_record.metadata) if session_record.metadata else {}
                }
                
        except Exception as e:
            self.logger.error("Failed to load session", session_id=session_id, error=str(e))
            return None
    
    # Audit operations
    
    def save_audit_event(self, event_data: Dict[str, Any]) -> bool:
        """Save audit event to database."""
        try:
            with self.get_session() as session:
                audit_record = AuditRecord(
                    id=event_data["event_id"],
                    event_type=event_data["event_type"],
                    user_id=event_data.get("user_id"),
                    operation=event_data.get("operation"),
                    resource=event_data.get("resource"),
                    success=event_data.get("success", True),
                    details=json.dumps(event_data.get("details", {}))
                )
                
                if event_data.get("timestamp"):
                    audit_record.timestamp = event_data["timestamp"]
                
                session.add(audit_record)
                session.commit()
                
                return True
                
        except Exception as e:
            self.logger.error("Failed to save audit event", error=str(e))
            return False
    
    # Configuration operations
    
    def save_config(self, key: str, value: Any, value_type: str = "string") -> bool:
        """Save configuration value."""
        try:
            with self.get_session() as session:
                config_record = ConfigRecord(
                    key=key,
                    value=json.dumps(value) if value_type != "string" else str(value),
                    type=value_type,
                    updated_at=datetime.utcnow()
                )
                
                session.merge(config_record)
                session.commit()
                
                return True
                
        except Exception as e:
            self.logger.error("Failed to save config", key=key, error=str(e))
            return False
    
    def load_config(self, key: str, default: Any = None) -> Any:
        """Load configuration value."""
        try:
            with self.get_session() as session:
                config_record = session.query(ConfigRecord).filter(ConfigRecord.key == key).first()
                
                if not config_record:
                    return default
                
                if config_record.type == "string":
                    return config_record.value
                else:
                    return json.loads(config_record.value)
                
        except Exception as e:
            self.logger.error("Failed to load config", key=key, error=str(e))
            return default
    
    async def cleanup(self) -> None:
        """Clean up database connections."""
        try:
            self.engine.dispose()
            self.logger.info("Database cleanup completed")
        except Exception as e:
            self.logger.error("Database cleanup failed", error=str(e))
