"""
Session management for Desktop AI Agent.

This module provides session lifecycle management, user state tracking,
and session persistence capabilities.
"""

import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import structlog

from desktop_ai_agent.storage.database import DatabaseManager


class SessionManager:
    """Session management system."""
    
    def __init__(self, database_manager: DatabaseManager):
        self.database_manager = database_manager
        self.logger = structlog.get_logger(__name__)
        
        # Active sessions cache
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        self.logger.info("Session manager initialized")
    
    def create_session(self, user_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Create a new session."""
        session_id = str(uuid.uuid4())
        
        session_data = {
            "session_id": session_id,
            "user_id": user_id,
            "created_at": datetime.utcnow(),
            "last_activity": datetime.utcnow(),
            "is_active": True,
            "metadata": metadata or {}
        }
        
        # Cache session
        self.active_sessions[session_id] = session_data
        
        # Persist to database
        self.database_manager.save_session(session_data)
        
        self.logger.info("Session created", session_id=session_id, user_id=user_id)
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data."""
        # Check cache first
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session["last_activity"] = datetime.utcnow()
            return session
        
        # Load from database
        session = self.database_manager.load_session(session_id)
        if session and session["is_active"]:
            self.active_sessions[session_id] = session
            return session
        
        return None
    
    def update_session(self, session_id: str, metadata: Dict[str, Any]) -> bool:
        """Update session metadata."""
        session = self.get_session(session_id)
        if not session:
            return False
        
        session["metadata"].update(metadata)
        session["last_activity"] = datetime.utcnow()
        
        # Persist changes
        return self.database_manager.save_session(session)
    
    def end_session(self, session_id: str) -> bool:
        """End a session."""
        session = self.get_session(session_id)
        if not session:
            return False
        
        session["is_active"] = False
        session["last_activity"] = datetime.utcnow()
        
        # Remove from cache
        self.active_sessions.pop(session_id, None)
        
        # Persist changes
        return self.database_manager.save_session(session)
    
    def cleanup_expired_sessions(self, max_age_hours: int = 24) -> int:
        """Clean up expired sessions."""
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        expired_count = 0
        
        for session_id, session in list(self.active_sessions.items()):
            if session["last_activity"] < cutoff_time:
                self.end_session(session_id)
                expired_count += 1
        
        self.logger.info("Expired sessions cleaned up", count=expired_count)
        return expired_count
