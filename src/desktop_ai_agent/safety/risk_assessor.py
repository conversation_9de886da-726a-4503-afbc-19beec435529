"""
Risk assessment for Desktop AI Agent.

This module provides risk assessment capabilities for operations and resources.
"""

from typing import Any, Dict, List

import structlog

from desktop_ai_agent.safety.models import RiskAssessment, RiskLevel


class RiskAssessor:
    """Risk assessment system."""
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        
        # Risk scoring rules
        self.risk_rules = {
            "high_risk_operations": {
                "operations": ["delete_file", "execute_script", "install_software"],
                "score": 0.4
            },
            "system_resources": {
                "patterns": ["/system", "C:\\Windows", "/usr", "/etc"],
                "score": 0.3
            },
            "executable_files": {
                "extensions": [".exe", ".bat", ".sh", ".ps1"],
                "score": 0.2
            }
        }
        
        self.logger.info("Risk assessor initialized")
    
    def calculate_risk_score(self, operation: str, resource: str, context: Dict[str, Any]) -> float:
        """Calculate risk score for operation."""
        score = 0.0
        
        # Check operation risk
        if operation in self.risk_rules["high_risk_operations"]["operations"]:
            score += self.risk_rules["high_risk_operations"]["score"]
        
        # Check resource risk
        for pattern in self.risk_rules["system_resources"]["patterns"]:
            if pattern in resource:
                score += self.risk_rules["system_resources"]["score"]
                break
        
        # Check file extension risk
        for ext in self.risk_rules["executable_files"]["extensions"]:
            if resource.endswith(ext):
                score += self.risk_rules["executable_files"]["score"]
                break
        
        return min(1.0, score)
    
    def determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine risk level from score."""
        if risk_score >= 0.8:
            return RiskLevel.CRITICAL
        elif risk_score >= 0.6:
            return RiskLevel.HIGH
        elif risk_score >= 0.3:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
