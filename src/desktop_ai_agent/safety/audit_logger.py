"""
Audit logging for Desktop AI Agent.

This module provides comprehensive audit logging capabilities.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog

from desktop_ai_agent.safety.models import AuditEvent, AuditEventType


class AuditLogger:
    """Audit logging system."""
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        self.events: List[AuditEvent] = []
        
        self.logger.info("Audit logger initialized")
    
    async def log_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[str] = None,
        operation: Optional[str] = None,
        resource: Optional[str] = None,
        success: bool = True,
        **kwargs
    ) -> AuditEvent:
        """Log an audit event."""
        event = AuditEvent(
            event_type=event_type,
            user_id=user_id,
            operation=operation,
            resource=resource,
            success=success,
            **kwargs
        )
        
        self.events.append(event)
        
        # Keep only recent events in memory
        if len(self.events) > 1000:
            self.events = self.events[-500:]
        
        self.logger.info(
            "Audit event logged",
            event_id=event.event_id,
            event_type=event_type,
            user_id=user_id,
            operation=operation
        )
        
        return event
    
    def get_events(
        self,
        limit: int = 100,
        event_type: Optional[AuditEventType] = None,
        user_id: Optional[str] = None
    ) -> List[AuditEvent]:
        """Get audit events with filtering."""
        events = self.events
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        if user_id:
            events = [e for e in events if e.user_id == user_id]
        
        return sorted(events, key=lambda e: e.timestamp, reverse=True)[:limit]
