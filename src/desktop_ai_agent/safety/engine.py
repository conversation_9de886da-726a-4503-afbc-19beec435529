"""
Safety and Sandboxing Layer (SSL) Engine for Desktop AI Agent.

This module provides the main interface for security, permission management,
risk assessment, and audit logging.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import structlog

from desktop_ai_agent.core.config import Settings
from desktop_ai_agent.core.orchestrator import Component, ComponentStatus
from desktop_ai_agent.safety.models import (
    AuditEvent,
    AuditEventType,
    Permission,
    PermissionStatus,
    RiskAssessment,
    RiskLevel,
    SafetyConfiguration,
    SecurityPolicy,
    SecurityViolation,
)


class SafetyEngine:
    """
    Safety and Sandboxing Layer (SSL) Engine - Main interface for security.
    
    Provides comprehensive security, permission management, risk assessment,
    and audit logging capabilities.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        
        # Component registration info
        self.component_info = Component(
            component_id="safety_engine",
            name="Safety and Sandboxing Layer",
            version="0.1.0",
            status=ComponentStatus.HEALTHY,
            capabilities=[
                "permission_management",
                "risk_assessment",
                "audit_logging",
                "policy_enforcement",
                "threat_detection"
            ],
            metadata={
                "safe_mode": settings.desktop.safe_mode,
                "audit_enabled": True
            }
        )
        
        # Safety configuration
        self.safety_config = SafetyConfiguration(
            safe_mode_enabled=settings.desktop.safe_mode,
            require_explicit_permission=True,
            auto_deny_high_risk=True
        )
        
        # Security policies
        self.security_policies: Dict[str, SecurityPolicy] = {}
        self._load_default_policies()
        
        # Active permissions
        self.active_permissions: Dict[str, Permission] = {}
        
        # Audit events storage (in production, this would be a database)
        self.audit_events: List[AuditEvent] = []
        
        # Security violations
        self.security_violations: List[SecurityViolation] = []
        
        # Risk assessments cache
        self.risk_assessments: Dict[str, RiskAssessment] = {}
        
        self.logger.info("Safety Engine initialized")
    
    async def start(self) -> None:
        """Start the safety engine."""
        try:
            self.component_info.status = ComponentStatus.HEALTHY
            self.component_info.last_heartbeat = datetime.utcnow()
            
            # Start background tasks
            asyncio.create_task(self._cleanup_expired_permissions())
            asyncio.create_task(self._monitor_security_violations())
            
            self.logger.info("Safety Engine started")
        except Exception as e:
            self.component_info.status = ComponentStatus.UNHEALTHY
            self.logger.error("Failed to start Safety Engine", error=str(e))
            raise
    
    async def stop(self) -> None:
        """Stop the safety engine."""
        try:
            self.component_info.status = ComponentStatus.OFFLINE
            self.logger.info("Safety Engine stopped")
        except Exception as e:
            self.logger.error("Failed to stop Safety Engine", error=str(e))
    
    def get_component_info(self) -> Component:
        """Get component information for registration."""
        self.component_info.last_heartbeat = datetime.utcnow()
        return self.component_info
    
    # Permission Management
    
    async def request_permission(
        self,
        operation: str,
        resource: str,
        user_id: Optional[str] = None,
        justification: Optional[str] = None,
        duration_seconds: Optional[int] = None
    ) -> Permission:
        """Request permission for an operation."""
        try:
            # Perform risk assessment
            risk_assessment = await self.assess_risk(operation, resource, {"user_id": user_id})
            
            # Create permission request
            permission = Permission(
                user_id=user_id,
                operation=operation,
                resource=resource,
                justification=justification,
                duration_seconds=duration_seconds or self.safety_config.default_permission_duration,
                risk_level=risk_assessment.risk_level,
                risk_factors=[rf["factor"] for rf in risk_assessment.risk_factors]
            )
            
            # Auto-approve low risk operations if safe mode allows
            if (not self.safety_config.safe_mode_enabled and 
                risk_assessment.risk_level == RiskLevel.LOW):
                permission.status = PermissionStatus.GRANTED
                permission.granted_at = datetime.utcnow()
                permission.granted_by = "system"
                permission.expires_at = datetime.utcnow() + timedelta(seconds=permission.duration_seconds)
                
                self.active_permissions[permission.permission_id] = permission
            
            # Auto-deny critical risk operations
            elif (self.safety_config.auto_deny_high_risk and 
                  risk_assessment.risk_level == RiskLevel.CRITICAL):
                permission.status = PermissionStatus.DENIED
                permission.denied_at = datetime.utcnow()
                permission.denied_by = "system"
                permission.denial_reason = "Critical risk level - auto-denied"
            
            # Log audit event
            await self._log_audit_event(
                AuditEventType.PERMISSION_REQUEST,
                user_id=user_id,
                operation=operation,
                resource=resource,
                permission_id=permission.permission_id,
                risk_level=risk_assessment.risk_level,
                details={"justification": justification}
            )
            
            self.logger.info(
                "Permission requested",
                permission_id=permission.permission_id,
                operation=operation,
                resource=resource,
                status=permission.status,
                risk_level=risk_assessment.risk_level
            )
            
            return permission
            
        except Exception as e:
            self.logger.error("Permission request failed", operation=operation, resource=resource, error=str(e))
            raise
    
    async def grant_permission(self, permission_id: str, granted_by: str) -> bool:
        """Grant a pending permission."""
        try:
            permission = self.active_permissions.get(permission_id)
            if not permission or permission.status != PermissionStatus.PENDING:
                return False
            
            permission.status = PermissionStatus.GRANTED
            permission.granted_at = datetime.utcnow()
            permission.granted_by = granted_by
            permission.expires_at = datetime.utcnow() + timedelta(seconds=permission.duration_seconds)
            
            # Log audit event
            await self._log_audit_event(
                AuditEventType.PERMISSION_GRANT,
                user_id=permission.user_id,
                operation=permission.operation,
                resource=permission.resource,
                permission_id=permission_id,
                details={"granted_by": granted_by}
            )
            
            self.logger.info("Permission granted", permission_id=permission_id, granted_by=granted_by)
            return True
            
        except Exception as e:
            self.logger.error("Grant permission failed", permission_id=permission_id, error=str(e))
            return False
    
    async def deny_permission(self, permission_id: str, denied_by: str, reason: str) -> bool:
        """Deny a pending permission."""
        try:
            permission = self.active_permissions.get(permission_id)
            if not permission or permission.status != PermissionStatus.PENDING:
                return False
            
            permission.status = PermissionStatus.DENIED
            permission.denied_at = datetime.utcnow()
            permission.denied_by = denied_by
            permission.denial_reason = reason
            
            # Log audit event
            await self._log_audit_event(
                AuditEventType.PERMISSION_DENY,
                user_id=permission.user_id,
                operation=permission.operation,
                resource=permission.resource,
                permission_id=permission_id,
                details={"denied_by": denied_by, "reason": reason}
            )
            
            self.logger.info("Permission denied", permission_id=permission_id, denied_by=denied_by, reason=reason)
            return True
            
        except Exception as e:
            self.logger.error("Deny permission failed", permission_id=permission_id, error=str(e))
            return False
    
    async def check_permission(self, operation: str, resource: str, user_id: Optional[str] = None) -> bool:
        """Check if operation is permitted."""
        try:
            # Find active permission
            for permission in self.active_permissions.values():
                if (permission.operation == operation and 
                    permission.resource == resource and
                    permission.user_id == user_id and
                    permission.status == PermissionStatus.GRANTED and
                    (not permission.expires_at or permission.expires_at > datetime.utcnow())):
                    
                    # Update usage count
                    permission.usage_count += 1
                    
                    # Check usage limits
                    if (permission.max_usage_count and 
                        permission.usage_count > permission.max_usage_count):
                        permission.status = PermissionStatus.EXPIRED
                        return False
                    
                    return True
            
            # No valid permission found
            return False
            
        except Exception as e:
            self.logger.error("Check permission failed", operation=operation, resource=resource, error=str(e))
            return False
    
    # Risk Assessment
    
    async def assess_risk(self, operation: str, resource: str, context: Dict[str, Any]) -> RiskAssessment:
        """Assess risk for an operation."""
        try:
            # Create risk assessment
            assessment = RiskAssessment(
                operation=operation,
                resource=resource,
                context=context,
                user_id=context.get("user_id")
            )
            
            # Calculate risk score using simple rule-based approach
            risk_score = 0.0
            risk_factors = []
            
            # High-risk operations
            high_risk_operations = [
                "delete_file", "format_drive", "install_software", 
                "modify_registry", "execute_script", "network_access"
            ]
            
            if operation in high_risk_operations:
                risk_score += 0.4
                risk_factors.append({"factor": "high_risk_operation", "weight": 0.4})
            
            # System resource access
            system_paths = ["/system", "C:\\Windows", "/usr", "/etc"]
            if any(path in resource for path in system_paths):
                risk_score += 0.3
                risk_factors.append({"factor": "system_resource_access", "weight": 0.3})
            
            # File extension risks
            risky_extensions = [".exe", ".bat", ".sh", ".ps1", ".scr"]
            if any(resource.endswith(ext) for ext in risky_extensions):
                risk_score += 0.2
                risk_factors.append({"factor": "risky_file_extension", "weight": 0.2})
            
            # Network operations
            if "network" in operation.lower() or "http" in resource.lower():
                risk_score += 0.2
                risk_factors.append({"factor": "network_operation", "weight": 0.2})
            
            # Determine risk level
            if risk_score >= 0.8:
                assessment.risk_level = RiskLevel.CRITICAL
            elif risk_score >= 0.6:
                assessment.risk_level = RiskLevel.HIGH
            elif risk_score >= 0.3:
                assessment.risk_level = RiskLevel.MEDIUM
            else:
                assessment.risk_level = RiskLevel.LOW
            
            assessment.risk_score = min(1.0, risk_score)
            assessment.confidence = 0.8  # Rule-based confidence
            assessment.risk_factors = risk_factors
            
            # Add mitigation suggestions
            if assessment.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                assessment.mitigation_suggestions = [
                    "Require explicit user confirmation",
                    "Execute in sandboxed environment",
                    "Create backup before operation",
                    "Monitor operation closely"
                ]
            
            # Cache assessment
            self.risk_assessments[f"{operation}:{resource}"] = assessment
            
            self.logger.debug(
                "Risk assessment completed",
                operation=operation,
                resource=resource,
                risk_level=assessment.risk_level,
                risk_score=assessment.risk_score
            )
            
            return assessment
            
        except Exception as e:
            self.logger.error("Risk assessment failed", operation=operation, resource=resource, error=str(e))
            # Return safe default
            return RiskAssessment(
                operation=operation,
                resource=resource,
                context=context,
                risk_level=RiskLevel.HIGH,
                risk_score=0.8
            )
    
    # Audit Logging
    
    async def _log_audit_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[str] = None,
        operation: Optional[str] = None,
        resource: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        **kwargs
    ) -> AuditEvent:
        """Log an audit event."""
        try:
            event = AuditEvent(
                event_type=event_type,
                user_id=user_id,
                operation=operation,
                resource=resource,
                success=success,
                error_message=error_message,
                **kwargs
            )
            
            # Store event (in production, this would go to a database)
            self.audit_events.append(event)
            
            # Keep only recent events in memory
            if len(self.audit_events) > 10000:
                self.audit_events = self.audit_events[-5000:]
            
            self.logger.debug("Audit event logged", event_id=event.event_id, event_type=event_type)
            return event
            
        except Exception as e:
            self.logger.error("Audit logging failed", event_type=event_type, error=str(e))
            raise
    
    # Background Tasks
    
    async def _cleanup_expired_permissions(self) -> None:
        """Background task to clean up expired permissions."""
        while True:
            try:
                current_time = datetime.utcnow()
                expired_permissions = []
                
                for permission_id, permission in self.active_permissions.items():
                    if (permission.expires_at and permission.expires_at < current_time):
                        permission.status = PermissionStatus.EXPIRED
                        expired_permissions.append(permission_id)
                
                for permission_id in expired_permissions:
                    self.active_permissions.pop(permission_id, None)
                
                if expired_permissions:
                    self.logger.info("Cleaned up expired permissions", count=len(expired_permissions))
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error("Permission cleanup error", error=str(e))
                await asyncio.sleep(60)
    
    async def _monitor_security_violations(self) -> None:
        """Background task to monitor for security violations."""
        while True:
            try:
                # Simple violation detection based on audit events
                recent_events = [e for e in self.audit_events if 
                               (datetime.utcnow() - e.timestamp).total_seconds() < 300]  # Last 5 minutes
                
                # Detect multiple failed permission requests
                failed_requests = [e for e in recent_events if 
                                 e.event_type == AuditEventType.PERMISSION_DENY]
                
                if len(failed_requests) > 5:  # More than 5 denials in 5 minutes
                    violation = SecurityViolation(
                        violation_type="excessive_permission_denials",
                        severity=RiskLevel.MEDIUM,
                        description=f"Detected {len(failed_requests)} permission denials in 5 minutes",
                        detection_method="audit_analysis"
                    )
                    self.security_violations.append(violation)
                    
                    self.logger.warning(
                        "Security violation detected",
                        violation_id=violation.violation_id,
                        type=violation.violation_type
                    )
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error("Security monitoring error", error=str(e))
                await asyncio.sleep(300)
    
    def _load_default_policies(self) -> None:
        """Load default security policies."""
        default_policy = SecurityPolicy(
            name="default_policy",
            description="Default security policy for Desktop AI Agent",
            allowed_operations=[
                "screenshot", "click", "type_text", "window_focus",
                "window_minimize", "window_maximize", "read_file"
            ],
            denied_operations=[
                "delete_system_file", "format_drive", "install_software",
                "modify_registry", "execute_arbitrary_code"
            ],
            restricted_paths=[
                "/system", "C:\\Windows\\System32", "/usr/bin", "/etc"
            ],
            max_risk_level=RiskLevel.MEDIUM,
            require_confirmation=["delete_file", "execute_script"]
        )
        
        self.security_policies[default_policy.policy_id] = default_policy
        self.logger.info("Default security policies loaded")
    
    # Public API Methods
    
    def get_audit_events(self, limit: int = 100, event_type: Optional[AuditEventType] = None) -> List[AuditEvent]:
        """Get recent audit events."""
        events = self.audit_events
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        return sorted(events, key=lambda e: e.timestamp, reverse=True)[:limit]
    
    def get_active_permissions(self, user_id: Optional[str] = None) -> List[Permission]:
        """Get active permissions."""
        permissions = list(self.active_permissions.values())
        if user_id:
            permissions = [p for p in permissions if p.user_id == user_id]
        return permissions
    
    def get_security_violations(self, limit: int = 50) -> List[SecurityViolation]:
        """Get recent security violations."""
        return sorted(self.security_violations, key=lambda v: v.detected_at, reverse=True)[:limit]
