"""
Data models for safety and security components.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class RiskLevel(str, Enum):
    """Risk level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class PermissionStatus(str, Enum):
    """Permission status enumeration."""
    GRANTED = "granted"
    DENIED = "denied"
    PENDING = "pending"
    EXPIRED = "expired"
    REVOKED = "revoked"


class AuditEventType(str, Enum):
    """Audit event type enumeration."""
    PERMISSION_REQUEST = "permission_request"
    PERMISSION_GRANT = "permission_grant"
    PERMISSION_DENY = "permission_deny"
    TASK_EXECUTION = "task_execution"
    SECURITY_VIOLATION = "security_violation"
    SYSTEM_ACCESS = "system_access"
    DATA_ACCESS = "data_access"
    ERROR_EVENT = "error_event"


class SecurityPolicy(BaseModel):
    """Security policy model."""
    policy_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    version: str = "1.0"
    
    # Policy rules
    allowed_operations: List[str] = Field(default_factory=list)
    denied_operations: List[str] = Field(default_factory=list)
    restricted_paths: List[str] = Field(default_factory=list)
    allowed_applications: List[str] = Field(default_factory=list)
    
    # Risk thresholds
    max_risk_level: RiskLevel = RiskLevel.MEDIUM
    require_confirmation: List[str] = Field(default_factory=list)
    
    # Time constraints
    max_session_duration: Optional[int] = None  # seconds
    allowed_time_windows: List[Dict[str, str]] = Field(default_factory=list)
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[str] = None
    is_active: bool = True


class Permission(BaseModel):
    """Permission model."""
    permission_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    operation: str
    resource: str
    status: PermissionStatus = PermissionStatus.PENDING
    
    # Request details
    justification: Optional[str] = None
    requested_at: datetime = Field(default_factory=datetime.utcnow)
    requested_by: Optional[str] = None
    
    # Grant/deny details
    granted_at: Optional[datetime] = None
    granted_by: Optional[str] = None
    denied_at: Optional[datetime] = None
    denied_by: Optional[str] = None
    denial_reason: Optional[str] = None
    
    # Expiration
    expires_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    
    # Conditions and constraints
    conditions: List[str] = Field(default_factory=list)
    usage_count: int = 0
    max_usage_count: Optional[int] = None
    
    # Risk assessment
    risk_level: RiskLevel = RiskLevel.LOW
    risk_factors: List[str] = Field(default_factory=list)


class RiskAssessment(BaseModel):
    """Risk assessment model."""
    assessment_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    operation: str
    resource: str
    context: Dict[str, Any] = Field(default_factory=dict)
    
    # Risk evaluation
    risk_level: RiskLevel = RiskLevel.LOW
    risk_score: float = 0.0  # 0.0 to 1.0
    confidence: float = 0.0  # 0.0 to 1.0
    
    # Risk factors
    risk_factors: List[Dict[str, Any]] = Field(default_factory=list)
    mitigation_suggestions: List[str] = Field(default_factory=list)
    
    # Assessment metadata
    assessed_at: datetime = Field(default_factory=datetime.utcnow)
    assessed_by: str = "system"
    assessment_method: str = "rule_based"
    
    # Additional context
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    previous_assessments: List[str] = Field(default_factory=list)


class AuditEvent(BaseModel):
    """Audit event model."""
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: AuditEventType
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Event details
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    operation: Optional[str] = None
    resource: Optional[str] = None
    
    # Result and status
    success: bool = True
    error_message: Optional[str] = None
    risk_level: Optional[RiskLevel] = None
    
    # Context and metadata
    context: Dict[str, Any] = Field(default_factory=dict)
    source_ip: Optional[str] = None
    user_agent: Optional[str] = None
    
    # Related entities
    permission_id: Optional[str] = None
    task_id: Optional[str] = None
    plan_id: Optional[str] = None
    
    # Additional data
    details: Dict[str, Any] = Field(default_factory=dict)
    tags: List[str] = Field(default_factory=list)


class SecurityViolation(BaseModel):
    """Security violation model."""
    violation_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    violation_type: str
    severity: RiskLevel = RiskLevel.MEDIUM
    
    # Violation details
    description: str
    operation: Optional[str] = None
    resource: Optional[str] = None
    user_id: Optional[str] = None
    
    # Detection
    detected_at: datetime = Field(default_factory=datetime.utcnow)
    detected_by: str = "system"
    detection_method: str
    
    # Response
    action_taken: Optional[str] = None
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    resolved_by: Optional[str] = None
    
    # Context
    context: Dict[str, Any] = Field(default_factory=dict)
    related_events: List[str] = Field(default_factory=list)


class SafetyConfiguration(BaseModel):
    """Safety configuration model."""
    config_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = "default"
    
    # Global safety settings
    safe_mode_enabled: bool = True
    require_explicit_permission: bool = True
    auto_deny_high_risk: bool = True
    
    # Risk thresholds
    low_risk_threshold: float = 0.3
    medium_risk_threshold: float = 0.6
    high_risk_threshold: float = 0.8
    
    # Permission settings
    default_permission_duration: int = 3600  # 1 hour
    max_permission_duration: int = 86400     # 24 hours
    require_justification: bool = True
    
    # Audit settings
    audit_all_operations: bool = True
    audit_retention_days: int = 90
    enable_real_time_monitoring: bool = True
    
    # Sandbox settings
    enable_sandboxing: bool = True
    sandbox_timeout: int = 300  # 5 minutes
    
    # Notification settings
    notify_on_high_risk: bool = True
    notify_on_violations: bool = True
    notification_channels: List[str] = Field(default_factory=list)
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True


class SandboxEnvironment(BaseModel):
    """Sandbox environment model."""
    sandbox_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    
    # Environment configuration
    isolation_level: str = "process"  # process, container, vm
    resource_limits: Dict[str, Any] = Field(default_factory=dict)
    network_access: bool = False
    file_system_access: List[str] = Field(default_factory=list)
    
    # Runtime settings
    max_execution_time: int = 300  # 5 minutes
    max_memory_mb: int = 512
    max_cpu_percent: int = 50
    
    # Status
    status: str = "inactive"  # inactive, active, error
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_used: Optional[datetime] = None
    
    # Usage statistics
    execution_count: int = 0
    total_execution_time: float = 0.0
    success_count: int = 0
    failure_count: int = 0


class ThreatIntelligence(BaseModel):
    """Threat intelligence model."""
    threat_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    threat_type: str
    severity: RiskLevel = RiskLevel.MEDIUM
    
    # Threat details
    name: str
    description: str
    indicators: List[str] = Field(default_factory=list)
    
    # Detection patterns
    patterns: List[Dict[str, Any]] = Field(default_factory=list)
    signatures: List[str] = Field(default_factory=list)
    
    # Metadata
    source: str = "internal"
    confidence: float = 0.5
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Response
    recommended_actions: List[str] = Field(default_factory=list)
    mitigation_steps: List[str] = Field(default_factory=list)
    
    # Statistics
    detection_count: int = 0
    false_positive_count: int = 0
