"""
Model discovery and download utility for Desktop AI Agent.

This module provides utilities to discover, download, and set up AI models
suitable for the Desktop AI Agent system.
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import structlog

from desktop_ai_agent.core.config import Settings


class ModelDownloader:
    """Utility for discovering and downloading AI models."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        
        # Model directory
        self.models_dir = Path(settings.data_dir) / "models"
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # Recommended models for Desktop AI Agent
        self.recommended_models = {
            "codellama-7b-instruct": {
                "name": "CodeLlama-7B-Instruct-GGUF",
                "description": "Code-focused language model, excellent for task planning",
                "size_gb": 3.8,
                "url": "https://huggingface.co/TheBloke/CodeLlama-7B-Instruct-GGUF/resolve/main/codellama-7b-instruct.Q4_K_M.gguf",
                "filename": "codellama-7b-instruct.Q4_K_M.gguf",
                "quantization": "Q4_K_M",
                "context_length": 4096
            },
            "mistral-7b-instruct": {
                "name": "Mistral-7B-Instruct-v0.2-GGUF",
                "description": "General-purpose instruction-following model",
                "size_gb": 4.1,
                "url": "https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.2-GGUF/resolve/main/mistral-7b-instruct-v0.2.Q4_K_M.gguf",
                "filename": "mistral-7b-instruct-v0.2.Q4_K_M.gguf",
                "quantization": "Q4_K_M",
                "context_length": 8192
            },
            "llama2-7b-chat": {
                "name": "Llama-2-7B-Chat-GGUF",
                "description": "Conversational AI model with good reasoning",
                "size_gb": 3.6,
                "url": "https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGUF/resolve/main/llama-2-7b-chat.Q4_K_M.gguf",
                "filename": "llama-2-7b-chat.Q4_K_M.gguf",
                "quantization": "Q4_K_M",
                "context_length": 4096
            }
        }
        
        self.logger.info("Model downloader initialized", models_dir=str(self.models_dir))
    
    def discover_existing_models(self) -> List[Dict[str, any]]:
        """Discover existing model files on the system."""
        discovered_models = []
        
        # Search paths for models
        search_paths = [
            self.models_dir,
            Path.home() / ".cache" / "huggingface" / "transformers",
            Path.home() / ".cache" / "huggingface" / "hub",
            Path("/opt/models") if Path("/opt/models").exists() else None,
            Path("/usr/local/share/models") if Path("/usr/local/share/models").exists() else None,
        ]
        
        # Remove None paths
        search_paths = [p for p in search_paths if p is not None]
        
        # Model file extensions
        model_extensions = [".gguf", ".bin", ".safetensors", ".pt", ".pth"]
        
        for search_path in search_paths:
            if not search_path.exists():
                continue
            
            self.logger.debug("Searching for models", path=str(search_path))
            
            try:
                for model_file in search_path.rglob("*"):
                    if model_file.is_file() and any(model_file.name.endswith(ext) for ext in model_extensions):
                        model_info = self._analyze_model_file(model_file)
                        if model_info:
                            discovered_models.append(model_info)
            except Exception as e:
                self.logger.warning("Error searching path", path=str(search_path), error=str(e))
        
        self.logger.info("Model discovery completed", found=len(discovered_models))
        return discovered_models
    
    def _analyze_model_file(self, model_path: Path) -> Optional[Dict[str, any]]:
        """Analyze a model file and extract information."""
        try:
            # Get file size
            size_mb = model_path.stat().st_size / (1024 * 1024)
            
            # Extract information from filename
            filename = model_path.name.lower()
            
            # Determine model type
            model_type = "llm"
            if any(keyword in filename for keyword in ["vision", "vlm", "multimodal"]):
                model_type = "vlm"
            elif any(keyword in filename for keyword in ["embed", "encoder", "retrieval"]):
                model_type = "specialized"
            
            # Extract parameter count
            parameter_count = None
            for size in ["7b", "13b", "30b", "65b", "70b"]:
                if size in filename:
                    parameter_count = size.upper()
                    break
            
            # Extract quantization
            quantization = None
            quant_patterns = ["q4_0", "q4_1", "q5_0", "q5_1", "q8_0", "f16", "f32", "q4_k_m", "q5_k_m"]
            for quant in quant_patterns:
                if quant in filename:
                    quantization = quant.upper()
                    break
            
            # Determine suitability for Desktop AI Agent
            suitable = self._is_suitable_for_desktop_agent(size_mb, parameter_count, model_type)
            
            return {
                "path": str(model_path),
                "filename": model_path.name,
                "size_mb": size_mb,
                "model_type": model_type,
                "parameter_count": parameter_count,
                "quantization": quantization,
                "suitable": suitable,
                "extension": model_path.suffix
            }
            
        except Exception as e:
            self.logger.warning("Failed to analyze model file", path=str(model_path), error=str(e))
            return None
    
    def _is_suitable_for_desktop_agent(self, size_mb: float, parameter_count: Optional[str], model_type: str) -> bool:
        """Determine if a model is suitable for Desktop AI Agent."""
        # Size constraints (prefer models under 8GB)
        if size_mb > 8000:  # 8GB
            return False
        
        # Type constraints (prefer LLMs)
        if model_type != "llm":
            return False
        
        # Parameter count constraints (prefer 7B-13B)
        if parameter_count and parameter_count in ["7B", "13B"]:
            return True
        
        # If no parameter count detected, use size heuristic
        if not parameter_count:
            # Rough heuristic: 7B models are typically 3-5GB when quantized
            return 2000 <= size_mb <= 6000
        
        return False
    
    def get_recommended_model(self) -> Optional[str]:
        """Get the best recommended model for the current system."""
        # Check available memory
        try:
            import psutil
            available_memory_gb = psutil.virtual_memory().available / (1024**3)
        except:
            available_memory_gb = 8  # Conservative default
        
        # Choose model based on available memory
        if available_memory_gb >= 8:
            return "codellama-7b-instruct"  # Best for task planning
        elif available_memory_gb >= 6:
            return "mistral-7b-instruct"    # Good general model
        else:
            return "llama2-7b-chat"         # Smallest option
    
    def download_model(self, model_key: str, progress_callback: Optional[callable] = None) -> bool:
        """Download a recommended model."""
        if model_key not in self.recommended_models:
            self.logger.error("Unknown model key", model_key=model_key)
            return False
        
        model_info = self.recommended_models[model_key]
        model_path = self.models_dir / model_info["filename"]
        
        # Check if model already exists
        if model_path.exists():
            self.logger.info("Model already exists", path=str(model_path))
            return True
        
        self.logger.info("Downloading model", name=model_info["name"], size_gb=model_info["size_gb"])
        
        try:
            # Use wget or curl to download
            if self._command_exists("wget"):
                cmd = ["wget", "-O", str(model_path), model_info["url"]]
            elif self._command_exists("curl"):
                cmd = ["curl", "-L", "-o", str(model_path), model_info["url"]]
            else:
                self.logger.error("Neither wget nor curl available for download")
                return False
            
            # Execute download
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Monitor progress if callback provided
            if progress_callback:
                # Simple progress monitoring (in production, would parse wget/curl output)
                while process.poll() is None:
                    progress_callback("Downloading...")
            
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                self.logger.info("Model downloaded successfully", path=str(model_path))
                return True
            else:
                self.logger.error("Model download failed", error=stderr)
                # Clean up partial download
                if model_path.exists():
                    model_path.unlink()
                return False
                
        except Exception as e:
            self.logger.error("Model download error", error=str(e))
            return False
    
    def _command_exists(self, command: str) -> bool:
        """Check if a command exists in PATH."""
        try:
            subprocess.run([command, "--version"], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def setup_default_model(self) -> Optional[str]:
        """Set up a default model for the Desktop AI Agent."""
        self.logger.info("Setting up default model for Desktop AI Agent")
        
        # First, check for existing suitable models
        existing_models = self.discover_existing_models()
        suitable_models = [m for m in existing_models if m["suitable"]]
        
        if suitable_models:
            # Use the first suitable model found
            best_model = suitable_models[0]
            self.logger.info("Found suitable existing model", path=best_model["path"])
            
            # Update configuration to use this model
            self.settings.ai_model.model_path = best_model["path"]
            return best_model["path"]
        
        # No suitable models found, recommend download
        recommended_key = self.get_recommended_model()
        if recommended_key:
            recommended_model = self.recommended_models[recommended_key]
            
            self.logger.info(
                "No suitable models found. Recommended model for download",
                name=recommended_model["name"],
                size_gb=recommended_model["size_gb"],
                description=recommended_model["description"]
            )
            
            # Return the recommended model info for user decision
            return recommended_key
        
        return None
    
    def list_recommended_models(self) -> Dict[str, Dict[str, any]]:
        """Get list of recommended models."""
        return self.recommended_models.copy()


def setup_model_for_agent(settings: Settings) -> Tuple[bool, Optional[str]]:
    """Set up an AI model for the Desktop AI Agent."""
    downloader = ModelDownloader(settings)
    
    # Try to find and set up a model
    result = downloader.setup_default_model()
    
    if result and result.startswith("/"):
        # Found an existing model
        return True, result
    elif result:
        # Found a recommended model to download
        return False, result
    else:
        # No suitable models found
        return False, None
