"""
Learning Engine for the Desktop AI Agent.

This module implements autonomous learning, performance analysis,
and continuous self-improvement capabilities.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import uuid4

import structlog

from ..core.config import Settings
from ..ai.models import InferenceRequest
from .models import (
    LearningSession, LearningInsight, ImprovementProposal, CapabilityDefinition,
    PerformanceMetric, UserFeedbackPattern, KnowledgeBase, AutomationScript,
    LearningType, ImprovementStatus, LearningConfidence
)


class LearningEngine:
    """Autonomous learning and self-improvement system."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        
        # Core components (will be injected)
        self.ai_engine = None
        self.storage_engine = None
        self.conversation_engine = None
        
        # Learning state
        self.knowledge_base = KnowledgeBase()
        self.active_learning_session: Optional[LearningSession] = None
        self.learning_enabled = True
        
        # Performance tracking
        self.performance_metrics: List[PerformanceMetric] = []
        self.user_feedback_patterns: List[UserFeedbackPattern] = []
        
        # Improvement tracking
        self.pending_proposals: List[ImprovementProposal] = []
        self.implemented_improvements: List[ImprovementProposal] = []
        
        # Capabilities registry
        self.capabilities: Dict[str, CapabilityDefinition] = {}
        
        # Learning configuration
        self.learning_interval = timedelta(hours=1)  # How often to run learning
        self.min_data_points = 10  # Minimum data points for learning
        self.confidence_threshold = 0.7  # Minimum confidence for proposals
        
        self.logger.info("Learning Engine initialized")
    
    def set_dependencies(self, ai_engine, storage_engine, conversation_engine):
        """Set dependencies for the learning engine."""
        self.ai_engine = ai_engine
        self.storage_engine = storage_engine
        self.conversation_engine = conversation_engine
        self.logger.info("Learning Engine dependencies set")

    async def analyze_performance(self) -> Dict[str, Any]:
        """Public method to analyze current system performance."""
        try:
            current_time = time.time()

            # Analyze task execution metrics
            task_metrics = await self._analyze_task_metrics()

            # Analyze AI model performance
            model_metrics = await self._analyze_model_performance()

            # Analyze user interaction patterns
            interaction_metrics = await self._analyze_user_interactions()

            # Generate improvement suggestions
            suggestions = await self._generate_improvement_suggestions(
                task_metrics, model_metrics, interaction_metrics
            )

            analysis = {
                "timestamp": current_time,
                "task_metrics": task_metrics,
                "model_metrics": model_metrics,
                "interaction_metrics": interaction_metrics,
                "improvement_suggestions": suggestions
            }

            self.logger.info("Performance analysis completed",
                           task_success_rate=task_metrics.get('success_rate', 0),
                           avg_response_time=model_metrics.get('avg_response_time', 0))
            return analysis

        except Exception as e:
            self.logger.error("Performance analysis failed", error=str(e))
            return {}

    async def learn_from_interactions(self) -> Dict[str, Any]:
        """Learn from recent user interactions."""
        try:
            # Get recent interactions
            recent_interactions = await self._get_recent_interactions()

            if not recent_interactions:
                return {"learned_patterns": 0}

            # Analyze interaction patterns
            patterns = await self._analyze_interaction_patterns(recent_interactions)

            # Update user preferences
            preferences_updated = await self._update_user_preferences(patterns)

            # Improve task execution based on feedback
            execution_improvements = await self._improve_task_execution(patterns)

            result = {
                "interactions_analyzed": len(recent_interactions),
                "patterns_found": len(patterns),
                "preferences_updated": preferences_updated,
                "execution_improvements": execution_improvements,
                "timestamp": time.time()
            }

            self.logger.info("Interaction learning completed", result=result)
            return result

        except Exception as e:
            self.logger.error("Interaction learning failed", error=str(e))
            return {"error": str(e)}

    async def generate_improvement_proposals(self) -> List[Dict[str, Any]]:
        """Generate concrete improvement proposals for the system."""
        try:
            proposals = []

            # Analyze current performance
            performance_analysis = await self.analyze_performance()

            # Generate proposals based on analysis
            if performance_analysis.get('task_metrics', {}).get('success_rate', 1.0) < 0.9:
                proposals.append({
                    "type": "task_execution_improvement",
                    "title": "Improve Task Success Rate",
                    "description": "Task success rate is below 90%. Consider improving error handling and validation.",
                    "priority": "high",
                    "estimated_impact": "high",
                    "implementation_effort": "medium"
                })

            if performance_analysis.get('model_metrics', {}).get('avg_response_time', 0) > 10:
                proposals.append({
                    "type": "performance_optimization",
                    "title": "Optimize AI Response Time",
                    "description": "AI response time is above 10 seconds. Consider model optimization or caching.",
                    "priority": "medium",
                    "estimated_impact": "high",
                    "implementation_effort": "high"
                })

            # Store proposals for review
            await self._store_improvement_proposals(proposals)

            self.logger.info("Generated improvement proposals", count=len(proposals))
            return proposals

        except Exception as e:
            self.logger.error("Proposal generation failed", error=str(e))
            return []
    
    async def start_learning_session(
        self, 
        learning_types: List[LearningType] = None,
        time_range_hours: int = 24
    ) -> LearningSession:
        """Start a new learning session."""
        if self.active_learning_session:
            await self.end_learning_session()
        
        if learning_types is None:
            learning_types = [
                LearningType.PERFORMANCE_ANALYSIS,
                LearningType.USER_PREFERENCE,
                LearningType.PATTERN_RECOGNITION
            ]
        
        session = LearningSession(
            learning_types=learning_types,
            time_range_start=datetime.now() - timedelta(hours=time_range_hours),
            time_range_end=datetime.now()
        )
        
        self.active_learning_session = session
        
        self.logger.info("Learning session started", 
                        session_id=session.session_id,
                        learning_types=[lt.value for lt in learning_types])
        
        # Start learning process
        await self._run_learning_analysis(session)
        
        return session
    
    async def _run_learning_analysis(self, session: LearningSession):
        """Run the main learning analysis process."""
        try:
            insights = []
            
            # 1. Performance Analysis
            if LearningType.PERFORMANCE_ANALYSIS in session.learning_types:
                performance_insights = await self._analyze_performance(session)
                insights.extend(performance_insights)
            
            # 2. User Preference Learning
            if LearningType.USER_PREFERENCE in session.learning_types:
                preference_insights = await self._analyze_user_preferences(session)
                insights.extend(preference_insights)
            
            # 3. Pattern Recognition
            if LearningType.PATTERN_RECOGNITION in session.learning_types:
                pattern_insights = await self._recognize_patterns(session)
                insights.extend(pattern_insights)
            
            # 4. Capability Expansion
            if LearningType.CAPABILITY_EXPANSION in session.learning_types:
                capability_insights = await self._analyze_capability_gaps(session)
                insights.extend(capability_insights)
            
            # 5. Generate improvement proposals
            proposals = await self._generate_improvement_proposals(insights)
            
            # Update session results
            session.insights_discovered = [i.insight_id for i in insights]
            session.proposals_generated = [p.proposal_id for p in proposals]
            session.data_points_analyzed = len(self.performance_metrics)
            
            # Store insights and proposals
            for insight in insights:
                await self._store_insight(insight)
            
            for proposal in proposals:
                await self._store_proposal(proposal)
                if proposal.requires_user_approval:
                    self.pending_proposals.append(proposal)
            
            self.logger.info("Learning analysis completed",
                           insights_count=len(insights),
                           proposals_count=len(proposals))
            
        except Exception as e:
            session.success = False
            session.error_messages.append(str(e))
            self.logger.error("Learning analysis failed", error=str(e))
    
    async def _analyze_performance(self, session: LearningSession) -> List[LearningInsight]:
        """Analyze performance metrics to identify improvement opportunities."""
        insights = []
        
        # Get recent performance data
        recent_metrics = [
            m for m in self.performance_metrics 
            if session.time_range_start <= m.timestamp <= session.time_range_end
        ]
        
        if len(recent_metrics) < self.min_data_points:
            return insights
        
        # Group metrics by task type
        task_metrics = {}
        for metric in recent_metrics:
            if metric.task_type not in task_metrics:
                task_metrics[metric.task_type] = []
            task_metrics[metric.task_type].append(metric)
        
        # Analyze each task type
        for task_type, metrics in task_metrics.items():
            # Calculate performance statistics
            execution_times = [m.value for m in metrics if m.metric_name == "execution_time"]
            success_rates = [m.value for m in metrics if m.metric_name == "success_rate"]
            
            if execution_times:
                avg_time = sum(execution_times) / len(execution_times)
                
                # Check if performance is degrading
                if len(execution_times) >= 5:
                    recent_avg = sum(execution_times[-3:]) / 3
                    older_avg = sum(execution_times[:3]) / 3
                    
                    if recent_avg > older_avg * 1.2:  # 20% slower
                        insight = LearningInsight(
                            learning_type=LearningType.PERFORMANCE_ANALYSIS,
                            title=f"Performance degradation in {task_type}",
                            description=f"Execution time for {task_type} tasks has increased by {((recent_avg/older_avg - 1) * 100):.1f}%",
                            confidence=LearningConfidence.HIGH,
                            potential_impact="medium",
                            affected_areas=[task_type],
                            suggested_actions=[
                                "Optimize task execution algorithms",
                                "Check for resource constraints",
                                "Review recent code changes"
                            ]
                        )
                        insights.append(insight)
        
        return insights
    
    async def _analyze_user_preferences(self, session: LearningSession) -> List[LearningInsight]:
        """Analyze user feedback and behavior to learn preferences."""
        insights = []
        
        # Analyze conversation patterns
        if self.conversation_engine:
            # Get recent conversations
            conversations = await self._get_recent_conversations(session.time_range_start)
            
            # Analyze language preferences
            language_usage = {}
            communication_styles = {}
            
            for conv in conversations:
                for message in conv.messages:
                    if message.language:
                        lang = message.language.value
                        language_usage[lang] = language_usage.get(lang, 0) + 1
                
                style = conv.context.communication_style
                communication_styles[style] = communication_styles.get(style, 0) + 1
            
            # Generate insights about preferences
            if language_usage:
                preferred_lang = max(language_usage, key=language_usage.get)
                if language_usage[preferred_lang] > sum(language_usage.values()) * 0.7:
                    insight = LearningInsight(
                        learning_type=LearningType.USER_PREFERENCE,
                        title=f"Strong preference for {preferred_lang} language",
                        description=f"User uses {preferred_lang} in {(language_usage[preferred_lang]/sum(language_usage.values())*100):.1f}% of interactions",
                        confidence=LearningConfidence.HIGH,
                        potential_impact="low",
                        suggested_actions=[
                            f"Default to {preferred_lang} for new conversations",
                            "Optimize language detection for this preference"
                        ]
                    )
                    insights.append(insight)
        
        return insights
    
    async def _recognize_patterns(self, session: LearningSession) -> List[LearningInsight]:
        """Recognize patterns in user behavior and task execution."""
        insights = []
        
        # Analyze task patterns
        task_sequences = await self._analyze_task_sequences(session)
        
        for sequence, frequency in task_sequences.items():
            if frequency >= 3:  # Pattern appears at least 3 times
                insight = LearningInsight(
                    learning_type=LearningType.PATTERN_RECOGNITION,
                    title=f"Recurring task sequence: {sequence}",
                    description=f"User frequently performs this sequence of tasks ({frequency} times)",
                    confidence=LearningConfidence.MEDIUM,
                    potential_impact="medium",
                    suggested_actions=[
                        "Create automation script for this sequence",
                        "Offer to automate this pattern",
                        "Optimize individual tasks in the sequence"
                    ]
                )
                insights.append(insight)
        
        return insights
    
    async def _generate_improvement_proposals(
        self, 
        insights: List[LearningInsight]
    ) -> List[ImprovementProposal]:
        """Generate improvement proposals based on insights."""
        proposals = []
        
        for insight in insights:
            if insight.confidence in [LearningConfidence.HIGH, LearningConfidence.VERY_HIGH]:
                # Use AI to generate detailed improvement proposal
                proposal_content = await self._ai_generate_proposal(insight)
                
                if proposal_content:
                    proposal = ImprovementProposal(
                        insight_id=insight.insight_id,
                        title=proposal_content.get("title", insight.title),
                        description=proposal_content.get("description", insight.description),
                        rationale=proposal_content.get("rationale", ""),
                        implementation_steps=proposal_content.get("steps", []),
                        risks=proposal_content.get("risks", []),
                        estimated_timeline=proposal_content.get("timeline", 1)
                    )
                    proposals.append(proposal)
        
        return proposals
    
    async def _ai_generate_proposal(self, insight: LearningInsight) -> Optional[Dict[str, Any]]:
        """Use AI to generate a detailed improvement proposal."""
        if not self.ai_engine:
            return None
        
        prompt = f"""You are a Desktop AI Agent analyzing your own performance. Based on this insight, create a detailed improvement proposal.

Insight: {insight.title}
Description: {insight.description}
Confidence: {insight.confidence.value}
Potential Impact: {insight.potential_impact}
Suggested Actions: {', '.join(insight.suggested_actions)}

Create a JSON response with:
- title: Clear, actionable title
- description: Detailed description of the improvement
- rationale: Why this improvement is needed
- steps: List of implementation steps
- risks: Potential risks or challenges
- timeline: Estimated days to implement

Response:"""
        
        try:
            request = InferenceRequest(
                model_id='local_codellama-7b-instruct.Q4_K_M',
                prompt=prompt,
                max_tokens=400,
                temperature=0.3
            )
            
            response = await self.ai_engine.generate_response(request)
            
            if response.success:
                # Try to parse JSON from response
                try:
                    return json.loads(response.text)
                except json.JSONDecodeError:
                    # Fallback to basic proposal
                    return {
                        "title": insight.title,
                        "description": insight.description,
                        "rationale": "Based on performance analysis",
                        "steps": insight.suggested_actions,
                        "risks": ["Implementation complexity"],
                        "timeline": 1
                    }
        except Exception as e:
            self.logger.error("Failed to generate AI proposal", error=str(e))
        
        return None
    
    async def get_pending_proposals(self) -> List[ImprovementProposal]:
        """Get proposals waiting for user approval."""
        return [p for p in self.pending_proposals if p.status == ImprovementStatus.PROPOSED]
    
    async def approve_proposal(self, proposal_id: str, user_id: str) -> bool:
        """Approve an improvement proposal."""
        for proposal in self.pending_proposals:
            if proposal.proposal_id == proposal_id:
                proposal.status = ImprovementStatus.APPROVED
                proposal.approved_by = user_id
                proposal.approval_timestamp = datetime.now()
                
                # Start implementation
                await self._implement_proposal(proposal)
                
                self.logger.info("Proposal approved", 
                               proposal_id=proposal_id, 
                               approved_by=user_id)
                return True
        
        return False
    
    async def reject_proposal(self, proposal_id: str, reason: str) -> bool:
        """Reject an improvement proposal."""
        for proposal in self.pending_proposals:
            if proposal.proposal_id == proposal_id:
                proposal.status = ImprovementStatus.REJECTED
                proposal.rejection_reason = reason
                
                self.logger.info("Proposal rejected", 
                               proposal_id=proposal_id, 
                               reason=reason)
                return True
        
        return False
    
    async def _implement_proposal(self, proposal: ImprovementProposal):
        """Implement an approved proposal."""
        proposal.status = ImprovementStatus.TESTING
        
        try:
            # Implementation logic would go here
            # This is a placeholder for the actual implementation
            
            proposal.implementation_progress = 1.0
            proposal.status = ImprovementStatus.IMPLEMENTED
            self.implemented_improvements.append(proposal)
            
            self.logger.info("Proposal implemented", 
                           proposal_id=proposal.proposal_id)
            
        except Exception as e:
            proposal.status = ImprovementStatus.ROLLED_BACK
            self.logger.error("Proposal implementation failed", 
                            proposal_id=proposal.proposal_id, 
                            error=str(e))
    
    async def record_performance_metric(self, metric: PerformanceMetric):
        """Record a performance metric for learning."""
        self.performance_metrics.append(metric)
        
        # Keep only recent metrics (last 1000)
        if len(self.performance_metrics) > 1000:
            self.performance_metrics = self.performance_metrics[-1000:]
    
    async def _store_insight(self, insight: LearningInsight):
        """Store an insight in the knowledge base."""
        # Implementation would store in database
        pass
    
    async def _store_proposal(self, proposal: ImprovementProposal):
        """Store a proposal in the database."""
        # Implementation would store in database
        pass

    async def _get_recent_conversations(self, since: datetime) -> List[Any]:
        """Get recent conversations for analysis."""
        if not self.conversation_engine:
            return []

        conversations = []
        for sessions in self.conversation_engine.conversation_history.values():
            for session in sessions:
                if session.start_time >= since:
                    conversations.append(session)

        return conversations

    async def _analyze_task_sequences(self, session: LearningSession) -> Dict[str, int]:
        """Analyze sequences of tasks to identify patterns."""
        sequences = {}

        # This would analyze task execution logs to find common sequences
        # For now, return empty dict as placeholder
        return sequences

    async def end_learning_session(self):
        """End the current learning session."""
        if self.active_learning_session:
            session = self.active_learning_session
            session.end_time = datetime.now()
            session.processing_time = (session.end_time - session.start_time).total_seconds()

            if session.data_points_analyzed > 0:
                session.insights_per_hour = len(session.insights_discovered) / (session.processing_time / 3600)

            self.logger.info("Learning session ended",
                           session_id=session.session_id,
                           insights=len(session.insights_discovered),
                           proposals=len(session.proposals_generated))

            self.active_learning_session = None

    async def get_learning_statistics(self) -> Dict[str, Any]:
        """Get learning system statistics."""
        return {
            "total_metrics": len(self.performance_metrics),
            "pending_proposals": len(await self.get_pending_proposals()),
            "implemented_improvements": len(self.implemented_improvements),
            "learning_enabled": self.learning_enabled,
            "active_session": self.active_learning_session is not None,
            "capabilities_count": len(self.capabilities)
        }

    async def discover_new_software(self) -> List[str]:
        """Discover new software on the system that the agent could learn to interact with."""
        discovered_software = []

        # This would scan the system for installed applications
        # and compare against known capabilities
        # Placeholder implementation

        return discovered_software

    async def create_automation_script(self, task_pattern: str, examples: List[Dict[str, Any]]) -> Optional[AutomationScript]:
        """Create a new automation script based on observed patterns."""
        if not self.ai_engine or len(examples) < 2:
            return None

        # Use AI to generate automation script
        prompt = f"""You are a Desktop AI Agent that learns automation patterns. Based on these examples of the task pattern "{task_pattern}", create a Python automation script.

Examples:
{json.dumps(examples, indent=2)}

Create a Python script that can automate this pattern. Include:
1. Parameter extraction
2. Error handling
3. Safety checks
4. Clear documentation

Script:"""

        try:
            request = InferenceRequest(
                model_id='local_codellama-7b-instruct.Q4_K_M',
                prompt=prompt,
                max_tokens=500,
                temperature=0.3
            )

            response = await self.ai_engine.generate_response(request)

            if response.success:
                script = AutomationScript(
                    name=f"Auto_{task_pattern}",
                    description=f"Learned automation for {task_pattern}",
                    script_type="python",
                    script_content=response.text,
                    learned_from_tasks=[ex.get('task_id', '') for ex in examples],
                    learning_confidence=0.7
                )

                return script

        except Exception as e:
            self.logger.error("Failed to create automation script", error=str(e))

        return None

    async def evaluate_improvement_impact(self, proposal_id: str) -> Dict[str, Any]:
        """Evaluate the potential impact of an improvement proposal."""
        for proposal in self.pending_proposals + self.implemented_improvements:
            if proposal.proposal_id == proposal_id:
                # Analyze potential impact
                impact_analysis = {
                    "performance_gain": "estimated_improvement_percentage",
                    "user_satisfaction": "estimated_satisfaction_increase",
                    "efficiency": "estimated_time_savings",
                    "risk_assessment": proposal.risks,
                    "implementation_effort": proposal.estimated_timeline
                }

                return impact_analysis

        return {}

    async def get_user_feedback_summary(self, user_id: str) -> Dict[str, Any]:
        """Get a summary of user feedback patterns."""
        feedback_summary = {
            "total_feedback_count": 0,
            "average_satisfaction": 0.0,
            "common_complaints": [],
            "common_praise": [],
            "improvement_suggestions": [],
            "preferred_features": []
        }

        # Analyze feedback patterns for the user
        # This would query stored feedback data

        return feedback_summary

    # Helper methods for enhanced learning

    async def _analyze_task_metrics(self) -> Dict[str, Any]:
        """Analyze task execution metrics."""
        try:
            recent_metrics = [m for m in self.performance_metrics if m.timestamp > time.time() - 3600]  # Last hour

            if not recent_metrics:
                return {"success_rate": 1.0, "avg_execution_time": 0, "total_tasks": 0}

            success_count = sum(1 for m in recent_metrics if m.metric_name == "success" and m.value == 1)
            total_tasks = len([m for m in recent_metrics if m.metric_name in ["success", "failure"]])

            execution_times = [m.value for m in recent_metrics if m.metric_name == "execution_time"]
            avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0

            return {
                "success_rate": success_count / total_tasks if total_tasks > 0 else 1.0,
                "avg_execution_time": avg_execution_time,
                "total_tasks": total_tasks
            }
        except Exception as e:
            self.logger.error("Task metrics analysis failed", error=str(e))
            return {"success_rate": 1.0, "avg_execution_time": 0, "total_tasks": 0}

    async def _analyze_model_performance(self) -> Dict[str, Any]:
        """Analyze AI model performance metrics."""
        try:
            if not self.ai_engine:
                return {"avg_response_time": 0, "model_accuracy": 1.0}

            # Get recent AI performance data
            recent_responses = []  # This would come from ai_engine metrics

            return {
                "avg_response_time": 5.0,  # Placeholder - would be calculated from real data
                "model_accuracy": 0.95,   # Placeholder
                "total_requests": len(recent_responses)
            }
        except Exception as e:
            self.logger.error("Model performance analysis failed", error=str(e))
            return {"avg_response_time": 0, "model_accuracy": 1.0}

    async def _analyze_user_interactions(self) -> Dict[str, Any]:
        """Analyze user interaction patterns."""
        try:
            if not self.conversation_engine:
                return {"total_interactions": 0, "satisfaction_score": 1.0}

            # This would analyze conversation patterns, user feedback, etc.
            return {
                "total_interactions": 10,  # Placeholder
                "satisfaction_score": 0.9,  # Placeholder
                "common_requests": ["screenshot", "organize_files"]
            }
        except Exception as e:
            self.logger.error("User interaction analysis failed", error=str(e))
            return {"total_interactions": 0, "satisfaction_score": 1.0}

    async def _generate_improvement_suggestions(self, task_metrics, model_metrics, interaction_metrics) -> List[str]:
        """Generate specific improvement suggestions based on metrics."""
        suggestions = []

        if task_metrics.get('success_rate', 1.0) < 0.9:
            suggestions.append("Improve error handling in task execution")

        if model_metrics.get('avg_response_time', 0) > 10:
            suggestions.append("Optimize AI model inference speed")

        if interaction_metrics.get('satisfaction_score', 1.0) < 0.8:
            suggestions.append("Enhance user experience and interface")

        return suggestions

    async def _get_recent_interactions(self) -> List[Dict[str, Any]]:
        """Get recent user interactions for learning."""
        # This would fetch from conversation_engine or storage
        return []  # Placeholder

    async def _analyze_interaction_patterns(self, interactions) -> List[Dict[str, Any]]:
        """Analyze patterns in user interactions."""
        # This would identify common patterns, preferences, etc.
        return []  # Placeholder

    async def _update_user_preferences(self, patterns) -> int:
        """Update user preferences based on patterns."""
        # This would update stored user preferences
        return len(patterns)  # Placeholder

    async def _improve_task_execution(self, patterns) -> int:
        """Improve task execution based on learned patterns."""
        # This would optimize task execution strategies
        return 0  # Placeholder

    async def _store_improvement_proposals(self, proposals) -> None:
        """Store improvement proposals for review."""
        # This would store proposals in database
        pass
