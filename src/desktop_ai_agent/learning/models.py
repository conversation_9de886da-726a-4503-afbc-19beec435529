"""
Learning and Self-Improvement Models for the Desktop AI Agent.

This module defines the data structures for autonomous learning,
performance analysis, and capability expansion.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, field
from uuid import uuid4


class LearningType(Enum):
    """Types of learning the agent can perform."""
    PERFORMANCE_ANALYSIS = "performance_analysis"
    PATTERN_RECOGNITION = "pattern_recognition"
    USER_PREFERENCE = "user_preference"
    CAPABILITY_EXPANSION = "capability_expansion"
    ERROR_CORRECTION = "error_correction"
    EFFICIENCY_OPTIMIZATION = "efficiency_optimization"


class ImprovementStatus(Enum):
    """Status of improvement proposals."""
    PROPOSED = "proposed"
    APPROVED = "approved"
    REJECTED = "rejected"
    IMPLEMENTED = "implemented"
    TESTING = "testing"
    ROLLED_BACK = "rolled_back"


class LearningConfidence(Enum):
    """Confidence levels for learning insights."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class PerformanceMetric:
    """A performance metric for task execution."""
    metric_id: str = field(default_factory=lambda: str(uuid4()))
    task_id: str = ""
    metric_name: str = ""
    value: float = 0.0
    unit: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Context
    task_type: str = ""
    complexity_level: str = ""
    user_id: str = ""
    
    # Comparison data
    baseline_value: Optional[float] = None
    improvement_percentage: Optional[float] = None
    target_value: Optional[float] = None


@dataclass
class LearningInsight:
    """An insight discovered through learning analysis."""
    insight_id: str = field(default_factory=lambda: str(uuid4()))
    learning_type: LearningType = LearningType.PERFORMANCE_ANALYSIS
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Insight content
    title: str = ""
    description: str = ""
    confidence: LearningConfidence = LearningConfidence.MEDIUM
    
    # Supporting data
    evidence: List[Dict[str, Any]] = field(default_factory=list)
    metrics: List[PerformanceMetric] = field(default_factory=list)
    
    # Impact assessment
    potential_impact: str = ""  # low, medium, high
    affected_areas: List[str] = field(default_factory=list)
    risk_level: str = "low"  # low, medium, high
    
    # Implementation details
    suggested_actions: List[str] = field(default_factory=list)
    implementation_complexity: str = "simple"  # simple, moderate, complex
    estimated_effort: Optional[int] = None  # hours


@dataclass
class ImprovementProposal:
    """A proposed improvement to the agent's capabilities."""
    proposal_id: str = field(default_factory=lambda: str(uuid4()))
    insight_id: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Proposal details
    title: str = ""
    description: str = ""
    rationale: str = ""
    status: ImprovementStatus = ImprovementStatus.PROPOSED
    
    # Implementation plan
    implementation_steps: List[str] = field(default_factory=list)
    required_resources: List[str] = field(default_factory=list)
    estimated_timeline: Optional[int] = None  # days
    
    # Risk assessment
    risks: List[str] = field(default_factory=list)
    mitigation_strategies: List[str] = field(default_factory=list)
    rollback_plan: str = ""
    
    # Approval workflow
    requires_user_approval: bool = True
    approved_by: Optional[str] = None
    approval_timestamp: Optional[datetime] = None
    rejection_reason: Optional[str] = None
    
    # Implementation tracking
    implementation_progress: float = 0.0
    implementation_notes: List[str] = field(default_factory=list)
    test_results: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class CapabilityDefinition:
    """Definition of an agent capability."""
    capability_id: str = field(default_factory=lambda: str(uuid4()))
    name: str = ""
    description: str = ""
    category: str = ""  # desktop_automation, communication, analysis, etc.
    
    # Capability metadata
    version: str = "1.0"
    created_date: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    
    # Implementation details
    implementation_type: str = ""  # built_in, learned, user_defined
    code_references: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    
    # Performance data
    usage_count: int = 0
    success_rate: float = 0.0
    average_execution_time: float = 0.0
    user_satisfaction: float = 0.0
    
    # Learning data
    learned_from: List[str] = field(default_factory=list)  # task_ids, user_feedback, etc.
    improvement_history: List[str] = field(default_factory=list)  # improvement_ids


@dataclass
class LearningSession:
    """A learning session where the agent analyzes and improves."""
    session_id: str = field(default_factory=lambda: str(uuid4()))
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    # Session scope
    learning_types: List[LearningType] = field(default_factory=list)
    data_sources: List[str] = field(default_factory=list)
    time_range_start: Optional[datetime] = None
    time_range_end: Optional[datetime] = None
    
    # Results
    insights_discovered: List[str] = field(default_factory=list)  # insight_ids
    proposals_generated: List[str] = field(default_factory=list)  # proposal_ids
    capabilities_updated: List[str] = field(default_factory=list)  # capability_ids
    
    # Session metrics
    data_points_analyzed: int = 0
    processing_time: float = 0.0
    insights_per_hour: float = 0.0
    
    # Session outcome
    success: bool = True
    error_messages: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)


@dataclass
class UserFeedbackPattern:
    """Patterns discovered in user feedback."""
    pattern_id: str = field(default_factory=lambda: str(uuid4()))
    pattern_type: str = ""  # preference, complaint, suggestion, praise
    
    # Pattern details
    description: str = ""
    frequency: int = 0
    confidence: float = 0.0
    
    # Associated data
    user_ids: List[str] = field(default_factory=list)
    task_types: List[str] = field(default_factory=list)
    feedback_samples: List[str] = field(default_factory=list)
    
    # Temporal data
    first_observed: datetime = field(default_factory=datetime.now)
    last_observed: datetime = field(default_factory=datetime.now)
    trend: str = "stable"  # increasing, decreasing, stable
    
    # Action items
    suggested_improvements: List[str] = field(default_factory=list)
    priority: str = "medium"  # low, medium, high


@dataclass
class KnowledgeBase:
    """The agent's knowledge base for continuous learning."""
    kb_id: str = field(default_factory=lambda: str(uuid4()))
    version: str = "1.0"
    last_updated: datetime = field(default_factory=datetime.now)
    
    # Knowledge categories
    task_patterns: Dict[str, Any] = field(default_factory=dict)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    error_solutions: Dict[str, Any] = field(default_factory=dict)
    optimization_rules: Dict[str, Any] = field(default_factory=dict)
    
    # Learning statistics
    total_insights: int = 0
    implemented_improvements: int = 0
    success_rate: float = 0.0
    
    # Knowledge sources
    learning_sessions: List[str] = field(default_factory=list)
    user_feedback: List[str] = field(default_factory=list)
    performance_data: List[str] = field(default_factory=list)


@dataclass
class AutomationScript:
    """A learned automation script."""
    script_id: str = field(default_factory=lambda: str(uuid4()))
    name: str = ""
    description: str = ""
    
    # Script content
    script_type: str = ""  # python, shell, workflow
    script_content: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # Learning metadata
    learned_from_tasks: List[str] = field(default_factory=list)
    learning_confidence: float = 0.0
    
    # Usage statistics
    execution_count: int = 0
    success_rate: float = 0.0
    average_runtime: float = 0.0
    
    # Validation
    tested: bool = False
    test_results: List[Dict[str, Any]] = field(default_factory=list)
    approved_for_use: bool = False
