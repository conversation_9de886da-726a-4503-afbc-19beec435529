"""
Configuration management for Desktop AI Agent.

This module handles all configuration settings, environment variables,
and application setup parameters.
"""

import os
import platform
from pathlib import Path
from typing import Any, Dict, List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    url: str = Field(default="sqlite:///./daa.db", description="Database URL")
    echo: bool = Field(default=False, description="Enable SQL query logging")
    pool_size: int = Field(default=5, description="Connection pool size")
    max_overflow: int = Field(default=10, description="Max overflow connections")
    
    class Config:
        env_prefix = "DAA_DB_"


class RedisSettings(BaseSettings):
    """Redis configuration settings."""
    
    host: str = Field(default="localhost", description="Redis host")
    port: int = Field(default=6379, description="Redis port")
    db: int = Field(default=0, description="Redis database number")
    password: Optional[str] = Field(default=None, description="Redis password")
    max_connections: int = Field(default=20, description="Max Redis connections")
    
    class Config:
        env_prefix = "DAA_REDIS_"


class SecuritySettings(BaseSettings):
    """Security and authentication settings."""
    
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="Secret key for JWT tokens"
    )
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(
        default=30, description="Access token expiration time"
    )
    refresh_token_expire_days: int = Field(
        default=7, description="Refresh token expiration time"
    )
    bcrypt_rounds: int = Field(default=12, description="Bcrypt hashing rounds")
    
    class Config:
        env_prefix = "DAA_SECURITY_"


class AIModelSettings(BaseSettings):
    """AI model configuration settings."""
    
    model_path: Optional[str] = Field(
        default=None, description="Path to the local AI model file"
    )
    model_name: str = Field(
        default="codellama-7b-instruct.gguf", description="Model filename"
    )
    context_length: int = Field(default=4096, description="Model context length")
    max_tokens: int = Field(default=512, description="Max tokens per response")
    temperature: float = Field(default=0.7, description="Model temperature")
    top_p: float = Field(default=0.9, description="Top-p sampling")
    n_gpu_layers: int = Field(default=0, description="Number of GPU layers")
    n_threads: Optional[int] = Field(default=None, description="Number of CPU threads")
    
    @validator("n_threads", pre=True, always=True)
    def set_default_threads(cls, v):
        if v is None:
            return max(1, os.cpu_count() // 2)
        return v
    
    class Config:
        env_prefix = "DAA_AI_"


class DesktopSettings(BaseSettings):
    """Desktop interaction settings."""
    
    screenshot_format: str = Field(default="PNG", description="Screenshot format")
    screenshot_quality: int = Field(default=95, description="Screenshot quality")
    input_delay: float = Field(default=0.1, description="Input simulation delay")
    window_detection_timeout: float = Field(
        default=5.0, description="Window detection timeout"
    )
    safe_mode: bool = Field(
        default=True, description="Enable safe mode restrictions"
    )
    
    class Config:
        env_prefix = "DAA_DESKTOP_"


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""
    
    level: str = Field(default="INFO", description="Logging level")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format"
    )
    file_path: Optional[str] = Field(default=None, description="Log file path")
    max_file_size: int = Field(default=10485760, description="Max log file size (10MB)")
    backup_count: int = Field(default=5, description="Number of backup log files")
    structured: bool = Field(default=True, description="Use structured logging")
    
    class Config:
        env_prefix = "DAA_LOG_"


class APISettings(BaseSettings):
    """API server configuration settings."""
    
    host: str = Field(default="127.0.0.1", description="API server host")
    port: int = Field(default=8000, description="API server port")
    workers: int = Field(default=1, description="Number of worker processes")
    reload: bool = Field(default=False, description="Enable auto-reload")
    debug: bool = Field(default=False, description="Enable debug mode")
    cors_origins: List[str] = Field(
        default=["http://localhost:3000"], description="CORS allowed origins"
    )
    rate_limit_requests: int = Field(
        default=100, description="Rate limit requests per minute"
    )
    
    class Config:
        env_prefix = "DAA_API_"


class Settings(BaseSettings):
    """Main application settings."""
    
    # Application info
    app_name: str = Field(default="Desktop AI Agent", description="Application name")
    app_version: str = Field(default="0.1.0", description="Application version")
    environment: str = Field(default="development", description="Environment")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Component settings
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    ai_model: AIModelSettings = Field(default_factory=AIModelSettings)
    desktop: DesktopSettings = Field(default_factory=DesktopSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    api: APISettings = Field(default_factory=APISettings)
    
    # System info
    platform: str = Field(default_factory=lambda: platform.system().lower())
    data_dir: Path = Field(
        default_factory=lambda: Path.home() / ".desktop_ai_agent"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure data directory exists
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Set model path if not provided
        if not self.ai_model.model_path:
            self.ai_model.model_path = str(self.data_dir / "models" / self.ai_model.model_name)
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"
    
    def get_database_url(self) -> str:
        """Get the complete database URL."""
        if self.database.url.startswith("sqlite"):
            # Ensure SQLite database is in data directory
            db_path = self.data_dir / "daa.db"
            return f"sqlite:///{db_path}"
        return self.database.url
    
    def get_log_file_path(self) -> Optional[Path]:
        """Get the log file path."""
        if self.logging.file_path:
            return Path(self.logging.file_path)
        if not self.is_development:
            return self.data_dir / "logs" / "daa.log"
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary."""
        return self.dict()


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment."""
    global settings
    settings = Settings()
    return settings
