"""
Central Orchestration Layer (COL) for Desktop AI Agent.

This module provides the main coordination hub that manages all system components,
task execution flow, and inter-component communication.
"""

import asyncio
import logging
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set

import structlog
from pydantic import BaseModel, Field

from desktop_ai_agent.core.config import Settings


class ComponentStatus(str, Enum):
    """Component status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    OFFLINE = "offline"


class TaskStatus(str, Enum):
    """Task status enumeration."""
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """Task priority enumeration."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class Component(BaseModel):
    """Component registration model."""
    component_id: str
    name: str
    version: str
    status: ComponentStatus = ComponentStatus.OFFLINE
    last_heartbeat: Optional[datetime] = None
    capabilities: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Task(BaseModel):
    """Task model for the orchestration system."""
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    type: str = "simple"
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.QUEUED
    parameters: Dict[str, Any] = Field(default_factory=dict)
    dependencies: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0
    current_step: Optional[str] = None
    steps_completed: int = 0
    total_steps: int = 1
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    timeout: Optional[int] = None
    retry_count: int = 0
    max_retries: int = 3


class ComponentRegistry:
    """Registry for managing system components."""
    
    def __init__(self):
        self.components: Dict[str, Component] = {}
        self.logger = structlog.get_logger(__name__)
    
    def register_component(self, component: Component) -> bool:
        """Register a new component."""
        try:
            self.components[component.component_id] = component
            self.logger.info(
                "Component registered",
                component_id=component.component_id,
                name=component.name,
                version=component.version
            )
            return True
        except Exception as e:
            self.logger.error(
                "Failed to register component",
                component_id=component.component_id,
                error=str(e)
            )
            return False
    
    def unregister_component(self, component_id: str) -> bool:
        """Unregister a component."""
        if component_id in self.components:
            component = self.components.pop(component_id)
            self.logger.info(
                "Component unregistered",
                component_id=component_id,
                name=component.name
            )
            return True
        return False
    
    def get_component(self, component_id: str) -> Optional[Component]:
        """Get a component by ID."""
        return self.components.get(component_id)
    
    def list_components(self, status: Optional[ComponentStatus] = None) -> List[Component]:
        """List all components, optionally filtered by status."""
        components = list(self.components.values())
        if status:
            components = [c for c in components if c.status == status]
        return components
    
    def update_component_status(self, component_id: str, status: ComponentStatus) -> bool:
        """Update component status."""
        if component_id in self.components:
            self.components[component_id].status = status
            self.components[component_id].last_heartbeat = datetime.utcnow()
            return True
        return False
    
    def get_healthy_components(self) -> List[Component]:
        """Get all healthy components."""
        return [c for c in self.components.values() if c.status == ComponentStatus.HEALTHY]


class TaskQueue:
    """Task queue management system."""
    
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.queue: asyncio.Queue = asyncio.Queue()
        self.running_tasks: Set[str] = set()
        self.logger = structlog.get_logger(__name__)
    
    async def add_task(self, task: Task) -> str:
        """Add a task to the queue."""
        try:
            self.tasks[task.task_id] = task
            await self.queue.put(task.task_id)
            self.logger.info(
                "Task added to queue",
                task_id=task.task_id,
                name=task.name,
                priority=task.priority
            )
            return task.task_id
        except Exception as e:
            self.logger.error(
                "Failed to add task to queue",
                task_id=task.task_id,
                error=str(e)
            )
            raise
    
    async def get_next_task(self) -> Optional[Task]:
        """Get the next task from the queue."""
        try:
            task_id = await self.queue.get()
            if task_id in self.tasks:
                task = self.tasks[task_id]
                if task.status == TaskStatus.QUEUED:
                    self.running_tasks.add(task_id)
                    task.status = TaskStatus.RUNNING
                    task.started_at = datetime.utcnow()
                    return task
            return None
        except Exception as e:
            self.logger.error("Failed to get next task", error=str(e))
            return None
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID."""
        return self.tasks.get(task_id)
    
    def update_task_progress(self, task_id: str, progress: float, current_step: Optional[str] = None) -> bool:
        """Update task progress."""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.progress = max(0.0, min(1.0, progress))
            if current_step:
                task.current_step = current_step
            return True
        return False
    
    def complete_task(self, task_id: str, result: Optional[Dict[str, Any]] = None) -> bool:
        """Mark a task as completed."""
        if task_id in self.tasks and task_id in self.running_tasks:
            task = self.tasks[task_id]
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()
            task.progress = 1.0
            if result:
                task.result = result
            self.running_tasks.discard(task_id)
            self.logger.info("Task completed", task_id=task_id, name=task.name)
            return True
        return False
    
    def fail_task(self, task_id: str, error_message: str) -> bool:
        """Mark a task as failed."""
        if task_id in self.tasks and task_id in self.running_tasks:
            task = self.tasks[task_id]
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.utcnow()
            task.error_message = error_message
            self.running_tasks.discard(task_id)
            self.logger.error("Task failed", task_id=task_id, name=task.name, error=error_message)
            return True
        return False
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a task."""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()
            self.running_tasks.discard(task_id)
            self.logger.info("Task cancelled", task_id=task_id, name=task.name)
            return True
        return False
    
    def list_tasks(self, status: Optional[TaskStatus] = None) -> List[Task]:
        """List all tasks, optionally filtered by status."""
        tasks = list(self.tasks.values())
        if status:
            tasks = [t for t in tasks if t.status == status]
        return sorted(tasks, key=lambda t: t.created_at, reverse=True)


class CentralOrchestrator:
    """
    Central Orchestration Layer (COL) - Main coordination hub.
    
    Manages all system components, task execution flow, and inter-component communication.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        
        # Core components
        self.component_registry = ComponentRegistry()
        self.task_queue = TaskQueue()
        
        # State management
        self.is_running = False
        self.worker_tasks: List[asyncio.Task] = []
        
        # Event system
        self.event_handlers: Dict[str, List[callable]] = {}
        
        self.logger.info("Central Orchestrator initialized")
    
    async def start(self) -> None:
        """Start the orchestration system."""
        if self.is_running:
            self.logger.warning("Orchestrator is already running")
            return
        
        self.is_running = True
        self.logger.info("Starting Central Orchestrator")
        
        # Start worker tasks
        self.worker_tasks = [
            asyncio.create_task(self._task_worker()),
            asyncio.create_task(self._health_monitor()),
        ]
        
        self.logger.info("Central Orchestrator started successfully")
    
    async def stop(self) -> None:
        """Stop the orchestration system."""
        if not self.is_running:
            return
        
        self.is_running = False
        self.logger.info("Stopping Central Orchestrator")
        
        # Cancel worker tasks
        for task in self.worker_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        self.worker_tasks.clear()
        
        self.logger.info("Central Orchestrator stopped")
    
    async def _task_worker(self) -> None:
        """Background worker for processing tasks."""
        self.logger.info("Task worker started")
        
        while self.is_running:
            try:
                task = await self.task_queue.get_next_task()
                if task:
                    await self._execute_task(task)
                else:
                    await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Task worker error", error=str(e))
                await asyncio.sleep(1)
        
        self.logger.info("Task worker stopped")
    
    async def _health_monitor(self) -> None:
        """Background health monitoring for components."""
        self.logger.info("Health monitor started")
        
        while self.is_running:
            try:
                await self._check_component_health()
                await asyncio.sleep(30)  # Check every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Health monitor error", error=str(e))
                await asyncio.sleep(5)
        
        self.logger.info("Health monitor stopped")
    
    async def _execute_task(self, task: Task) -> None:
        """Execute a task."""
        self.logger.info("Executing task", task_id=task.task_id, name=task.name)
        
        try:
            # Emit task started event
            await self._emit_event("task_started", {"task": task})
            
            # Simple task execution for Phase 1
            # In later phases, this will delegate to appropriate components
            await asyncio.sleep(1)  # Simulate work
            
            # Mark task as completed
            self.task_queue.complete_task(task.task_id, {"status": "success"})
            
            # Emit task completed event
            await self._emit_event("task_completed", {"task": task})
            
        except Exception as e:
            self.logger.error("Task execution failed", task_id=task.task_id, error=str(e))
            self.task_queue.fail_task(task.task_id, str(e))
            await self._emit_event("task_failed", {"task": task, "error": str(e)})
    
    async def _check_component_health(self) -> None:
        """Check health of all registered components."""
        components = self.component_registry.list_components()
        for component in components:
            # Simple health check - in production, this would ping components
            if component.last_heartbeat:
                time_since_heartbeat = (datetime.utcnow() - component.last_heartbeat).total_seconds()
                if time_since_heartbeat > 60:  # 1 minute timeout
                    self.component_registry.update_component_status(
                        component.component_id, ComponentStatus.UNHEALTHY
                    )
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Emit an event to registered handlers."""
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    await handler(data)
                except Exception as e:
                    self.logger.error("Event handler error", event_type=event_type, error=str(e))
    
    def register_event_handler(self, event_type: str, handler: callable) -> None:
        """Register an event handler."""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    # Public API methods
    
    async def submit_task(self, task: Task) -> str:
        """Submit a task for execution."""
        return await self.task_queue.add_task(task)
    
    def get_task_status(self, task_id: str) -> Optional[Task]:
        """Get task status."""
        return self.task_queue.get_task(task_id)
    
    def list_tasks(self, status: Optional[TaskStatus] = None) -> List[Task]:
        """List tasks."""
        return self.task_queue.list_tasks(status)
    
    def register_component(self, component: Component) -> bool:
        """Register a component."""
        return self.component_registry.register_component(component)
    
    def get_component_status(self, component_id: str) -> Optional[Component]:
        """Get component status."""
        return self.component_registry.get_component(component_id)
    
    def list_components(self, status: Optional[ComponentStatus] = None) -> List[Component]:
        """List components."""
        return self.component_registry.list_components(status)
