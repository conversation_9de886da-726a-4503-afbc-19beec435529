"""
Model Inference and Response Generation (MIRG) Engine for Desktop AI Agent.

This module provides the main interface for AI model inference,
response generation, and context management.
"""

import asyncio
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog

from desktop_ai_agent.ai.context_manager import Context<PERSON>anager
from desktop_ai_agent.ai.model_manager import ModelManager
from desktop_ai_agent.ai.models import (
    ConversationContext,
    InferenceRequest,
    InferenceResponse,
    ModelInfo,
    TaskContext,
)
from desktop_ai_agent.core.config import Settings
from desktop_ai_agent.core.orchestrator import Component, ComponentStatus


class ModelInferenceEngine:
    """
    Model Inference and Response Generation (MIRG) Engine.
    
    Provides AI model inference, response generation, and context management.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        
        # Initialize sub-components
        self.model_manager = ModelManager(settings)
        self.context_manager = ContextManager()
        
        # Component registration info
        self.component_info = Component(
            component_id="model_inference_engine",
            name="Model Inference and Response Generation Engine",
            version="0.1.0",
            status=ComponentStatus.HEALTHY,
            capabilities=[
                "text_generation",
                "context_management",
                "model_management",
                "response_generation"
            ],
            metadata={
                "max_context_length": settings.ai_model.context_length,
                "default_model": settings.ai_model.model_name
            }
        )
        
        # Default system prompts
        self.system_prompts = {
            "desktop_agent": """You are a Desktop AI Agent, an advanced autonomous system designed to interact with desktop environments and execute tasks through intelligent automation. 

Your capabilities include:
- Window management and application control
- Input simulation (keyboard and mouse)
- Screen capture and analysis
- File system operations
- Task planning and execution

Always prioritize user safety and system security. Ask for confirmation before performing potentially destructive operations. Provide clear, step-by-step explanations of your actions.""",
            
            "task_planning": """You are a task planning specialist for a Desktop AI Agent. Your role is to break down complex user requests into executable sub-tasks.

For each task, consider:
- Required permissions and safety implications
- Dependencies between sub-tasks
- Error handling and recovery strategies
- User confirmation requirements

Provide detailed, actionable plans that can be executed safely and efficiently."""
        }
        
        self.logger.info("Model Inference Engine initialized")
    
    async def start(self) -> None:
        """Start the model inference engine."""
        try:
            self.component_info.status = ComponentStatus.HEALTHY
            self.component_info.last_heartbeat = datetime.utcnow()
            
            # Initialize model manager
            await self.model_manager.initialize()
            
            self.logger.info("Model Inference Engine started")
        except Exception as e:
            self.component_info.status = ComponentStatus.UNHEALTHY
            self.logger.error("Failed to start Model Inference Engine", error=str(e))
            raise
    
    async def stop(self) -> None:
        """Stop the model inference engine."""
        try:
            # Clean up model manager
            await self.model_manager.cleanup()
            
            self.component_info.status = ComponentStatus.OFFLINE
            self.logger.info("Model Inference Engine stopped")
        except Exception as e:
            self.logger.error("Failed to stop Model Inference Engine", error=str(e))
    
    def get_component_info(self) -> Component:
        """Get component information for registration."""
        self.component_info.last_heartbeat = datetime.utcnow()
        return self.component_info
    
    async def generate_response(self, request: InferenceRequest) -> InferenceResponse:
        """Generate AI response for a request."""
        start_time = time.time()
        
        try:
            self.logger.debug("Generating response", request_id=request.request_id, model_id=request.model_id)
            
            # Get model
            model = self.model_manager.get_model(request.model_id)
            if not model:
                # Try to load the model
                success = await self.model_manager.load_model(request.model_id)
                if not success:
                    return InferenceResponse(
                        request_id=request.request_id,
                        text="",
                        model_id=request.model_id,
                        success=False,
                        error_message=f"Model {request.model_id} not available"
                    )
                model = self.model_manager.get_model(request.model_id)
            
            # Prepare prompt
            full_prompt = self._prepare_prompt(request)
            
            # Generate response using llama.cpp
            response_text = await self._generate_with_llama_cpp(model, full_prompt, request)
            
            # Calculate metrics
            inference_time = (time.time() - start_time) * 1000
            prompt_tokens = len(full_prompt.split())  # Rough estimate
            completion_tokens = len(response_text.split())  # Rough estimate
            total_tokens = prompt_tokens + completion_tokens
            tokens_per_second = completion_tokens / (inference_time / 1000) if inference_time > 0 else 0
            
            # Update model statistics
            self.model_manager.update_model_stats(request.model_id, inference_time, completion_tokens)
            
            # Create response
            response = InferenceResponse(
                request_id=request.request_id,
                text=response_text,
                model_id=request.model_id,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                inference_time_ms=inference_time,
                tokens_per_second=tokens_per_second,
                context_id=request.context_id
            )
            
            # Update conversation context if provided
            if request.context_id:
                self.context_manager.add_message(request.context_id, "user", request.prompt)
                self.context_manager.add_message(request.context_id, "assistant", response_text)
            
            self.logger.debug(
                "Response generated",
                request_id=request.request_id,
                inference_time_ms=inference_time,
                tokens=total_tokens
            )
            
            return response
            
        except Exception as e:
            inference_time = (time.time() - start_time) * 1000
            self.logger.error("Response generation failed", request_id=request.request_id, error=str(e))
            
            return InferenceResponse(
                request_id=request.request_id,
                text="",
                model_id=request.model_id,
                inference_time_ms=inference_time,
                success=False,
                error_message=str(e)
            )
    
    def _prepare_prompt(self, request: InferenceRequest) -> str:
        """Prepare the full prompt for inference."""
        prompt_parts = []
        
        # Add system prompt
        system_prompt = request.system_prompt
        if not system_prompt and request.context_id:
            context = self.context_manager.get_conversation_context(request.context_id)
            if context and context.system_prompt:
                system_prompt = context.system_prompt
        
        if not system_prompt:
            system_prompt = self.system_prompts.get("desktop_agent")
        
        if system_prompt:
            prompt_parts.append(f"System: {system_prompt}")
        
        # Add conversation history
        if request.context_id:
            context = self.context_manager.get_conversation_context(request.context_id)
            if context:
                for message in context.messages[-10:]:  # Last 10 messages
                    role = message["role"].title()
                    content = message["content"]
                    prompt_parts.append(f"{role}: {content}")
        
        # Add current prompt
        prompt_parts.append(f"User: {request.prompt}")
        prompt_parts.append("Assistant:")
        
        return "\n\n".join(prompt_parts)
    
    async def _generate_with_llama_cpp(self, model: Any, prompt: str, request: InferenceRequest) -> str:
        """Generate response using llama.cpp model."""
        try:
            # Prepare generation parameters
            generation_params = {
                "prompt": prompt,
                "max_tokens": request.max_tokens,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "top_k": request.top_k,
                "stop": request.stop_sequences,
                "echo": False
            }
            
            # Generate response
            response = model(**generation_params)
            
            # Extract generated text
            generated_text = response["choices"][0]["text"].strip()
            
            return generated_text
            
        except Exception as e:
            self.logger.error("Llama.cpp generation failed", error=str(e))
            return "I apologize, but I encountered an error while generating a response."
    
    async def create_conversation_context(
        self,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        context_type: str = "desktop_agent"
    ) -> ConversationContext:
        """Create a new conversation context."""
        system_prompt = self.system_prompts.get(context_type, self.system_prompts["desktop_agent"])
        
        context = self.context_manager.create_conversation_context(
            user_id=user_id,
            session_id=session_id,
            system_prompt=system_prompt
        )
        
        return context
    
    async def create_task_context(
        self,
        task_id: str,
        task_name: str,
        task_description: Optional[str] = None
    ) -> TaskContext:
        """Create a new task context."""
        return self.context_manager.create_task_context(task_id, task_name, task_description)
    
    def get_conversation_context(self, context_id: str) -> Optional[ConversationContext]:
        """Get conversation context."""
        return self.context_manager.get_conversation_context(context_id)
    
    def get_task_context(self, task_id: str) -> Optional[TaskContext]:
        """Get task context."""
        return self.context_manager.get_task_context(task_id)
    
    def list_models(self) -> List[ModelInfo]:
        """List available models."""
        return self.model_manager.list_models()
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get model information."""
        return self.model_manager.get_model_info(model_id)
    
    async def load_model(self, model_id: str) -> bool:
        """Load a model."""
        return await self.model_manager.load_model(model_id)
    
    async def unload_model(self, model_id: str) -> bool:
        """Unload a model."""
        return await self.model_manager.unload_model(model_id)
    
    def get_default_model_id(self) -> Optional[str]:
        """Get default model ID."""
        return self.model_manager.get_default_model_id()
