"""
Data models for AI inference components.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ModelType(str, Enum):
    """AI model type enumeration."""
    LLM = "llm"
    VLM = "vlm"  # Vision-Language Model
    SPECIALIZED = "specialized"


class ModelStatus(str, Enum):
    """Model status enumeration."""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ERROR = "error"


class InferenceRequest(BaseModel):
    """AI inference request model."""
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    model_id: str
    prompt: str
    
    # Generation parameters
    max_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 40
    stop_sequences: List[str] = Field(default_factory=list)
    
    # Context
    context_id: Optional[str] = None
    system_prompt: Optional[str] = None
    conversation_history: List[Dict[str, str]] = Field(default_factory=list)
    
    # Metadata
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Special parameters
    stream: bool = False
    include_reasoning: bool = False


class InferenceResponse(BaseModel):
    """AI inference response model."""
    request_id: str
    response_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    
    # Generated content
    text: str
    finish_reason: str = "stop"  # stop, length, error
    
    # Token usage
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    
    # Model info
    model_id: str
    model_version: Optional[str] = None
    
    # Performance metrics
    inference_time_ms: float = 0.0
    tokens_per_second: float = 0.0
    
    # Context
    context_id: Optional[str] = None
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    success: bool = True
    error_message: Optional[str] = None


class ModelInfo(BaseModel):
    """AI model information model."""
    model_id: str
    name: str
    type: ModelType
    version: str = "1.0"
    
    # Model specifications
    parameter_count: Optional[str] = None  # e.g., "7B", "13B"
    context_length: int = 4096
    quantization: Optional[str] = None  # e.g., "Q4_0", "Q8_0"
    
    # Capabilities
    capabilities: List[str] = Field(default_factory=list)
    supported_languages: List[str] = Field(default_factory=list)
    
    # File information
    file_path: Optional[str] = None
    file_size_mb: Optional[float] = None
    
    # Status and performance
    status: ModelStatus = ModelStatus.UNLOADED
    load_time_ms: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    gpu_memory_usage_mb: Optional[float] = None
    
    # Usage statistics
    total_requests: int = 0
    total_tokens_generated: int = 0
    average_inference_time_ms: float = 0.0
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_used: Optional[datetime] = None


class ConversationContext(BaseModel):
    """Conversation context model."""
    context_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    
    # Context content
    system_prompt: Optional[str] = None
    messages: List[Dict[str, str]] = Field(default_factory=list)
    
    # Context management
    max_messages: int = 50
    max_tokens: int = 4000
    current_tokens: int = 0
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    
    # Statistics
    message_count: int = 0
    total_input_tokens: int = 0
    total_output_tokens: int = 0


class TaskContext(BaseModel):
    """Task execution context for AI models."""
    task_id: str
    task_name: str
    task_description: Optional[str] = None
    
    # Desktop context
    current_application: Optional[str] = None
    active_windows: List[str] = Field(default_factory=list)
    screen_resolution: Optional[Dict[str, int]] = None
    
    # Execution state
    completed_steps: List[str] = Field(default_factory=list)
    current_step: Optional[str] = None
    variables: Dict[str, Any] = Field(default_factory=dict)
    
    # Screenshots and visual context
    screenshots: List[str] = Field(default_factory=list)  # Screenshot IDs
    
    # Timestamp
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class PromptTemplate(BaseModel):
    """Prompt template model."""
    template_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    category: str = "general"
    
    # Template content
    system_prompt: Optional[str] = None
    user_prompt_template: str
    variables: List[str] = Field(default_factory=list)
    
    # Usage statistics
    usage_count: int = 0
    success_rate: float = 0.0
    average_response_time: float = 0.0
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[str] = None
    tags: List[str] = Field(default_factory=list)


class ModelConfiguration(BaseModel):
    """Model configuration model."""
    config_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    model_id: str
    name: str = "default"
    
    # Inference parameters
    default_max_tokens: int = 512
    default_temperature: float = 0.7
    default_top_p: float = 0.9
    default_top_k: int = 40
    
    # Performance settings
    n_threads: Optional[int] = None
    n_gpu_layers: int = 0
    batch_size: int = 1
    
    # Memory management
    context_length: int = 4096
    rope_freq_base: float = 10000.0
    rope_freq_scale: float = 1.0
    
    # Safety settings
    max_response_length: int = 2048
    content_filter_enabled: bool = True
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True


class InferenceMetrics(BaseModel):
    """Inference performance metrics."""
    metric_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    model_id: str
    
    # Performance metrics
    requests_per_minute: float = 0.0
    average_inference_time_ms: float = 0.0
    tokens_per_second: float = 0.0
    
    # Resource usage
    cpu_usage_percent: float = 0.0
    memory_usage_mb: float = 0.0
    gpu_usage_percent: float = 0.0
    gpu_memory_usage_mb: float = 0.0
    
    # Quality metrics
    success_rate: float = 0.0
    error_rate: float = 0.0
    timeout_rate: float = 0.0
    
    # Usage statistics
    total_requests: int = 0
    total_tokens_generated: int = 0
    total_errors: int = 0
    
    # Time window
    window_start: datetime = Field(default_factory=datetime.utcnow)
    window_end: datetime = Field(default_factory=datetime.utcnow)
    
    # Metadata
    collected_at: datetime = Field(default_factory=datetime.utcnow)
