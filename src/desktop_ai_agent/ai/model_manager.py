"""
Model management for Desktop AI Agent.

This module provides AI model loading, management, and lifecycle control
with support for local models via llama.cpp.
"""

import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

import structlog

from desktop_ai_agent.ai.models import ModelInfo, ModelStatus, ModelType
from desktop_ai_agent.core.config import Settings


# MockLlamaModel removed - we only use real AI models in production


class ModelManager:
    """AI model management system."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        
        # Model registry
        self.models: Dict[str, ModelInfo] = {}
        self.loaded_models: Dict[str, Any] = {}  # model_id -> loaded model instance
        
        # Model search paths
        self.model_paths = [
            Path(settings.data_dir) / "models",
            Path.home() / ".cache" / "huggingface" / "transformers",
            Path("/opt/models") if os.path.exists("/opt/models") else None
        ]
        self.model_paths = [p for p in self.model_paths if p is not None]
        
        # Ensure model directory exists
        (Path(settings.data_dir) / "models").mkdir(parents=True, exist_ok=True)
        
        self.logger.info("Model manager initialized", model_paths=len(self.model_paths))
    
    async def initialize(self) -> None:
        """Initialize the model manager."""
        try:
            # Discover available models
            await self._discover_models()
            
            # Load default model if specified
            if self.settings.ai_model.model_path:
                await self.load_model_from_path(self.settings.ai_model.model_path)
            
            self.logger.info("Model manager initialized", available_models=len(self.models))
            
        except Exception as e:
            self.logger.error("Model manager initialization failed", error=str(e))
            raise
    
    async def _discover_models(self) -> None:
        """Discover available models in search paths."""
        try:
            model_extensions = [".gguf", ".bin", ".safetensors"]
            
            for search_path in self.model_paths:
                if not search_path.exists():
                    continue
                
                for model_file in search_path.rglob("*"):
                    if model_file.is_file() and any(model_file.name.endswith(ext) for ext in model_extensions):
                        await self._register_model_file(model_file)
            
            self.logger.info("Model discovery completed", discovered=len(self.models))
            
        except Exception as e:
            self.logger.error("Model discovery failed", error=str(e))
    
    async def _register_model_file(self, model_path: Path) -> None:
        """Register a model file."""
        try:
            # Extract model info from filename and path
            model_name = model_path.stem
            model_id = f"local_{model_name}"
            
            # Determine model type based on filename patterns
            model_type = ModelType.LLM
            if "vision" in model_name.lower() or "vlm" in model_name.lower():
                model_type = ModelType.VLM
            elif "embed" in model_name.lower() or "encoder" in model_name.lower():
                model_type = ModelType.SPECIALIZED
            
            # Extract parameter count if present
            parameter_count = None
            for size in ["7b", "13b", "30b", "65b", "70b"]:
                if size in model_name.lower():
                    parameter_count = size.upper()
                    break
            
            # Extract quantization info
            quantization = None
            quant_patterns = ["q4_0", "q4_1", "q5_0", "q5_1", "q8_0", "f16", "f32"]
            for quant in quant_patterns:
                if quant in model_name.lower():
                    quantization = quant.upper()
                    break
            
            # Get file size
            file_size_mb = model_path.stat().st_size / (1024 * 1024)
            
            # Create model info
            model_info = ModelInfo(
                model_id=model_id,
                name=model_name,
                type=model_type,
                parameter_count=parameter_count,
                quantization=quantization,
                file_path=str(model_path),
                file_size_mb=file_size_mb,
                capabilities=["text_generation"] if model_type == ModelType.LLM else ["multimodal"],
                supported_languages=["en"]  # Default to English
            )
            
            self.models[model_id] = model_info
            
            self.logger.debug(
                "Model registered",
                model_id=model_id,
                name=model_name,
                type=model_type,
                size_mb=file_size_mb
            )
            
        except Exception as e:
            self.logger.error("Failed to register model", model_path=str(model_path), error=str(e))
    
    async def load_model(self, model_id: str) -> bool:
        """Load a model by ID."""
        try:
            if model_id not in self.models:
                self.logger.error("Model not found", model_id=model_id)
                return False
            
            if model_id in self.loaded_models:
                self.logger.info("Model already loaded", model_id=model_id)
                return True
            
            model_info = self.models[model_id]
            model_info.status = ModelStatus.LOADING
            
            start_time = time.time()
            
            # Load model using llama.cpp
            loaded_model = await self._load_llama_cpp_model(model_info)
            
            if loaded_model:
                load_time = (time.time() - start_time) * 1000
                
                self.loaded_models[model_id] = loaded_model
                model_info.status = ModelStatus.LOADED
                model_info.load_time_ms = load_time
                model_info.last_used = None
                
                self.logger.info(
                    "Model loaded successfully",
                    model_id=model_id,
                    load_time_ms=load_time
                )
                return True
            else:
                model_info.status = ModelStatus.ERROR
                return False
                
        except Exception as e:
            if model_id in self.models:
                self.models[model_id].status = ModelStatus.ERROR
            self.logger.error("Model loading failed", model_id=model_id, error=str(e))
            return False
    
    async def _load_llama_cpp_model(self, model_info: ModelInfo) -> Optional[Any]:
        """Load model using llama.cpp."""
        try:
            # Import llama-cpp-python
            from llama_cpp import Llama
            
            # Model loading parameters
            model_params = {
                "model_path": model_info.file_path,
                "n_ctx": self.settings.ai_model.context_length,
                "n_threads": self.settings.ai_model.n_threads,
                "n_gpu_layers": self.settings.ai_model.n_gpu_layers,
                "verbose": False
            }
            
            # Load the model
            model = Llama(**model_params)
            
            self.logger.debug("Llama.cpp model loaded", model_id=model_info.model_id)
            return model
            
        except ImportError as e:
            self.logger.error("llama-cpp-python not available - real AI models required", error=str(e))
            raise RuntimeError(f"Cannot load AI model: llama-cpp-python not installed. Install with: pip install llama-cpp-python") from e
        except Exception as e:
            self.logger.error("Failed to load llama.cpp model", error=str(e), model_path=model_info.file_path)
            raise RuntimeError(f"Cannot load AI model from {model_info.file_path}: {str(e)}") from e
    
    async def load_model_from_path(self, model_path: str) -> Optional[str]:
        """Load a model from a file path."""
        try:
            path = Path(model_path)
            if not path.exists():
                self.logger.error("Model file not found", path=model_path)
                return None
            
            # Register the model first
            await self._register_model_file(path)
            
            # Find the registered model ID
            model_id = None
            for mid, model_info in self.models.items():
                if model_info.file_path == str(path):
                    model_id = mid
                    break
            
            if model_id:
                success = await self.load_model(model_id)
                return model_id if success else None
            
            return None
            
        except Exception as e:
            self.logger.error("Failed to load model from path", path=model_path, error=str(e))
            return None
    
    async def unload_model(self, model_id: str) -> bool:
        """Unload a model."""
        try:
            if model_id not in self.loaded_models:
                return True
            
            # Clean up model resources
            del self.loaded_models[model_id]
            
            if model_id in self.models:
                self.models[model_id].status = ModelStatus.UNLOADED
                self.models[model_id].memory_usage_mb = None
                self.models[model_id].gpu_memory_usage_mb = None
            
            self.logger.info("Model unloaded", model_id=model_id)
            return True
            
        except Exception as e:
            self.logger.error("Model unloading failed", model_id=model_id, error=str(e))
            return False
    
    def get_model(self, model_id: str) -> Optional[Any]:
        """Get a loaded model instance."""
        return self.loaded_models.get(model_id)
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get model information."""
        return self.models.get(model_id)
    
    def list_models(self, status: Optional[ModelStatus] = None) -> List[ModelInfo]:
        """List available models."""
        models = list(self.models.values())
        if status:
            models = [m for m in models if m.status == status]
        return models
    
    def list_loaded_models(self) -> List[str]:
        """List loaded model IDs."""
        return list(self.loaded_models.keys())
    
    def get_default_model_id(self) -> Optional[str]:
        """Get the default model ID."""
        # Return the first loaded model, or first available model
        if self.loaded_models:
            return next(iter(self.loaded_models.keys()))
        
        if self.models:
            return next(iter(self.models.keys()))
        
        return None
    
    async def cleanup(self) -> None:
        """Clean up all loaded models."""
        try:
            for model_id in list(self.loaded_models.keys()):
                await self.unload_model(model_id)
            
            self.logger.info("Model manager cleanup completed")
            
        except Exception as e:
            self.logger.error("Model manager cleanup failed", error=str(e))
    
    def update_model_stats(self, model_id: str, inference_time_ms: float, tokens_generated: int) -> None:
        """Update model usage statistics."""
        if model_id in self.models:
            model_info = self.models[model_id]
            model_info.total_requests += 1
            model_info.total_tokens_generated += tokens_generated
            
            # Update average inference time
            total_time = model_info.average_inference_time_ms * (model_info.total_requests - 1)
            model_info.average_inference_time_ms = (total_time + inference_time_ms) / model_info.total_requests
            
            model_info.last_used = time.time()
