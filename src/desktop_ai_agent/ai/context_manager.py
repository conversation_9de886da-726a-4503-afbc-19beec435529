"""
Context management for Desktop AI Agent.

This module provides conversation context and task context management
for AI model interactions.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import structlog

from desktop_ai_agent.ai.models import ConversationContext, TaskContext


class ContextManager:
    """Context management system for AI interactions."""
    
    def __init__(self, max_contexts: int = 100):
        self.max_contexts = max_contexts
        self.logger = structlog.get_logger(__name__)
        
        # Context storage
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        self.task_contexts: Dict[str, TaskContext] = {}
        
        self.logger.info("Context manager initialized", max_contexts=max_contexts)
    
    def create_conversation_context(
        self,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        system_prompt: Optional[str] = None
    ) -> ConversationContext:
        """Create a new conversation context."""
        context = ConversationContext(
            user_id=user_id,
            session_id=session_id,
            system_prompt=system_prompt
        )
        
        self.conversation_contexts[context.context_id] = context
        self._cleanup_old_contexts()
        
        self.logger.debug("Conversation context created", context_id=context.context_id)
        return context
    
    def get_conversation_context(self, context_id: str) -> Optional[ConversationContext]:
        """Get conversation context by ID."""
        context = self.conversation_contexts.get(context_id)
        if context:
            context.last_activity = datetime.utcnow()
        return context
    
    def add_message(self, context_id: str, role: str, content: str) -> bool:
        """Add a message to conversation context."""
        context = self.get_conversation_context(context_id)
        if not context:
            return False
        
        message = {"role": role, "content": content, "timestamp": datetime.utcnow().isoformat()}
        context.messages.append(message)
        context.message_count += 1
        context.updated_at = datetime.utcnow()
        
        # Trim context if too long
        if len(context.messages) > context.max_messages:
            context.messages = context.messages[-context.max_messages:]
        
        return True
    
    def create_task_context(
        self,
        task_id: str,
        task_name: str,
        task_description: Optional[str] = None
    ) -> TaskContext:
        """Create a new task context."""
        context = TaskContext(
            task_id=task_id,
            task_name=task_name,
            task_description=task_description
        )
        
        self.task_contexts[task_id] = context
        
        self.logger.debug("Task context created", task_id=task_id)
        return context
    
    def get_task_context(self, task_id: str) -> Optional[TaskContext]:
        """Get task context by ID."""
        return self.task_contexts.get(task_id)
    
    def update_task_context(
        self,
        task_id: str,
        current_step: Optional[str] = None,
        variables: Optional[Dict[str, Any]] = None,
        screenshots: Optional[List[str]] = None
    ) -> bool:
        """Update task context."""
        context = self.get_task_context(task_id)
        if not context:
            return False
        
        if current_step:
            context.current_step = current_step
        
        if variables:
            context.variables.update(variables)
        
        if screenshots:
            context.screenshots.extend(screenshots)
        
        context.updated_at = datetime.utcnow()
        return True
    
    def _cleanup_old_contexts(self) -> None:
        """Clean up old conversation contexts."""
        if len(self.conversation_contexts) <= self.max_contexts:
            return
        
        # Remove oldest contexts
        contexts_by_age = sorted(
            self.conversation_contexts.items(),
            key=lambda x: x[1].last_activity
        )
        
        contexts_to_remove = contexts_by_age[:len(self.conversation_contexts) - self.max_contexts]
        
        for context_id, _ in contexts_to_remove:
            del self.conversation_contexts[context_id]
        
        self.logger.debug("Cleaned up old contexts", removed=len(contexts_to_remove))
