"""
Command Line Interface for Desktop AI Agent.

This module provides CLI commands for managing the Desktop AI Agent,
including starting the service, managing models, and running diagnostics.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
import structlog

from desktop_ai_agent.core.config import Settings, get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.monitoring.logger import setup_logging


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config: Optional[str], verbose: bool):
    """Desktop AI Agent - Advanced autonomous desktop automation system."""
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Load settings
    settings = get_settings()
    if verbose:
        settings.logging.level = "DEBUG"
    
    # Set up logging
    setup_logging(settings)
    
    # Store settings in context
    ctx.obj['settings'] = settings
    ctx.obj['logger'] = structlog.get_logger(__name__)


@cli.command()
@click.option('--host', default=None, help='API server host')
@click.option('--port', type=int, default=None, help='API server port')
@click.option('--reload', is_flag=True, help='Enable auto-reload in development')
@click.pass_context
def start(ctx, host: Optional[str], port: Optional[int], reload: bool):
    """Start the Desktop AI Agent service."""
    settings = ctx.obj['settings']
    logger = ctx.obj['logger']
    
    # Override settings if provided
    if host:
        settings.api.host = host
    if port:
        settings.api.port = port
    if reload:
        settings.api.reload = reload
    
    logger.info("Starting Desktop AI Agent service", 
                host=settings.api.host, 
                port=settings.api.port)
    
    async def run_app():
        app = DesktopAIAgent(settings)
        try:
            await app.initialize()
            await app.start()
        except KeyboardInterrupt:
            logger.info("Shutdown requested by user")
        finally:
            await app.stop()
    
    try:
        asyncio.run(run_app())
    except Exception as e:
        logger.error("Service failed", error=str(e))
        sys.exit(1)


@cli.command()
@click.pass_context
def status(ctx):
    """Check the status of Desktop AI Agent components."""
    settings = ctx.obj['settings']
    logger = ctx.obj['logger']
    
    async def check_status():
        app = DesktopAIAgent(settings)
        try:
            await app.initialize()
            
            # Get component statuses
            if app.orchestrator:
                components = app.orchestrator.list_components()
                
                click.echo("Desktop AI Agent Component Status:")
                click.echo("=" * 40)
                
                for component in components:
                    status_color = "green" if component.status == "healthy" else "red"
                    click.echo(f"{component.name}: ", nl=False)
                    click.secho(component.status, fg=status_color)
                
                # Get health summary
                if app.health_monitor:
                    health_summary = app.health_monitor.get_health_summary()
                    overall_status = health_summary["overall_status"]
                    
                    click.echo(f"\nOverall Status: ", nl=False)
                    status_color = "green" if overall_status == "healthy" else "red"
                    click.secho(overall_status, fg=status_color)
            
        except Exception as e:
            logger.error("Status check failed", error=str(e))
            click.echo(f"Error: {e}", err=True)
        finally:
            if app:
                await app.stop()
    
    asyncio.run(check_status())


@cli.group()
def models():
    """AI model management commands."""
    pass


@models.command('list')
@click.pass_context
def list_models(ctx):
    """List available AI models."""
    settings = ctx.obj['settings']
    logger = ctx.obj['logger']
    
    async def list_available_models():
        app = DesktopAIAgent(settings)
        try:
            await app.initialize()
            
            if app.ai_engine:
                models = app.ai_engine.list_models()
                
                if not models:
                    click.echo("No AI models found.")
                    return
                
                click.echo("Available AI Models:")
                click.echo("=" * 50)
                
                for model in models:
                    status_color = "green" if model.status == "loaded" else "yellow"
                    click.echo(f"ID: {model.model_id}")
                    click.echo(f"Name: {model.name}")
                    click.echo(f"Type: {model.type}")
                    click.echo(f"Status: ", nl=False)
                    click.secho(model.status, fg=status_color)
                    if model.file_path:
                        click.echo(f"Path: {model.file_path}")
                    if model.file_size_mb:
                        click.echo(f"Size: {model.file_size_mb:.1f} MB")
                    click.echo("-" * 30)
            
        except Exception as e:
            logger.error("Model listing failed", error=str(e))
            click.echo(f"Error: {e}", err=True)
        finally:
            if app:
                await app.stop()
    
    asyncio.run(list_available_models())


@models.command('load')
@click.argument('model_id')
@click.pass_context
def load_model(ctx, model_id: str):
    """Load an AI model."""
    settings = ctx.obj['settings']
    logger = ctx.obj['logger']
    
    async def load_model_async():
        app = DesktopAIAgent(settings)
        try:
            await app.initialize()
            
            if app.ai_engine:
                click.echo(f"Loading model: {model_id}")
                success = await app.ai_engine.load_model(model_id)
                
                if success:
                    click.secho(f"Model {model_id} loaded successfully!", fg="green")
                else:
                    click.secho(f"Failed to load model {model_id}", fg="red")
            
        except Exception as e:
            logger.error("Model loading failed", error=str(e))
            click.echo(f"Error: {e}", err=True)
        finally:
            if app:
                await app.stop()
    
    asyncio.run(load_model_async())


@models.command('download')
@click.argument('model_name')
@click.option('--url', help='Direct download URL for the model')
@click.pass_context
def download_model(ctx, model_name: str, url: Optional[str]):
    """Download an AI model."""
    settings = ctx.obj['settings']
    logger = ctx.obj['logger']
    
    # Create models directory
    models_dir = settings.data_dir / "models"
    models_dir.mkdir(parents=True, exist_ok=True)
    
    if url:
        click.echo(f"Downloading model from: {url}")
        # In a full implementation, this would download the model
        click.echo("Model download functionality not implemented in Phase 1")
    else:
        # Suggest some common models
        click.echo("Suggested models for Desktop AI Agent:")
        click.echo("- CodeLlama-7B-Instruct-GGUF")
        click.echo("- Mistral-7B-Instruct-v0.2-GGUF")
        click.echo("- Llama-2-7B-Chat-GGUF")
        click.echo("\nPlease provide a --url parameter to download a specific model.")


@cli.command()
@click.pass_context
def test(ctx):
    """Run system tests and diagnostics."""
    settings = ctx.obj['settings']
    logger = ctx.obj['logger']
    
    async def run_tests():
        click.echo("Running Desktop AI Agent diagnostics...")
        
        app = DesktopAIAgent(settings)
        try:
            # Test initialization
            click.echo("Testing component initialization...")
            await app.initialize()
            click.secho("✓ Initialization successful", fg="green")
            
            # Test component health
            if app.health_monitor:
                click.echo("Running health checks...")
                health_summary = app.health_monitor.get_health_summary()
                
                for component, status in health_summary["components"].items():
                    status_color = "green" if status["status"] == "healthy" else "red"
                    click.echo(f"  {component}: ", nl=False)
                    click.secho(status["status"], fg=status_color)
            
            # Test basic functionality
            click.echo("Testing basic functionality...")
            
            # Test task creation
            if app.orchestrator:
                from desktop_ai_agent.core.orchestrator import Task
                
                test_task = Task(
                    name="Test Task",
                    description="Simple test task",
                    type="simple"
                )
                
                task_id = await app.orchestrator.submit_task(test_task)
                click.secho(f"✓ Task creation successful: {task_id}", fg="green")
            
            click.secho("\n✓ All tests passed!", fg="green")
            
        except Exception as e:
            logger.error("Test failed", error=str(e))
            click.secho(f"✗ Test failed: {e}", fg="red")
        finally:
            if app:
                await app.stop()
    
    asyncio.run(run_tests())


@cli.command()
@click.option('--output', '-o', type=click.Path(), help='Output file for configuration')
@click.pass_context
def config(ctx, output: Optional[str]):
    """Generate or display configuration."""
    settings = ctx.obj['settings']
    
    config_dict = settings.to_dict()
    
    if output:
        import json
        output_path = Path(output)
        with open(output_path, 'w') as f:
            json.dump(config_dict, f, indent=2, default=str)
        click.echo(f"Configuration saved to: {output_path}")
    else:
        import json
        click.echo(json.dumps(config_dict, indent=2, default=str))


def main():
    """Main CLI entry point."""
    cli()


if __name__ == '__main__':
    main()
