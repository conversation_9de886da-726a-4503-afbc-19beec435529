"""
Conversation models for the Desktop AI Agent.

This module defines the data structures for managing conversations,
context, and user interactions.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, field
from uuid import uuid4


class MessageType(Enum):
    """Types of messages in a conversation."""
    USER_TEXT = "user_text"
    USER_VOICE = "user_voice"
    AGENT_TEXT = "agent_text"
    AGENT_VOICE = "agent_voice"
    SYSTEM = "system"
    TASK_RESULT = "task_result"
    FEEDBACK_REQUEST = "feedback_request"


class ConversationLanguage(Enum):
    """Supported conversation languages."""
    ENGLISH = "en"
    ROMANIAN = "ro"
    AUTO_DETECT = "auto"


class TaskComplexity(Enum):
    """Complexity levels for tasks."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    MULTI_STEP = "multi_step"


@dataclass
class ConversationMessage:
    """A single message in a conversation."""
    message_id: str = field(default_factory=lambda: str(uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    message_type: MessageType = MessageType.USER_TEXT
    content: str = ""
    language: ConversationLanguage = ConversationLanguage.AUTO_DETECT
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Voice-specific fields
    audio_data: Optional[bytes] = None
    audio_duration: Optional[float] = None
    
    # Task-related fields
    task_id: Optional[str] = None
    task_complexity: Optional[TaskComplexity] = None
    
    # Context fields
    context_references: List[str] = field(default_factory=list)
    user_intent: Optional[str] = None
    confidence_score: Optional[float] = None


@dataclass
class ConversationContext:
    """Context information for a conversation."""
    context_id: str = field(default_factory=lambda: str(uuid4()))
    user_id: str = ""
    session_id: str = ""
    
    # Current conversation state
    current_topic: Optional[str] = None
    active_tasks: List[str] = field(default_factory=list)
    pending_feedback: List[str] = field(default_factory=list)
    
    # User preferences
    preferred_language: ConversationLanguage = ConversationLanguage.AUTO_DETECT
    communication_style: str = "friendly"  # friendly, professional, concise
    expertise_level: str = "intermediate"  # beginner, intermediate, advanced
    
    # Context memory
    recent_topics: List[str] = field(default_factory=list)
    user_goals: List[str] = field(default_factory=list)
    common_tasks: Dict[str, int] = field(default_factory=dict)  # task -> frequency
    
    # Learning context
    learning_preferences: Dict[str, Any] = field(default_factory=dict)
    feedback_history: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class ConversationSession:
    """A conversation session between user and agent."""
    session_id: str = field(default_factory=lambda: str(uuid4()))
    user_id: str = ""
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    # Messages in this session
    messages: List[ConversationMessage] = field(default_factory=list)
    
    # Session context
    context: ConversationContext = field(default_factory=ConversationContext)
    
    # Session statistics
    total_messages: int = 0
    tasks_completed: int = 0
    user_satisfaction: Optional[float] = None
    
    # Session metadata
    platform: str = "cli"  # cli, web, gui
    voice_enabled: bool = False
    language_detected: Optional[ConversationLanguage] = None


@dataclass
class TaskFeedback:
    """Feedback about a completed task."""
    feedback_id: str = field(default_factory=lambda: str(uuid4()))
    task_id: str = ""
    session_id: str = ""
    user_id: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Feedback ratings (1-5 scale)
    success_rating: Optional[int] = None
    speed_rating: Optional[int] = None
    accuracy_rating: Optional[int] = None
    overall_satisfaction: Optional[int] = None
    
    # Textual feedback
    positive_aspects: List[str] = field(default_factory=list)
    improvement_areas: List[str] = field(default_factory=list)
    additional_comments: str = ""
    
    # Learning data
    task_complexity_perceived: Optional[TaskComplexity] = None
    would_use_again: Optional[bool] = None
    recommend_to_others: Optional[bool] = None


@dataclass
class ConversationIntent:
    """Detected user intent from a message."""
    intent_id: str = field(default_factory=lambda: str(uuid4()))
    message_id: str = ""
    
    # Intent classification
    primary_intent: str = ""  # task_request, question, feedback, greeting, etc.
    secondary_intents: List[str] = field(default_factory=list)
    confidence: float = 0.0
    
    # Extracted entities
    entities: Dict[str, Any] = field(default_factory=dict)
    
    # Task-related
    task_type: Optional[str] = None
    task_parameters: Dict[str, Any] = field(default_factory=dict)
    urgency_level: str = "normal"  # low, normal, high, urgent
    
    # Context requirements
    requires_context: bool = False
    context_dependencies: List[str] = field(default_factory=list)


@dataclass
class VoiceSettings:
    """Voice input/output settings."""
    voice_enabled: bool = False
    input_language: ConversationLanguage = ConversationLanguage.AUTO_DETECT
    output_language: ConversationLanguage = ConversationLanguage.AUTO_DETECT
    
    # Voice recognition settings
    recognition_confidence_threshold: float = 0.7
    noise_reduction: bool = True
    auto_punctuation: bool = True
    
    # Voice synthesis settings
    voice_speed: float = 1.0
    voice_pitch: float = 1.0
    voice_volume: float = 0.8
    preferred_voice: Optional[str] = None
    
    # Audio settings
    sample_rate: int = 16000
    audio_format: str = "wav"
    max_recording_duration: int = 30  # seconds


@dataclass
class ConversationMetrics:
    """Metrics for conversation analysis."""
    session_id: str = ""
    
    # Response metrics
    average_response_time: float = 0.0
    total_response_time: float = 0.0
    fastest_response: float = float('inf')
    slowest_response: float = 0.0
    
    # Conversation flow
    message_count: int = 0
    user_messages: int = 0
    agent_messages: int = 0
    context_switches: int = 0
    
    # Task metrics
    tasks_requested: int = 0
    tasks_completed: int = 0
    tasks_failed: int = 0
    average_task_complexity: float = 0.0
    
    # User engagement
    session_duration: float = 0.0
    user_satisfaction_score: Optional[float] = None
    return_user: bool = False
    
    # Language metrics
    languages_used: List[ConversationLanguage] = field(default_factory=list)
    language_switches: int = 0
    translation_requests: int = 0


class ConversationState(Enum):
    """Current state of the conversation."""
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    RESPONDING = "responding"
    EXECUTING_TASK = "executing_task"
    WAITING_FEEDBACK = "waiting_feedback"
    LEARNING = "learning"
    ERROR = "error"
