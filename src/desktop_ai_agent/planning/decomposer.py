"""
Task decomposition for Desktop AI Agent.

This module provides intelligent task decomposition capabilities,
breaking down complex tasks into manageable sub-tasks.
"""

import re
from typing import Any, Dict, List, Optional

import structlog

from desktop_ai_agent.core.orchestrator import Task
from desktop_ai_agent.planning.models import (
    DependencyGraph,
    ExecutionContext,
    PlanningResult,
    SubTask,
    TaskPlan,
    TaskType,
)


class TaskDecomposer:
    """Intelligent task decomposition system."""
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        
        # Built-in task patterns for Phase 1
        self.task_patterns = {
            "open_application": self._decompose_open_application,
            "close_application": self._decompose_close_application,
            "take_screenshot": self._decompose_take_screenshot,
            "click_element": self._decompose_click_element,
            "type_text": self._decompose_type_text,
            "window_management": self._decompose_window_management,
            "file_operation": self._decompose_file_operation,
        }
        
        # Action type mappings
        self.action_mappings = {
            "screenshot": "take_screenshot",
            "click": "click_element", 
            "type": "type_text",
            "open": "open_application",
            "close": "close_application",
            "focus": "window_management",
            "minimize": "window_management",
            "maximize": "window_management",
        }
        
        self.logger.info("Task decomposer initialized")
    
    async def decompose_task(self, task: Task, context: ExecutionContext) -> PlanningResult:
        """Decompose a task into executable sub-tasks."""
        start_time = context.updated_at
        
        try:
            self.logger.info("Decomposing task", task_id=task.task_id, name=task.name, type=task.type)
            
            # Analyze task to determine decomposition strategy
            task_analysis = self._analyze_task(task, context)
            
            # Create initial task plan
            task_plan = TaskPlan(
                task_id=task.task_id,
                name=task.name,
                description=task.description,
                task_type=TaskType(task.type) if task.type in TaskType.__members__.values() else TaskType.SIMPLE,
                context=context.dict()
            )
            
            # Decompose based on task type and content
            if task_plan.task_type == TaskType.SIMPLE:
                subtasks = await self._decompose_simple_task(task, context, task_analysis)
            elif task_plan.task_type == TaskType.COMPLEX:
                subtasks = await self._decompose_complex_task(task, context, task_analysis)
            elif task_plan.task_type == TaskType.WORKFLOW:
                subtasks = await self._decompose_workflow_task(task, context, task_analysis)
            else:
                subtasks = await self._decompose_simple_task(task, context, task_analysis)
            
            # Add subtasks to plan
            task_plan.subtasks = subtasks
            
            # Build dependency graph
            dependency_graph = self._build_dependency_graph(subtasks)
            
            # Validate plan
            validation_result = self._validate_plan(task_plan, dependency_graph)
            if not validation_result["valid"]:
                return PlanningResult(
                    success=False,
                    error_message=f"Plan validation failed: {validation_result['error']}",
                    planning_time=(context.updated_at - start_time).total_seconds()
                )
            
            # Estimate execution time
            task_plan.estimated_total_duration = self._estimate_execution_time(subtasks)
            
            # Optimize plan
            optimized_plan = self._optimize_plan(task_plan, context)
            
            planning_time = (context.updated_at - start_time).total_seconds()
            
            result = PlanningResult(
                success=True,
                plan=optimized_plan,
                planning_time=planning_time,
                estimated_execution_time=optimized_plan.estimated_total_duration,
                complexity_score=len(subtasks),
                decomposition_method=task_analysis.get("method", "pattern_based")
            )
            
            self.logger.info(
                "Task decomposition completed",
                task_id=task.task_id,
                subtasks_count=len(subtasks),
                planning_time=planning_time
            )
            
            return result
            
        except Exception as e:
            planning_time = (context.updated_at - start_time).total_seconds()
            self.logger.error("Task decomposition failed", task_id=task.task_id, error=str(e))
            
            return PlanningResult(
                success=False,
                error_message=str(e),
                planning_time=planning_time
            )
    
    def _analyze_task(self, task: Task, context: ExecutionContext) -> Dict[str, Any]:
        """Analyze task to determine decomposition strategy."""
        analysis = {
            "method": "pattern_based",
            "complexity": "simple",
            "keywords": [],
            "action_type": None,
            "parameters": {}
        }
        
        # Extract keywords from task name and description
        text = f"{task.name} {task.description or ''}".lower()
        
        # Identify action type
        for action, pattern in self.action_mappings.items():
            if action in text:
                analysis["action_type"] = pattern
                break
        
        # Extract common keywords
        keywords = re.findall(r'\b(?:open|close|click|type|screenshot|window|application|file)\b', text)
        analysis["keywords"] = list(set(keywords))
        
        # Determine complexity
        if len(keywords) > 3 or "and" in text or "then" in text:
            analysis["complexity"] = "complex"
        
        # Extract parameters from task parameters
        analysis["parameters"] = task.parameters
        
        return analysis
    
    async def _decompose_simple_task(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose a simple task."""
        action_type = analysis.get("action_type")
        
        if action_type and action_type in self.task_patterns:
            return self.task_patterns[action_type](task, context, analysis)
        else:
            # Default decomposition for unknown tasks
            return self._decompose_generic_task(task, context, analysis)
    
    async def _decompose_complex_task(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose a complex task."""
        # For Phase 1, treat complex tasks as sequences of simple tasks
        subtasks = []
        
        # Split task description by common separators
        text = task.description or task.name
        steps = re.split(r'\b(?:and|then|next|after)\b', text.lower())
        
        for i, step in enumerate(steps):
            step = step.strip()
            if step:
                subtask = SubTask(
                    name=f"Step {i+1}: {step}",
                    description=step,
                    action_type="generic_action",
                    parameters={"step_text": step},
                    order=i
                )
                subtasks.append(subtask)
        
        return subtasks
    
    async def _decompose_workflow_task(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose a workflow task."""
        # For Phase 1, treat workflows as complex tasks
        return await self._decompose_complex_task(task, context, analysis)
    
    def _decompose_open_application(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose application opening task."""
        app_name = analysis["parameters"].get("application") or self._extract_app_name(task.name)
        
        subtasks = [
            SubTask(
                name="Take initial screenshot",
                action_type="screenshot",
                parameters={"region": None},
                order=0
            ),
            SubTask(
                name=f"Open {app_name}",
                action_type="open_application",
                parameters={"application": app_name},
                dependencies=[],
                order=1
            ),
            SubTask(
                name="Verify application opened",
                action_type="verify_window",
                parameters={"application": app_name},
                dependencies=[],
                order=2
            )
        ]
        
        return subtasks
    
    def _decompose_close_application(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose application closing task."""
        app_name = analysis["parameters"].get("application") or self._extract_app_name(task.name)
        
        subtasks = [
            SubTask(
                name=f"Find {app_name} window",
                action_type="find_window",
                parameters={"application": app_name},
                order=0
            ),
            SubTask(
                name=f"Close {app_name}",
                action_type="close_window",
                parameters={"application": app_name},
                dependencies=[],
                order=1
            )
        ]
        
        return subtasks
    
    def _decompose_take_screenshot(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose screenshot task."""
        region = analysis["parameters"].get("region")
        
        subtasks = [
            SubTask(
                name="Capture screenshot",
                action_type="screenshot",
                parameters={"region": region},
                order=0
            )
        ]
        
        return subtasks
    
    def _decompose_click_element(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose click element task."""
        position = analysis["parameters"].get("position")
        element = analysis["parameters"].get("element")
        
        subtasks = [
            SubTask(
                name="Take screenshot for element detection",
                action_type="screenshot",
                parameters={"region": None},
                order=0
            )
        ]
        
        if position:
            subtasks.append(SubTask(
                name="Click at position",
                action_type="click",
                parameters={"position": position},
                dependencies=[],
                order=1
            ))
        elif element:
            subtasks.extend([
                SubTask(
                    name=f"Find element: {element}",
                    action_type="find_element",
                    parameters={"element": element},
                    dependencies=[],
                    order=1
                ),
                SubTask(
                    name=f"Click element: {element}",
                    action_type="click",
                    parameters={"element": element},
                    dependencies=[],
                    order=2
                )
            ])
        
        return subtasks
    
    def _decompose_type_text(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose text typing task."""
        text = analysis["parameters"].get("text", "")
        
        subtasks = [
            SubTask(
                name="Type text",
                action_type="type_text",
                parameters={"text": text},
                order=0
            )
        ]
        
        return subtasks
    
    def _decompose_window_management(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose window management task."""
        action = analysis["parameters"].get("action", "focus")
        window_id = analysis["parameters"].get("window_id")
        
        subtasks = [
            SubTask(
                name=f"Window {action}",
                action_type=f"window_{action}",
                parameters={"window_id": window_id},
                order=0
            )
        ]
        
        return subtasks
    
    def _decompose_file_operation(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Decompose file operation task."""
        operation = analysis["parameters"].get("operation", "open")
        file_path = analysis["parameters"].get("file_path", "")
        
        subtasks = [
            SubTask(
                name=f"File {operation}",
                action_type=f"file_{operation}",
                parameters={"file_path": file_path},
                order=0
            )
        ]
        
        return subtasks
    
    def _decompose_generic_task(self, task: Task, context: ExecutionContext, analysis: Dict[str, Any]) -> List[SubTask]:
        """Generic task decomposition fallback."""
        subtasks = [
            SubTask(
                name=task.name,
                description=task.description,
                action_type="generic_action",
                parameters=task.parameters,
                order=0
            )
        ]
        
        return subtasks
    
    def _extract_app_name(self, text: str) -> str:
        """Extract application name from text."""
        # Simple extraction - in production, this would be more sophisticated
        words = text.lower().split()
        common_apps = ["notepad", "calculator", "browser", "chrome", "firefox", "word", "excel"]
        
        for word in words:
            if word in common_apps:
                return word
        
        return "unknown"
    
    def _build_dependency_graph(self, subtasks: List[SubTask]) -> DependencyGraph:
        """Build dependency graph from subtasks."""
        graph = DependencyGraph()
        
        # Add all nodes
        for subtask in subtasks:
            graph.add_node(subtask)
        
        # Add dependency edges
        for subtask in subtasks:
            for dep_id in subtask.dependencies:
                graph.add_dependency(dep_id, subtask.subtask_id)
        
        return graph
    
    def _validate_plan(self, plan: TaskPlan, graph: DependencyGraph) -> Dict[str, Any]:
        """Validate task plan."""
        # Check for circular dependencies
        if graph.has_cycles():
            return {"valid": False, "error": "Circular dependencies detected"}
        
        # Check if all dependencies exist
        for subtask in plan.subtasks:
            for dep_id in subtask.dependencies:
                if dep_id not in graph.nodes:
                    return {"valid": False, "error": f"Dependency {dep_id} not found"}
        
        return {"valid": True}
    
    def _estimate_execution_time(self, subtasks: List[SubTask]) -> float:
        """Estimate total execution time for subtasks."""
        # Simple estimation based on action types
        time_estimates = {
            "screenshot": 1.0,
            "click": 0.5,
            "type_text": 2.0,
            "open_application": 3.0,
            "close_application": 1.0,
            "window_focus": 0.5,
            "generic_action": 2.0
        }
        
        total_time = 0.0
        for subtask in subtasks:
            action_type = subtask.action_type
            estimated_time = time_estimates.get(action_type, 2.0)
            total_time += estimated_time
        
        return total_time
    
    def _optimize_plan(self, plan: TaskPlan, context: ExecutionContext) -> TaskPlan:
        """Optimize task plan for better execution."""
        # For Phase 1, minimal optimization
        # Sort subtasks by order
        plan.subtasks.sort(key=lambda x: x.order)
        
        return plan
