"""
Data models for task planning and execution components.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class TaskType(str, Enum):
    """Task type enumeration."""
    SIMPLE = "simple"
    COMPLEX = "complex"
    WORKFLOW = "workflow"
    CONDITIONAL = "conditional"
    LOOP = "loop"


class ExecutionStrategy(str, Enum):
    """Execution strategy enumeration."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    RETRY = "retry"


class DependencyType(str, Enum):
    """Dependency type enumeration."""
    PREREQUISITE = "prerequisite"
    RESOURCE = "resource"
    DATA = "data"
    TIMING = "timing"


class PlanningStatus(str, Enum):
    """Planning status enumeration."""
    PENDING = "pending"
    ANALYZING = "analyzing"
    PLANNING = "planning"
    READY = "ready"
    FAILED = "failed"


class SubTask(BaseModel):
    """Sub-task model for task decomposition."""
    subtask_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    action_type: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    dependencies: List[str] = Field(default_factory=list)
    estimated_duration: Optional[float] = None
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    order: int = 0
    
    # Execution tracking
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: str = "pending"
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class TaskPlan(BaseModel):
    """Task execution plan model."""
    plan_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    task_id: str
    name: str
    description: Optional[str] = None
    task_type: TaskType = TaskType.SIMPLE
    execution_strategy: ExecutionStrategy = ExecutionStrategy.SEQUENTIAL
    
    # Planning metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    planning_status: PlanningStatus = PlanningStatus.PENDING
    estimated_total_duration: Optional[float] = None
    
    # Sub-tasks and dependencies
    subtasks: List[SubTask] = Field(default_factory=list)
    dependencies: List[str] = Field(default_factory=list)
    
    # Execution tracking
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    current_subtask: Optional[str] = None
    completed_subtasks: List[str] = Field(default_factory=list)
    failed_subtasks: List[str] = Field(default_factory=list)
    
    # Context and state
    context: Dict[str, Any] = Field(default_factory=dict)
    shared_state: Dict[str, Any] = Field(default_factory=dict)
    
    @property
    def progress(self) -> float:
        """Calculate execution progress."""
        if not self.subtasks:
            return 0.0
        
        completed = len(self.completed_subtasks)
        total = len(self.subtasks)
        return completed / total if total > 0 else 0.0
    
    @property
    def is_complete(self) -> bool:
        """Check if plan execution is complete."""
        return len(self.completed_subtasks) == len(self.subtasks)
    
    @property
    def has_failures(self) -> bool:
        """Check if plan has failed subtasks."""
        return len(self.failed_subtasks) > 0


class DependencyGraph(BaseModel):
    """Dependency graph for task execution ordering."""
    nodes: Dict[str, SubTask] = Field(default_factory=dict)
    edges: Dict[str, List[str]] = Field(default_factory=dict)  # node_id -> [dependent_node_ids]
    
    def add_node(self, subtask: SubTask) -> None:
        """Add a node to the dependency graph."""
        self.nodes[subtask.subtask_id] = subtask
        if subtask.subtask_id not in self.edges:
            self.edges[subtask.subtask_id] = []
    
    def add_dependency(self, from_task: str, to_task: str) -> None:
        """Add a dependency edge."""
        if from_task not in self.edges:
            self.edges[from_task] = []
        if to_task not in self.edges[from_task]:
            self.edges[from_task].append(to_task)
    
    def get_ready_tasks(self, completed_tasks: List[str]) -> List[str]:
        """Get tasks that are ready to execute."""
        ready_tasks = []
        
        for task_id, subtask in self.nodes.items():
            if task_id in completed_tasks:
                continue
            
            # Check if all dependencies are completed
            dependencies_met = all(
                dep in completed_tasks for dep in subtask.dependencies
            )
            
            if dependencies_met:
                ready_tasks.append(task_id)
        
        return ready_tasks
    
    def has_cycles(self) -> bool:
        """Check if the dependency graph has cycles."""
        visited = set()
        rec_stack = set()
        
        def has_cycle_util(node: str) -> bool:
            visited.add(node)
            rec_stack.add(node)
            
            for neighbor in self.edges.get(node, []):
                if neighbor not in visited:
                    if has_cycle_util(neighbor):
                        return True
                elif neighbor in rec_stack:
                    return True
            
            rec_stack.remove(node)
            return False
        
        for node in self.nodes:
            if node not in visited:
                if has_cycle_util(node):
                    return True
        
        return False


class ExecutionContext(BaseModel):
    """Execution context for task planning."""
    context_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    
    # Environment context
    platform: str
    screen_resolution: Optional[Dict[str, int]] = None
    active_applications: List[str] = Field(default_factory=list)
    
    # User preferences
    preferences: Dict[str, Any] = Field(default_factory=dict)
    safety_level: str = "normal"  # low, normal, high
    
    # Execution constraints
    max_execution_time: Optional[float] = None
    resource_limits: Dict[str, Any] = Field(default_factory=dict)
    
    # State tracking
    variables: Dict[str, Any] = Field(default_factory=dict)
    screenshots: List[str] = Field(default_factory=list)  # Screenshot IDs
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class PlanningResult(BaseModel):
    """Result of task planning operation."""
    success: bool
    plan: Optional[TaskPlan] = None
    error_message: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)
    planning_time: float = 0.0
    estimated_execution_time: Optional[float] = None
    complexity_score: Optional[float] = None
    
    # Planning metadata
    decomposition_method: Optional[str] = None
    optimization_applied: List[str] = Field(default_factory=list)
    alternative_plans: List[TaskPlan] = Field(default_factory=list)


class ExecutionResult(BaseModel):
    """Result of task execution."""
    plan_id: str
    success: bool
    completed_subtasks: List[str] = Field(default_factory=list)
    failed_subtasks: List[str] = Field(default_factory=list)
    execution_time: float = 0.0
    final_result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    
    # Execution statistics
    total_subtasks: int = 0
    success_rate: float = 0.0
    average_subtask_time: float = 0.0
    
    # Context and artifacts
    final_context: Optional[ExecutionContext] = None
    screenshots: List[str] = Field(default_factory=list)
    logs: List[str] = Field(default_factory=list)
    
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class TaskTemplate(BaseModel):
    """Template for common task patterns."""
    template_id: str
    name: str
    description: str
    category: str
    task_type: TaskType
    
    # Template structure
    subtask_templates: List[Dict[str, Any]] = Field(default_factory=list)
    parameter_schema: Dict[str, Any] = Field(default_factory=dict)
    
    # Metadata
    usage_count: int = 0
    success_rate: float = 0.0
    average_execution_time: float = 0.0
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class OptimizationRule(BaseModel):
    """Rule for task plan optimization."""
    rule_id: str
    name: str
    description: str
    condition: str  # Condition expression
    action: str     # Optimization action
    priority: int = 0
    enabled: bool = True
    
    # Statistics
    applied_count: int = 0
    success_count: int = 0
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
