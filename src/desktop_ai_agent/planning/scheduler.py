"""
Execution scheduler for Desktop AI Agent.

This module provides intelligent task execution scheduling with dependency
resolution, parallel execution, and progress tracking.
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional, Set

import structlog

from desktop_ai_agent.planning.models import (
    DependencyGraph,
    ExecutionContext,
    ExecutionResult,
    SubTask,
    TaskPlan,
)


class ExecutionScheduler:
    """Intelligent task execution scheduler."""
    
    def __init__(self, max_concurrent_tasks: int = 3):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.logger = structlog.get_logger(__name__)
        
        # Execution tracking
        self.active_executions: Dict[str, TaskPlan] = {}
        self.execution_semaphore = asyncio.Semaphore(max_concurrent_tasks)
        
        self.logger.info("Execution scheduler initialized", max_concurrent=max_concurrent_tasks)
    
    async def execute_plan(
        self,
        plan: TaskPlan,
        context: ExecutionContext,
        executor_callback: callable
    ) -> ExecutionResult:
        """Execute a task plan with intelligent scheduling."""
        start_time = datetime.utcnow()
        plan_id = plan.plan_id
        
        try:
            self.active_executions[plan_id] = plan
            self.logger.info("Starting plan execution", plan_id=plan_id, subtasks=len(plan.subtasks))
            
            # Initialize execution state
            plan.started_at = start_time
            completed_subtasks: Set[str] = set()
            failed_subtasks: Set[str] = set()
            running_tasks: Dict[str, asyncio.Task] = {}
            
            # Build dependency graph
            dependency_graph = self._build_dependency_graph(plan.subtasks)
            
            # Execute subtasks with dependency resolution
            while len(completed_subtasks) + len(failed_subtasks) < len(plan.subtasks):
                # Get ready tasks
                ready_tasks = dependency_graph.get_ready_tasks(list(completed_subtasks))
                
                # Filter out already running or failed tasks
                ready_tasks = [
                    task_id for task_id in ready_tasks
                    if task_id not in running_tasks and task_id not in failed_subtasks
                ]
                
                # Start new tasks up to concurrency limit
                available_slots = self.max_concurrent_tasks - len(running_tasks)
                tasks_to_start = ready_tasks[:available_slots]
                
                for task_id in tasks_to_start:
                    subtask = next(st for st in plan.subtasks if st.subtask_id == task_id)
                    task_coroutine = self._execute_subtask(subtask, context, executor_callback)
                    running_tasks[task_id] = asyncio.create_task(task_coroutine)
                    self.logger.debug("Started subtask", task_id=task_id, name=subtask.name)
                
                # Wait for at least one task to complete
                if running_tasks:
                    done, pending = await asyncio.wait(
                        running_tasks.values(),
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    # Process completed tasks
                    for task in done:
                        # Find the task ID
                        task_id = None
                        for tid, t in running_tasks.items():
                            if t == task:
                                task_id = tid
                                break
                        
                        if task_id:
                            try:
                                result = await task
                                if result["success"]:
                                    completed_subtasks.add(task_id)
                                    plan.completed_subtasks.append(task_id)
                                    self.logger.debug("Subtask completed", task_id=task_id)
                                else:
                                    failed_subtasks.add(task_id)
                                    plan.failed_subtasks.append(task_id)
                                    self.logger.error("Subtask failed", task_id=task_id, error=result.get("error"))
                            except Exception as e:
                                failed_subtasks.add(task_id)
                                plan.failed_subtasks.append(task_id)
                                self.logger.error("Subtask execution error", task_id=task_id, error=str(e))
                            
                            # Remove from running tasks
                            running_tasks.pop(task_id, None)
                
                # Check for deadlock (no tasks running and no ready tasks)
                if not running_tasks and not ready_tasks:
                    remaining_tasks = set(st.subtask_id for st in plan.subtasks) - completed_subtasks - failed_subtasks
                    if remaining_tasks:
                        self.logger.error("Execution deadlock detected", remaining_tasks=list(remaining_tasks))
                        break
                
                # Small delay to prevent busy waiting
                await asyncio.sleep(0.1)
            
            # Wait for any remaining running tasks
            if running_tasks:
                await asyncio.gather(*running_tasks.values(), return_exceptions=True)
            
            # Calculate execution statistics
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            success_rate = len(completed_subtasks) / len(plan.subtasks) if plan.subtasks else 0.0
            
            # Create execution result
            result = ExecutionResult(
                plan_id=plan_id,
                success=len(failed_subtasks) == 0,
                completed_subtasks=list(completed_subtasks),
                failed_subtasks=list(failed_subtasks),
                execution_time=execution_time,
                total_subtasks=len(plan.subtasks),
                success_rate=success_rate,
                average_subtask_time=execution_time / len(plan.subtasks) if plan.subtasks else 0.0,
                final_context=context
            )
            
            plan.completed_at = datetime.utcnow()
            
            self.logger.info(
                "Plan execution completed",
                plan_id=plan_id,
                success=result.success,
                completed=len(completed_subtasks),
                failed=len(failed_subtasks),
                execution_time=execution_time
            )
            
            return result
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            self.logger.error("Plan execution failed", plan_id=plan_id, error=str(e))
            
            return ExecutionResult(
                plan_id=plan_id,
                success=False,
                execution_time=execution_time,
                error_message=str(e),
                total_subtasks=len(plan.subtasks)
            )
            
        finally:
            self.active_executions.pop(plan_id, None)
    
    async def _execute_subtask(
        self,
        subtask: SubTask,
        context: ExecutionContext,
        executor_callback: callable
    ) -> Dict[str, Any]:
        """Execute a single subtask."""
        async with self.execution_semaphore:
            start_time = datetime.utcnow()
            
            try:
                subtask.started_at = start_time
                subtask.status = "running"
                
                self.logger.debug("Executing subtask", subtask_id=subtask.subtask_id, name=subtask.name)
                
                # Call the executor callback
                result = await executor_callback(subtask, context)
                
                execution_time = (datetime.utcnow() - start_time).total_seconds()
                
                if result.get("success", False):
                    subtask.status = "completed"
                    subtask.completed_at = datetime.utcnow()
                    subtask.result = result
                    
                    self.logger.debug(
                        "Subtask execution successful",
                        subtask_id=subtask.subtask_id,
                        execution_time=execution_time
                    )
                else:
                    subtask.status = "failed"
                    subtask.completed_at = datetime.utcnow()
                    subtask.error_message = result.get("error", "Unknown error")
                    
                    self.logger.error(
                        "Subtask execution failed",
                        subtask_id=subtask.subtask_id,
                        error=subtask.error_message
                    )
                
                return result
                
            except Exception as e:
                execution_time = (datetime.utcnow() - start_time).total_seconds()
                
                subtask.status = "failed"
                subtask.completed_at = datetime.utcnow()
                subtask.error_message = str(e)
                
                self.logger.error(
                    "Subtask execution exception",
                    subtask_id=subtask.subtask_id,
                    error=str(e),
                    execution_time=execution_time
                )
                
                return {"success": False, "error": str(e)}
    
    def _build_dependency_graph(self, subtasks: List[SubTask]) -> DependencyGraph:
        """Build dependency graph from subtasks."""
        graph = DependencyGraph()
        
        # Add all nodes
        for subtask in subtasks:
            graph.add_node(subtask)
        
        # Add dependency edges
        for subtask in subtasks:
            for dep_id in subtask.dependencies:
                graph.add_dependency(dep_id, subtask.subtask_id)
        
        return graph
    
    async def cancel_execution(self, plan_id: str) -> bool:
        """Cancel an active execution."""
        if plan_id in self.active_executions:
            plan = self.active_executions[plan_id]
            # In a full implementation, this would cancel running tasks
            self.logger.info("Execution cancelled", plan_id=plan_id)
            return True
        return False
    
    def get_execution_status(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """Get execution status for a plan."""
        if plan_id in self.active_executions:
            plan = self.active_executions[plan_id]
            return {
                "plan_id": plan_id,
                "status": "running",
                "progress": plan.progress,
                "completed_subtasks": len(plan.completed_subtasks),
                "failed_subtasks": len(plan.failed_subtasks),
                "total_subtasks": len(plan.subtasks),
                "current_subtask": plan.current_subtask,
                "started_at": plan.started_at.isoformat() if plan.started_at else None
            }
        return None
    
    def list_active_executions(self) -> List[str]:
        """List all active execution plan IDs."""
        return list(self.active_executions.keys())
    
    async def pause_execution(self, plan_id: str) -> bool:
        """Pause an active execution."""
        # Placeholder for pause functionality
        self.logger.info("Execution pause requested", plan_id=plan_id)
        return False
    
    async def resume_execution(self, plan_id: str) -> bool:
        """Resume a paused execution."""
        # Placeholder for resume functionality
        self.logger.info("Execution resume requested", plan_id=plan_id)
        return False
