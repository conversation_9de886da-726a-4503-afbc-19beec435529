"""
Task Planning and Execution Engine (TPEE) for Desktop AI Agent.

This module provides the main interface for task planning, decomposition,
and execution coordination.
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog

from desktop_ai_agent.core.config import Settings
from desktop_ai_agent.core.orchestrator import Component, ComponentStatus, Task
from desktop_ai_agent.planning.decomposer import TaskDecomposer
from desktop_ai_agent.planning.models import (
    ExecutionContext,
    ExecutionResult,
    PlanningResult,
    SubTask,
    TaskPlan,
)
from desktop_ai_agent.planning.scheduler import ExecutionScheduler


class TaskPlanningEngine:
    """
    Task Planning and Execution Engine (TPEE) - Main interface for task planning.
    
    Coordinates task decomposition, dependency resolution, execution scheduling,
    and progress tracking.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        
        # Initialize sub-components
        self.task_decomposer = TaskDecomposer()
        self.execution_scheduler = ExecutionScheduler(max_concurrent_tasks=3)
        
        # Component registration info
        self.component_info = Component(
            component_id="task_planning_engine",
            name="Task Planning and Execution Engine",
            version="0.1.0",
            status=ComponentStatus.HEALTHY,
            capabilities=[
                "task_decomposition",
                "dependency_resolution",
                "execution_scheduling",
                "progress_tracking",
                "plan_optimization"
            ],
            metadata={
                "max_concurrent_tasks": 3,
                "supported_task_types": ["simple", "complex", "workflow"]
            }
        )
        
        # State management
        self.active_plans: Dict[str, TaskPlan] = {}
        self.execution_contexts: Dict[str, ExecutionContext] = {}
        
        # Executor callback (will be set by orchestrator)
        self.executor_callback: Optional[callable] = None
        
        self.logger.info("Task Planning Engine initialized")
    
    async def start(self) -> None:
        """Start the task planning engine."""
        try:
            self.component_info.status = ComponentStatus.HEALTHY
            self.component_info.last_heartbeat = datetime.utcnow()
            self.logger.info("Task Planning Engine started")
        except Exception as e:
            self.component_info.status = ComponentStatus.UNHEALTHY
            self.logger.error("Failed to start Task Planning Engine", error=str(e))
            raise
    
    async def stop(self) -> None:
        """Stop the task planning engine."""
        try:
            # Cancel any active executions
            for plan_id in list(self.active_plans.keys()):
                await self.cancel_execution(plan_id)
            
            self.component_info.status = ComponentStatus.OFFLINE
            self.logger.info("Task Planning Engine stopped")
        except Exception as e:
            self.logger.error("Failed to stop Task Planning Engine", error=str(e))
    
    def get_component_info(self) -> Component:
        """Get component information for registration."""
        self.component_info.last_heartbeat = datetime.utcnow()
        return self.component_info
    
    def set_executor_callback(self, callback: callable) -> None:
        """Set the executor callback for subtask execution."""
        self.executor_callback = callback
        self.logger.info("Executor callback set")
    
    async def plan_task(self, task: Task, user_id: Optional[str] = None) -> PlanningResult:
        """Plan a task by decomposing it into executable sub-tasks."""
        try:
            self.logger.info("Planning task", task_id=task.task_id, name=task.name)
            
            # Create execution context
            context = ExecutionContext(
                user_id=user_id,
                platform=self.settings.platform,
                preferences={"safety_level": "normal"},
                max_execution_time=300.0  # 5 minutes default
            )
            
            # Store context
            self.execution_contexts[task.task_id] = context
            
            # Decompose task
            planning_result = await self.task_decomposer.decompose_task(task, context)
            
            if planning_result.success and planning_result.plan:
                # Store active plan
                self.active_plans[task.task_id] = planning_result.plan
                
                self.logger.info(
                    "Task planning completed",
                    task_id=task.task_id,
                    subtasks=len(planning_result.plan.subtasks),
                    estimated_time=planning_result.estimated_execution_time
                )
            else:
                self.logger.error(
                    "Task planning failed",
                    task_id=task.task_id,
                    error=planning_result.error_message
                )
            
            return planning_result
            
        except Exception as e:
            self.logger.error("Task planning error", task_id=task.task_id, error=str(e))
            return PlanningResult(
                success=False,
                error_message=str(e),
                planning_time=0.0
            )
    
    async def execute_plan(self, plan_id: str) -> ExecutionResult:
        """Execute a task plan."""
        try:
            if plan_id not in self.active_plans:
                raise ValueError(f"Plan {plan_id} not found")
            
            if not self.executor_callback:
                raise RuntimeError("Executor callback not set")
            
            plan = self.active_plans[plan_id]
            context = self.execution_contexts.get(plan.task_id)
            
            if not context:
                raise ValueError(f"Execution context not found for plan {plan_id}")
            
            self.logger.info("Executing plan", plan_id=plan_id, subtasks=len(plan.subtasks))
            
            # Execute plan using scheduler
            result = await self.execution_scheduler.execute_plan(
                plan, context, self.executor_callback
            )
            
            # Update plan status
            if result.success:
                plan.planning_status = "completed"
            else:
                plan.planning_status = "failed"
            
            self.logger.info(
                "Plan execution completed",
                plan_id=plan_id,
                success=result.success,
                execution_time=result.execution_time
            )
            
            return result
            
        except Exception as e:
            self.logger.error("Plan execution error", plan_id=plan_id, error=str(e))
            return ExecutionResult(
                plan_id=plan_id,
                success=False,
                error_message=str(e),
                execution_time=0.0
            )
    
    async def plan_and_execute_task(self, task: Task, user_id: Optional[str] = None) -> ExecutionResult:
        """Plan and execute a task in one operation."""
        try:
            # Plan the task
            planning_result = await self.plan_task(task, user_id)
            
            if not planning_result.success or not planning_result.plan:
                return ExecutionResult(
                    plan_id="",
                    success=False,
                    error_message=f"Planning failed: {planning_result.error_message}",
                    execution_time=0.0
                )
            
            # Execute the plan
            execution_result = await self.execute_plan(planning_result.plan.plan_id)
            
            return execution_result
            
        except Exception as e:
            self.logger.error("Plan and execute error", task_id=task.task_id, error=str(e))
            return ExecutionResult(
                plan_id="",
                success=False,
                error_message=str(e),
                execution_time=0.0
            )
    
    async def cancel_execution(self, plan_id: str) -> bool:
        """Cancel an active execution."""
        try:
            success = await self.execution_scheduler.cancel_execution(plan_id)
            
            if plan_id in self.active_plans:
                plan = self.active_plans[plan_id]
                plan.planning_status = "cancelled"
            
            self.logger.info("Execution cancelled", plan_id=plan_id, success=success)
            return success
            
        except Exception as e:
            self.logger.error("Cancel execution error", plan_id=plan_id, error=str(e))
            return False
    
    def get_plan_status(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a task plan."""
        if plan_id in self.active_plans:
            plan = self.active_plans[plan_id]
            execution_status = self.execution_scheduler.get_execution_status(plan_id)
            
            return {
                "plan_id": plan_id,
                "task_id": plan.task_id,
                "name": plan.name,
                "status": plan.planning_status,
                "progress": plan.progress,
                "subtasks_total": len(plan.subtasks),
                "subtasks_completed": len(plan.completed_subtasks),
                "subtasks_failed": len(plan.failed_subtasks),
                "estimated_duration": plan.estimated_total_duration,
                "started_at": plan.started_at.isoformat() if plan.started_at else None,
                "completed_at": plan.completed_at.isoformat() if plan.completed_at else None,
                "execution_status": execution_status
            }
        return None
    
    def list_active_plans(self) -> List[Dict[str, Any]]:
        """List all active plans."""
        plans = []
        for plan_id, plan in self.active_plans.items():
            status = self.get_plan_status(plan_id)
            if status:
                plans.append(status)
        return plans
    
    def get_plan_details(self, plan_id: str) -> Optional[TaskPlan]:
        """Get detailed plan information."""
        return self.active_plans.get(plan_id)
    
    def get_subtask_details(self, plan_id: str, subtask_id: str) -> Optional[SubTask]:
        """Get detailed subtask information."""
        if plan_id in self.active_plans:
            plan = self.active_plans[plan_id]
            for subtask in plan.subtasks:
                if subtask.subtask_id == subtask_id:
                    return subtask
        return None
    
    async def retry_failed_subtasks(self, plan_id: str) -> bool:
        """Retry failed subtasks in a plan."""
        try:
            if plan_id not in self.active_plans:
                return False
            
            plan = self.active_plans[plan_id]
            
            # Reset failed subtasks
            for subtask_id in plan.failed_subtasks:
                subtask = next((st for st in plan.subtasks if st.subtask_id == subtask_id), None)
                if subtask and subtask.retry_count < subtask.max_retries:
                    subtask.status = "pending"
                    subtask.retry_count += 1
                    subtask.error_message = None
            
            # Clear failed subtasks list
            plan.failed_subtasks.clear()
            
            # Re-execute the plan
            context = self.execution_contexts.get(plan.task_id)
            if context and self.executor_callback:
                await self.execution_scheduler.execute_plan(plan, context, self.executor_callback)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error("Retry failed subtasks error", plan_id=plan_id, error=str(e))
            return False
    
    def cleanup_completed_plans(self, max_age_hours: int = 24) -> int:
        """Clean up old completed plans."""
        try:
            current_time = datetime.utcnow()
            plans_to_remove = []
            
            for plan_id, plan in self.active_plans.items():
                if plan.completed_at:
                    age_hours = (current_time - plan.completed_at).total_seconds() / 3600
                    if age_hours > max_age_hours:
                        plans_to_remove.append(plan_id)
            
            for plan_id in plans_to_remove:
                self.active_plans.pop(plan_id, None)
                self.execution_contexts.pop(plan_id, None)
            
            self.logger.info("Cleaned up completed plans", count=len(plans_to_remove))
            return len(plans_to_remove)
            
        except Exception as e:
            self.logger.error("Cleanup error", error=str(e))
            return 0
