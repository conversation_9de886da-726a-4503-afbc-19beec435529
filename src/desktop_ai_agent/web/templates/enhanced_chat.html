<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Desktop AI Agent - Task Control Center</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main-container {
            display: flex;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .sidebar h2 {
            margin-bottom: 20px;
            color: #ecf0f1;
            font-size: 1.2em;
        }

        .task-panel {
            flex: 1;
            overflow-y: auto;
        }

        .task-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .task-item.running {
            border-left-color: #f39c12;
            animation: pulse 2s infinite;
        }

        .task-item.completed {
            border-left-color: #27ae60;
        }

        .task-item.failed {
            border-left-color: #e74c3c;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: #34495e;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e74c3c;
            animation: blink 1s infinite;
        }

        .status-dot.connected {
            background: #27ae60;
            animation: none;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #ecf0f1;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
        }

        .message.user .message-content {
            background: #3498db;
            color: white;
        }

        .message.agent .message-content {
            background: white;
            color: #2c3e50;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .message.task .message-content {
            background: #f39c12;
            color: white;
            width: 100%;
            max-width: 100%;
        }

        .message.system .message-content {
            background: #95a5a6;
            color: white;
            width: 100%;
            max-width: 100%;
        }

        .task-progress {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 10px;
            margin-top: 10px;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.3);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            background: white;
            height: 100%;
            transition: width 0.3s ease;
        }

        .permission-request {
            background: #e67e22;
            border: 2px solid #d35400;
            padding: 20px;
            border-radius: 15px;
            margin: 10px 0;
        }

        .permission-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-approve {
            background: #27ae60;
            color: white;
        }

        .btn-deny {
            background: #e74c3c;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .chat-input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #bdc3c7;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #ecf0f1;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            border-color: #3498db;
        }

        .send-btn {
            padding: 15px 25px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .send-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 8px 15px;
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
            border: 1px solid #3498db;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .quick-action-btn:hover {
            background: #3498db;
            color: white;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 10px;
            padding: 15px 20px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 20px;
            margin-bottom: 20px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #3498db;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .task-suggestions {
            background: rgba(46, 204, 113, 0.1);
            border: 1px solid #2ecc71;
            border-radius: 15px;
            padding: 15px;
            margin: 10px 0;
        }

        .suggestion-title {
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
        }

        .suggestion-item {
            padding: 8px 12px;
            background: white;
            border-radius: 8px;
            margin: 5px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-item:hover {
            background: #2ecc71;
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar with task management -->
        <div class="sidebar">
            <h2>🎯 Active Tasks</h2>
            <div class="task-panel" id="taskPanel">
                <div class="task-item">
                    <div>No active tasks</div>
                </div>
            </div>
            
            <h2 style="margin-top: 30px;">📊 Quick Stats</h2>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                <div>Tasks Completed: <span id="tasksCompleted">0</span></div>
                <div>Success Rate: <span id="successRate">100%</span></div>
                <div>Avg Duration: <span id="avgDuration">--</span></div>
            </div>
        </div>

        <!-- Main chat area -->
        <div class="chat-area">
            <div class="chat-header">
                <h1>🤖 Desktop AI Agent - Task Control Center</h1>
                <div class="status-indicator">
                    <span class="status-dot" id="statusDot"></span>
                    <span id="statusText">Connecting...</span>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="typing-indicator" id="typingIndicator">
                    <span>🤖 Agent is thinking</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>

            <div class="chat-input-area">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="sendQuickAction('take a screenshot')">📸 Screenshot</button>
                    <button class="quick-action-btn" onclick="sendQuickAction('organize my desktop files')">📁 Organize Files</button>
                    <button class="quick-action-btn" onclick="sendQuickAction('create a new folder')">📂 New Folder</button>
                    <button class="quick-action-btn" onclick="sendQuickAction('arrange windows')">🪟 Arrange Windows</button>
                    <button class="quick-action-btn" onclick="sendQuickAction('backup my documents')">💾 Backup</button>
                </div>
                
                <div class="input-container">
                    <input type="text" id="messageInput" class="chat-input" 
                           placeholder="Type a task or question... (e.g., 'organize my desktop files', 'take a screenshot')" 
                           onkeypress="handleKeyPress(event)">
                    <button id="sendBtn" class="send-btn" onclick="sendMessage()">Send</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let userId = 'web_user_' + Math.random().toString(36).substr(2, 9);
        let activeTasks = new Map();
        let taskStats = { completed: 0, failed: 0, totalDuration: 0 };

        // Initialize WebSocket connection
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/${userId}`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                updateStatus('connected', 'Connected');
                addSystemMessage('🟢 Connected to Desktop AI Agent');
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleMessage(data);
            };
            
            ws.onclose = function() {
                updateStatus('disconnected', 'Disconnected');
                addSystemMessage('🔴 Connection lost. Attempting to reconnect...');
                setTimeout(initWebSocket, 3000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
                updateStatus('error', 'Connection Error');
            };
        }

        // Handle incoming messages
        function handleMessage(data) {
            hideTypingIndicator();
            
            switch(data.type) {
                case 'agent_message':
                    addMessage('agent', data.content);
                    break;
                    
                case 'task_detected':
                    addTaskMessage(data.content, 'detected');
                    break;
                    
                case 'task_started':
                    addTaskMessage(data.content, 'started');
                    addTaskToSidebar(data.task_id, data.content, 'running');
                    activeTasks.set(data.task_id, { status: 'running', name: data.content });
                    break;
                    
                case 'task_progress':
                    updateTaskProgress(data.task_id, data.progress, data.content);
                    break;
                    
                case 'task_completed':
                    addTaskMessage('✅ ' + data.content, 'completed');
                    if (data.results) {
                        addTaskResults(data.results);
                    }
                    updateTaskInSidebar(data.task_id, 'completed');
                    updateTaskStats('completed');
                    break;
                    
                case 'task_failed':
                    addTaskMessage('❌ ' + data.content, 'failed');
                    updateTaskInSidebar(data.task_id, 'failed');
                    updateTaskStats('failed');
                    break;
                    
                case 'permission_request':
                    addPermissionRequest(data);
                    break;
                    
                case 'typing':
                    showTypingIndicator();
                    break;
                    
                default:
                    addMessage('agent', data.content || 'Unknown message type');
            }
            
            scrollToBottom();
        }

        // UI Functions
        function updateStatus(status, text) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            statusDot.className = 'status-dot';
            if (status === 'connected') {
                statusDot.classList.add('connected');
            }
            
            statusText.textContent = text;
        }

        function addMessage(sender, content) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${content}
                    <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;">
                        ${new Date().toLocaleTimeString()}
                    </div>
                </div>
            `;
            
            messagesDiv.appendChild(messageDiv);
        }

        function addTaskMessage(content, type) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message task`;
            
            let bgColor = '#f39c12';
            if (type === 'completed') bgColor = '#27ae60';
            if (type === 'failed') bgColor = '#e74c3c';
            if (type === 'detected') bgColor = '#3498db';
            
            messageDiv.innerHTML = `
                <div class="message-content" style="background: ${bgColor};">
                    ${content}
                    <div style="font-size: 12px; opacity: 0.8; margin-top: 5px;">
                        ${new Date().toLocaleTimeString()}
                    </div>
                </div>
            `;
            
            messagesDiv.appendChild(messageDiv);
        }

        function addTaskResults(results) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message system`;
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    <strong>📊 Task Results:</strong><br>
                    <strong>Duration:</strong> ${results.duration}<br>
                    <strong>Details:</strong><br>
                    ${results.details.replace(/\n/g, '<br>')}
                </div>
            `;
            
            messagesDiv.appendChild(messageDiv);
        }

        function addSystemMessage(content) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message system`;
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${content}
                    <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;">
                        ${new Date().toLocaleTimeString()}
                    </div>
                </div>
            `;
            
            messagesDiv.appendChild(messageDiv);
        }

        function addPermissionRequest(data) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message system`;
            
            messageDiv.innerHTML = `
                <div class="permission-request">
                    <strong>🔒 Permission Required</strong><br>
                    ${data.content}<br>
                    <strong>Risk Level:</strong> ${data.risk_level}<br>
                    <strong>Operation:</strong> ${data.details.operation}<br>
                    <div class="permission-buttons">
                        <button class="btn btn-approve" onclick="respondToPermission('${data.permission_id}', true)">
                            ✅ Approve
                        </button>
                        <button class="btn btn-deny" onclick="respondToPermission('${data.permission_id}', false)">
                            ❌ Deny
                        </button>
                    </div>
                </div>
            `;
            
            messagesDiv.appendChild(messageDiv);
        }

        function addTaskToSidebar(taskId, name, status) {
            const taskPanel = document.getElementById('taskPanel');
            
            // Remove "No active tasks" message
            if (taskPanel.children.length === 1 && taskPanel.children[0].textContent === 'No active tasks') {
                taskPanel.innerHTML = '';
            }
            
            const taskDiv = document.createElement('div');
            taskDiv.className = `task-item ${status}`;
            taskDiv.id = `task-${taskId}`;
            
            taskDiv.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 5px;">${name}</div>
                <div style="font-size: 12px; opacity: 0.8;">Status: ${status}</div>
                <div class="task-progress" id="progress-${taskId}" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div style="font-size: 12px; margin-top: 5px;">0% complete</div>
                </div>
            `;
            
            taskPanel.appendChild(taskDiv);
        }

        function updateTaskProgress(taskId, progress, content) {
            const progressDiv = document.getElementById(`progress-${taskId}`);
            if (progressDiv) {
                progressDiv.style.display = 'block';
                const progressFill = progressDiv.querySelector('.progress-fill');
                const progressText = progressDiv.querySelector('div:last-child');
                
                progressFill.style.width = `${progress * 100}%`;
                progressText.textContent = `${Math.round(progress * 100)}% complete`;
            }
            
            // Also add progress message to chat
            addTaskMessage(content, 'progress');
        }

        function updateTaskInSidebar(taskId, status) {
            const taskDiv = document.getElementById(`task-${taskId}`);
            if (taskDiv) {
                taskDiv.className = `task-item ${status}`;
                const statusText = taskDiv.querySelector('div:nth-child(2)');
                statusText.textContent = `Status: ${status}`;
                
                // Hide progress bar when completed/failed
                const progressDiv = document.getElementById(`progress-${taskId}`);
                if (progressDiv && (status === 'completed' || status === 'failed')) {
                    progressDiv.style.display = 'none';
                }
            }
        }

        function updateTaskStats(result) {
            if (result === 'completed') {
                taskStats.completed++;
            } else if (result === 'failed') {
                taskStats.failed++;
            }
            
            const total = taskStats.completed + taskStats.failed;
            const successRate = total > 0 ? Math.round((taskStats.completed / total) * 100) : 100;
            
            document.getElementById('tasksCompleted').textContent = taskStats.completed;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'flex';
            scrollToBottom();
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        function scrollToBottom() {
            const messagesDiv = document.getElementById('chatMessages');
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Message sending functions
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message && ws && ws.readyState === WebSocket.OPEN) {
                // Add user message to chat
                addMessage('user', message);
                
                // Send to server
                ws.send(JSON.stringify({
                    type: 'user_message',
                    content: message,
                    user_id: userId
                }));
                
                // Clear input and show typing indicator
                input.value = '';
                showTypingIndicator();
                
                // Disable send button temporarily
                const sendBtn = document.getElementById('sendBtn');
                sendBtn.disabled = true;
                setTimeout(() => {
                    sendBtn.disabled = false;
                }, 2000);
            }
        }

        function sendQuickAction(action) {
            document.getElementById('messageInput').value = action;
            sendMessage();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function respondToPermission(permissionId, approved) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'permission_response',
                    permission_id: permissionId,
                    approved: approved,
                    user_id: userId
                }));
                
                addSystemMessage(approved ? 
                    '✅ Permission granted. Task will proceed.' : 
                    '❌ Permission denied. Task cancelled.'
                );
            }
        }

        // Initialize when page loads
        window.onload = function() {
            initWebSocket();
            
            // Add welcome message
            setTimeout(() => {
                addSystemMessage('🚀 Welcome to Desktop AI Agent Task Control Center!');
                addSystemMessage('💡 Try commands like: "take a screenshot", "organize my desktop files", "create a new folder"');
            }, 1000);
        };
    </script>
</body>
</html>
