"""
Web-based Chat Interface for the Desktop AI Agent.

This module provides a modern web interface for conversational interaction
with the AI agent, including real-time chat and learning management.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import <PERSON><PERSON>2<PERSON>emplates
from fastapi.responses import HTMLResponse
import structlog

from ..conversation.engine import ConversationEngine
from ..conversation.models import MessageType, ConversationLanguage
from ..learning.engine import LearningEngine
from ..ai.models import InferenceRequest
from ..core.orchestrator import Task, TaskPriority
from ..desktop.models import DesktopAction
from ..safety.models import Permission


class WebChatInterface:
    """Web-based chat interface for the Desktop AI Agent."""
    
    def __init__(self, conversation_engine: ConversationEngine, learning_engine: LearningEngine, orchestrator=None, safety_engine=None, desktop_engine=None):
        self.conversation_engine = conversation_engine
        self.learning_engine = learning_engine
        self.orchestrator = orchestrator
        self.safety_engine = safety_engine
        self.desktop_engine = desktop_engine
        self.logger = structlog.get_logger(__name__)

        # Active WebSocket connections
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, str] = {}  # websocket_id -> session_id

        # Task and permission tracking
        self.active_tasks: Dict[str, str] = {}  # task_id -> user_id mapping
        self.pending_permissions: Dict[str, str] = {}  # permission_id -> user_id mapping
        
        # Initialize FastAPI app
        self.app = FastAPI(title="Desktop AI Agent Chat Interface")
        self._setup_routes()
        
        # Setup static files and templates
        static_dir = Path(__file__).parent / "static"
        templates_dir = Path(__file__).parent / "templates"
        
        if static_dir.exists():
            self.app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
        
        if templates_dir.exists():
            self.templates = Jinja2Templates(directory=str(templates_dir))
        else:
            self.templates = None
    
    def _setup_routes(self):
        """Setup FastAPI routes."""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def chat_page(request: Request):
            """Serve the main chat interface."""
            if self.templates:
                return self.templates.TemplateResponse("enhanced_chat.html", {"request": request})
            else:
                return HTMLResponse(self._get_enhanced_chat_html())
        
        @self.app.websocket("/ws/{user_id}")
        async def websocket_endpoint(websocket: WebSocket, user_id: str):
            """WebSocket endpoint for real-time chat."""
            await self._handle_websocket_connection(websocket, user_id)
        
        @self.app.get("/api/learning/status")
        async def learning_status():
            """Get learning system status."""
            return await self.learning_engine.get_learning_statistics()
        
        @self.app.get("/api/learning/proposals")
        async def get_proposals():
            """Get pending improvement proposals."""
            proposals = await self.learning_engine.get_pending_proposals()
            return [
                {
                    "id": p.proposal_id,
                    "title": p.title,
                    "description": p.description,
                    "status": p.status.value,
                    "timeline": p.estimated_timeline,
                    "risks": p.risks
                }
                for p in proposals
            ]
        
        @self.app.post("/api/learning/proposals/{proposal_id}/approve")
        async def approve_proposal(proposal_id: str):
            """Approve an improvement proposal."""
            success = await self.learning_engine.approve_proposal(proposal_id, "web_user")
            return {"success": success}
        
        @self.app.post("/api/learning/proposals/{proposal_id}/reject")
        async def reject_proposal(proposal_id: str, reason: str = "No reason provided"):
            """Reject an improvement proposal."""
            success = await self.learning_engine.reject_proposal(proposal_id, reason)
            return {"success": success}
    
    async def _handle_websocket_connection(self, websocket: WebSocket, user_id: str):
        """Handle WebSocket connection for real-time chat."""
        await websocket.accept()
        connection_id = f"{user_id}_{datetime.now().timestamp()}"
        self.active_connections[connection_id] = websocket
        
        try:
            # Start conversation session
            session = await self.conversation_engine.start_conversation(
                user_id=user_id,
                platform="web",
                voice_enabled=False
            )
            
            self.user_sessions[connection_id] = session.session_id
            
            # Send welcome message
            if session.messages:
                welcome_msg = session.messages[0]
                await self._send_message(websocket, {
                    "type": "agent_message",
                    "content": welcome_msg.content,
                    "timestamp": welcome_msg.timestamp.isoformat(),
                    "language": welcome_msg.language.value if welcome_msg.language else "auto"
                })
            
            # Listen for messages
            while True:
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                await self._process_websocket_message(websocket, connection_id, message_data)
                
        except WebSocketDisconnect:
            self.logger.info("WebSocket disconnected", user_id=user_id)
        except Exception as e:
            self.logger.error("WebSocket error", error=str(e))
        finally:
            # Cleanup
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
            
            if connection_id in self.user_sessions:
                session_id = self.user_sessions[connection_id]
                await self.conversation_engine.end_conversation(session_id)
                del self.user_sessions[connection_id]
    
    async def _process_websocket_message(
        self,
        websocket: WebSocket,
        connection_id: str,
        message_data: Dict[str, Any]
    ):
        """Process a message received via WebSocket with task execution support."""
        try:
            message_type = message_data.get("type", "user_message")
            content = message_data.get("content", "")
            user_id = message_data.get("user_id", "web_user")

            if message_type == "user_message" and content:
                session_id = self.user_sessions.get(connection_id)
                if not session_id:
                    return

                # Send typing indicator
                await self._send_message(websocket, {
                    "type": "typing",
                    "content": "Agent is analyzing your request..."
                })

                # First, analyze if this is a task request
                task_intent = await self._analyze_task_intent(content)

                if task_intent and task_intent.get('is_task', False) and self.orchestrator:
                    # Handle as executable task
                    await self._handle_task_request(websocket, user_id, content, task_intent, session_id)
                else:
                    # Handle as regular conversation
                    await self._handle_conversation(websocket, session_id, content)

                # Check for learning updates
                await self._send_learning_updates(websocket)

            elif message_type == "permission_response":
                # Handle permission approval/denial
                await self._handle_permission_response(websocket, user_id, message_data)

            elif message_type == "task_cancel":
                # Handle task cancellation
                await self._handle_task_cancellation(websocket, user_id, message_data)

            elif message_type == "feedback":
                # Handle user feedback
                rating = message_data.get("rating")
                comment = message_data.get("comment", "")
                
                if rating:
                    # Record feedback for learning
                    from ..learning.models import PerformanceMetric
                    metric = PerformanceMetric(
                        metric_name="user_satisfaction",
                        value=float(rating),
                        unit="stars",
                        task_type="conversation",
                        user_id=message_data.get("user_id", "web_user")
                    )
                    
                    await self.learning_engine.record_performance_metric(metric)
                    
                    await self._send_message(websocket, {
                        "type": "feedback_received",
                        "content": f"Thank you for the {rating}-star rating!"
                    })
            
        except Exception as e:
            self.logger.error("Error processing WebSocket message", error=str(e))
            await self._send_message(websocket, {
                "type": "error",
                "content": "Sorry, I encountered an error processing your message."
            })
    
    async def _send_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Send a message via WebSocket."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            self.logger.error("Error sending WebSocket message", error=str(e))
    
    async def _send_learning_updates(self, websocket: WebSocket):
        """Send learning system updates to the client."""
        try:
            # Check for new proposals
            proposals = await self.learning_engine.get_pending_proposals()
            
            if proposals:
                await self._send_message(websocket, {
                    "type": "learning_update",
                    "content": f"I have {len(proposals)} new improvement proposals!",
                    "proposals_count": len(proposals)
                })
        
        except Exception as e:
            self.logger.error("Error sending learning updates", error=str(e))

    async def _analyze_task_intent(self, content: str) -> Dict[str, Any]:
        """Analyze if the user message contains a task request."""
        # Task keywords and patterns
        task_keywords = [
            'organize', 'screenshot', 'capture', 'move', 'copy', 'delete', 'create',
            'folder', 'file', 'window', 'desktop', 'arrange', 'sort', 'clean',
            'backup', 'compress', 'extract', 'rename', 'search', 'find'
        ]

        action_patterns = [
            r'take a screenshot',
            r'organize (my )?desktop',
            r'create (a )?folder',
            r'move (.*) to (.*)',
            r'copy (.*) to (.*)',
            r'delete (.*)',
            r'find (.*)',
            r'search for (.*)',
            r'backup (.*)',
            r'compress (.*)',
            r'extract (.*)',
            r'rename (.*) to (.*)',
            r'arrange windows',
            r'close (all )?windows',
            r'minimize (all )?windows'
        ]

        content_lower = content.lower()

        # Check for task keywords
        has_task_keyword = any(keyword in content_lower for keyword in task_keywords)

        # Check for action patterns
        import re
        has_action_pattern = any(re.search(pattern, content_lower) for pattern in action_patterns)

        if has_task_keyword or has_action_pattern:
            # Determine task type
            task_type = self._determine_task_type(content_lower)

            return {
                'is_task': True,
                'task_type': task_type,
                'confidence': 0.8 if has_action_pattern else 0.6,
                'original_request': content
            }

        return {'is_task': False}

    def _determine_task_type(self, content: str) -> str:
        """Determine the specific type of task based on content."""
        if any(word in content for word in ['screenshot', 'capture', 'screen']):
            return 'screenshot'
        elif any(word in content for word in ['organize', 'sort', 'arrange', 'clean']):
            return 'file_organization'
        elif any(word in content for word in ['folder', 'directory', 'create']):
            return 'folder_management'
        elif any(word in content for word in ['window', 'minimize', 'close', 'arrange']):
            return 'window_management'
        elif any(word in content for word in ['move', 'copy', 'transfer']):
            return 'file_operations'
        elif any(word in content for word in ['search', 'find', 'locate']):
            return 'file_search'
        elif any(word in content for word in ['backup', 'compress', 'archive']):
            return 'backup_operations'
        else:
            return 'general_automation'

    async def _handle_task_request(self, websocket: WebSocket, user_id: str, content: str, task_intent: Dict[str, Any], session_id: str):
        """Handle a task execution request."""
        try:
            task_type = task_intent['task_type']

            # Send task analysis result
            await self._send_message(websocket, {
                "type": "task_detected",
                "content": f"🎯 Task detected: {task_type.replace('_', ' ').title()}",
                "task_type": task_type,
                "confidence": task_intent['confidence']
            })

            # For very simple tasks, skip AI and use direct rule-based approach
            if task_type in ['screenshot', 'create_folder'] and self._is_simple_request(content):
                self.logger.info(f"Using fast rule-based execution for {task_type}")
                task_params = self._generate_fallback_parameters(content, task_type)
            else:
                # Use AI for complex tasks
                task_params = await self._generate_task_parameters(content, task_intent)

            # Check if task requires safety approval
            if self.safety_engine and await self._requires_safety_approval(task_params):
                await self._request_safety_approval(websocket, user_id, task_params)
                return

            # Execute task directly
            await self._execute_task(websocket, user_id, task_params, session_id)

        except Exception as e:
            self.logger.error("Error handling task request", error=str(e))
            await self._send_message(websocket, {
                "type": "task_error",
                "content": f"❌ Error processing task: {str(e)}"
            })

    def _is_simple_request(self, content: str) -> bool:
        """Check if request is simple enough to skip AI processing."""
        content_lower = content.lower()
        simple_patterns = [
            'take a screenshot',
            'take screenshot',
            'screenshot',
            'create folder',
            'create a folder',
            'new folder',
            'make folder'
        ]
        return any(pattern in content_lower for pattern in simple_patterns)

    async def _handle_conversation(self, websocket: WebSocket, session_id: str, content: str):
        """Handle regular conversation (non-task)."""
        try:
            # Process message through conversation engine
            response = await self.conversation_engine.process_user_message(
                session_id,
                content,
                MessageType.USER_TEXT
            )

            # Send response
            await self._send_message(websocket, {
                "type": "agent_message",
                "content": response.content,
                "timestamp": response.timestamp.isoformat(),
                "language": response.language.value if response.language else "auto",
                "metadata": response.metadata
            })

        except Exception as e:
            self.logger.error("Error handling conversation", error=str(e))
            await self._send_message(websocket, {
                "type": "error",
                "content": f"Error processing message: {str(e)}"
            })

    async def _generate_task_parameters(self, content: str, task_intent: Dict[str, Any]) -> Dict[str, Any]:
        """Generate detailed task parameters using AI."""
        task_type = task_intent['task_type']

        # Use a faster AI model for task parameter generation
        if hasattr(self.conversation_engine, 'ai_engine'):
            # Try TinyLlama first (much faster - 1.1B vs 7B parameters)
            fast_models = [
                'local_tinyllama-1.1b-chat-v1.0.Q4_K_M',  # Fastest option
                'local_tinyllama-1.1b-chat.Q4_K_M',       # Alternative TinyLlama
                'local_phi-2.Q4_K_M',                      # Phi-2 is also fast
                'local_phi-3-mini-4k.Q4_K_M',             # Phi-3 mini
                'local_mistral-7b-v0.1.Q4_K_M',           # Mistral fallback
                'local_codellama-7b-instruct.Q4_K_M'      # Last resort
            ]

            # Simplified prompt for faster models
            ai_prompt = f"""Task: {task_type}
Request: "{content}"

Return JSON only:
{{"action": "action_name", "parameters": {{}}, "description": "task description", "estimated_duration": 10}}

Examples:
screenshot -> {{"action": "take_screenshot", "parameters": {{"format": "PNG"}}, "description": "Take screenshot", "estimated_duration": 5}}
organize -> {{"action": "organize_files", "parameters": {{"organize_by": "type"}}, "description": "Organize files", "estimated_duration": 20}}
folder -> {{"action": "create_folder", "parameters": {{"folder_name": "New Folder"}}, "description": "Create folder", "estimated_duration": 3}}

JSON:"""

            for model_id in fast_models:
                try:
                    ai_response = await self.conversation_engine.ai_engine.generate_response(
                        InferenceRequest(
                            model_id=model_id,
                            prompt=ai_prompt,
                            max_tokens=150,
                            temperature=0.1  # Lower temperature for more consistent JSON
                        )
                    )

                    if ai_response.success:
                        import json
                        import re

                        # Extract JSON from AI response
                        json_match = re.search(r'\{.*\}', ai_response.text, re.DOTALL)
                        if json_match:
                            try:
                                result = json.loads(json_match.group())
                                self.logger.info(f"Task parameters generated using {model_id}")
                                return result
                            except json.JSONDecodeError:
                                continue  # Try next model

                except Exception as e:
                    self.logger.warning(f"Model {model_id} failed", error=str(e))
                    continue  # Try next model

        # Fallback to rule-based parameter generation
        self.logger.info("Using fallback rule-based parameter generation")
        return self._generate_fallback_parameters(content, task_type)

    def _generate_fallback_parameters(self, content: str, task_type: str) -> Dict[str, Any]:
        """Generate task parameters using enhanced rule-based approach."""
        import os
        import re
        from datetime import datetime

        # Extract specific details from user content
        content_lower = content.lower()

        base_params = {
            "action": task_type,
            "description": f"Execute {task_type.replace('_', ' ')} task",
            "estimated_duration": 15
        }

        if task_type == 'screenshot':
            # Check for specific screenshot requests
            filename = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            if 'window' in content_lower:
                action = "take_window_screenshot"
            elif 'region' in content_lower or 'area' in content_lower:
                action = "take_region_screenshot"
            else:
                action = "take_screenshot"

            base_params.update({
                "action": action,
                "parameters": {
                    "format": "PNG",
                    "save_path": os.path.expanduser("~/Desktop"),
                    "filename": filename,
                    "quality": 95
                },
                "description": f"Take a screenshot and save as {filename}",
                "estimated_duration": 5
            })

        elif task_type == 'file_organization':
            # Extract path if mentioned
            source_path = os.path.expanduser("~/Desktop")
            if 'documents' in content_lower:
                source_path = os.path.expanduser("~/Documents")
            elif 'downloads' in content_lower:
                source_path = os.path.expanduser("~/Downloads")

            base_params.update({
                "action": "organize_files",
                "parameters": {
                    "source_path": source_path,
                    "create_folders": ["Documents", "Images", "Videos", "Archives", "Others"],
                    "organize_by": "file_type",
                    "backup_before": True
                },
                "description": f"Organize files in {source_path} by type",
                "estimated_duration": 30
            })

        elif task_type == 'folder_management':
            # Extract folder name if mentioned
            folder_name = "New Folder"
            name_match = re.search(r'(?:folder|directory).*?["\']([^"\']+)["\']', content, re.IGNORECASE)
            if name_match:
                folder_name = name_match.group(1)
            elif 'called' in content_lower:
                parts = content_lower.split('called')
                if len(parts) > 1:
                    folder_name = parts[1].strip().strip('"\'')

            base_params.update({
                "action": "create_folder",
                "parameters": {
                    "path": os.path.expanduser("~/Desktop"),
                    "folder_name": folder_name,
                    "create_parents": True
                },
                "description": f"Create folder '{folder_name}'",
                "estimated_duration": 3
            })

        elif task_type == 'window_management':
            operation = "arrange"
            if 'minimize' in content_lower:
                operation = "minimize_all"
            elif 'maximize' in content_lower:
                operation = "maximize_all"
            elif 'close' in content_lower:
                operation = "close_all"
            elif 'tile' in content_lower:
                operation = "tile"

            base_params.update({
                "action": "manage_windows",
                "parameters": {
                    "operation": operation,
                    "layout": "auto",
                    "exclude_current": True
                },
                "description": f"Window management: {operation}",
                "estimated_duration": 10
            })

        elif task_type == 'file_operations':
            # Detect move/copy operations
            if 'move' in content_lower:
                action = "move_files"
            elif 'copy' in content_lower:
                action = "copy_files"
            else:
                action = "file_operation"

            base_params.update({
                "action": action,
                "parameters": {
                    "source_pattern": "*",
                    "destination": os.path.expanduser("~/Desktop"),
                    "create_destination": True
                },
                "description": f"File operation: {action}",
                "estimated_duration": 20
            })

        elif task_type == 'file_search':
            # Extract search term
            search_term = "file"
            if 'find' in content_lower:
                parts = content_lower.split('find')
                if len(parts) > 1:
                    search_term = parts[1].strip()

            base_params.update({
                "action": "search_files",
                "parameters": {
                    "search_term": search_term,
                    "search_path": os.path.expanduser("~"),
                    "max_results": 50
                },
                "description": f"Search for '{search_term}'",
                "estimated_duration": 15
            })

        return base_params

    async def _requires_safety_approval(self, task_params: Dict[str, Any]) -> bool:
        """Check if task requires safety approval."""
        high_risk_actions = [
            'delete', 'remove', 'format', 'system_command',
            'registry_edit', 'service_control', 'network_config'
        ]

        action = task_params.get('action', '').lower()
        parameters = task_params.get('parameters', {})

        # Check for high-risk actions
        if any(risk_action in action for risk_action in high_risk_actions):
            return True

        # Check for system paths
        for param_value in parameters.values():
            if isinstance(param_value, str):
                if any(sys_path in param_value.lower() for sys_path in ['/system', '/windows', 'c:\\windows', '/usr/bin']):
                    return True

        return False

    async def _request_safety_approval(self, websocket: WebSocket, user_id: str, task_params: Dict[str, Any]):
        """Request safety approval for a task."""
        try:
            # Create permission request
            permission = await self.safety_engine.request_permission(
                operation=task_params.get('action', 'unknown'),
                resource=str(task_params.get('parameters', {})),
                justification=f"User requested: {task_params.get('description', 'Unknown task')}"
            )

            if permission:
                self.pending_permissions[permission.permission_id] = user_id

                # Send permission request to user
                await self._send_message(websocket, {
                    "type": "permission_request",
                    "permission_id": permission.permission_id,
                    "content": f"🔒 Safety approval required for: {task_params.get('description')}",
                    "risk_level": permission.risk_level.value,
                    "details": {
                        "operation": permission.operation,
                        "resource": permission.resource,
                        "justification": permission.justification
                    }
                })

        except Exception as e:
            self.logger.error("Error requesting safety approval", error=str(e))
            await self._send_message(websocket, {
                "type": "task_error",
                "content": f"❌ Safety check failed: {str(e)}"
            })

    async def _execute_task(self, websocket: WebSocket, user_id: str, task_params: Dict[str, Any], session_id: str):
        """Execute a task and provide real-time updates."""
        try:
            action = task_params.get('action', '')

            # For simple tasks, execute directly without orchestrator overhead
            if action in ['take_screenshot', 'create_folder'] and self._is_simple_task(task_params):
                await self._execute_simple_task(websocket, user_id, task_params)
                return

            # For complex tasks, use orchestrator
            task = Task(
                name=task_params.get('description', 'Desktop Automation Task'),
                description=task_params.get('description', ''),
                type='desktop_automation',
                priority=TaskPriority.NORMAL,
                parameters=task_params.get('parameters', {})
            )

            # Submit task
            task_id = await self.orchestrator.submit_task(task)
            self.active_tasks[task_id] = user_id

            # Send task started notification
            await self._send_message(websocket, {
                "type": "task_started",
                "task_id": task_id,
                "content": f"🚀 Task started: {task.name}",
                "estimated_duration": task_params.get('estimated_duration', 30)
            })

            # Monitor task progress
            await self._monitor_task_progress(websocket, task_id, session_id)

        except Exception as e:
            self.logger.error("Error executing task", error=str(e))
            await self._send_message(websocket, {
                "type": "task_error",
                "content": f"❌ Task execution failed: {str(e)}"
            })

    def _is_simple_task(self, task_params: Dict[str, Any]) -> bool:
        """Check if task can be executed directly without orchestrator."""
        action = task_params.get('action', '')
        simple_actions = [
            'take_screenshot', 'create_folder', 'search_files'
        ]
        return action in simple_actions

    async def _execute_simple_task(self, websocket: WebSocket, user_id: str, task_params: Dict[str, Any]):
        """Execute simple tasks directly for faster response."""
        import asyncio
        import os
        from pathlib import Path

        action = task_params.get('action', '')
        parameters = task_params.get('parameters', {})
        description = task_params.get('description', 'Task')

        try:
            # Send task started
            await self._send_message(websocket, {
                "type": "task_started",
                "content": f"🚀 Executing: {description}",
                "estimated_duration": task_params.get('estimated_duration', 10)
            })

            # Simulate progress
            await self._send_message(websocket, {
                "type": "task_progress",
                "progress": 0.3,
                "content": "📊 Task 30% complete"
            })

            result = {}

            if action == 'take_screenshot':
                # Execute screenshot
                save_path = parameters.get('save_path', os.path.expanduser('~/Desktop'))
                filename = parameters.get('filename', 'screenshot.png')
                full_path = os.path.join(save_path, filename)

                # Use desktop engine if available
                if self.desktop_engine:
                    try:
                        screenshot_result = await self.desktop_engine.take_screenshot(save_path=save_path, filename=filename)
                        if screenshot_result and screenshot_result.success:
                            result = {
                                'screenshot_path': screenshot_result.file_path,
                                'file_size': os.path.getsize(screenshot_result.file_path) if os.path.exists(screenshot_result.file_path) else 0
                            }
                        else:
                            raise Exception("Desktop engine screenshot failed")
                    except Exception as de_error:
                        self.logger.warning(f"Desktop engine failed: {de_error}, trying fallback")
                        raise de_error
                else:
                    # Fallback: use system command
                    import subprocess
                    try:
                        subprocess.run(['gnome-screenshot', '-f', full_path], check=True, timeout=10)
                        result = {'screenshot_path': full_path}
                    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                        # Try alternative screenshot methods
                        try:
                            subprocess.run(['scrot', full_path], check=True, timeout=10)
                            result = {'screenshot_path': full_path}
                        except:
                            raise Exception("Screenshot tools not available")

            elif action == 'create_folder':
                # Execute folder creation
                path = parameters.get('path', os.path.expanduser('~/Desktop'))
                folder_name = parameters.get('folder_name', 'New Folder')
                full_path = os.path.join(path, folder_name)

                Path(full_path).mkdir(parents=True, exist_ok=True)
                result = {
                    'folder_path': full_path,
                    'folder_created': True
                }

            elif action == 'search_files':
                # Execute file search
                search_term = parameters.get('search_term', '')
                search_path = parameters.get('search_path', os.path.expanduser('~'))
                max_results = parameters.get('max_results', 10)

                found_files = []
                for root, dirs, files in os.walk(search_path):
                    for file in files:
                        if search_term.lower() in file.lower():
                            found_files.append(os.path.join(root, file))
                            if len(found_files) >= max_results:
                                break
                    if len(found_files) >= max_results:
                        break

                result = {
                    'found_files': found_files,
                    'files_found': len(found_files)
                }

            # Send progress update
            await self._send_message(websocket, {
                "type": "task_progress",
                "progress": 0.9,
                "content": "📊 Task 90% complete"
            })

            # Send completion
            await self._send_message(websocket, {
                "type": "task_completed",
                "content": f"✅ {description} completed successfully!",
                "results": {
                    "description": description,
                    "duration": "< 1 second",
                    "details": self._format_task_results(action, result)
                }
            })

        except Exception as e:
            self.logger.error("Simple task execution failed", error=str(e))
            await self._send_message(websocket, {
                "type": "task_failed",
                "content": f"❌ Task failed: {str(e)}"
            })

    def _format_task_results(self, action: str, result: Dict[str, Any]) -> str:
        """Format task results for display."""
        if action == 'take_screenshot':
            path = result.get('screenshot_path', 'Unknown')
            size = result.get('file_size', 0)
            return f"📸 Screenshot saved to: {path}\n💾 File size: {size} bytes"

        elif action == 'create_folder':
            path = result.get('folder_path', 'Unknown')
            return f"📂 Folder created: {path}"

        elif action == 'search_files':
            count = result.get('files_found', 0)
            files = result.get('found_files', [])
            details = f"🔍 Found {count} files"
            if files:
                details += f"\n📄 First few results:\n" + "\n".join(files[:3])
                if len(files) > 3:
                    details += f"\n... and {len(files) - 3} more"
            return details

        return "Task completed successfully"

    async def _monitor_task_progress(self, websocket: WebSocket, task_id: str, session_id: str):
        """Monitor task progress and send updates."""
        import asyncio

        try:
            max_wait_time = 300  # 5 minutes max
            check_interval = 2   # Check every 2 seconds
            elapsed_time = 0

            while elapsed_time < max_wait_time:
                # Get task status
                task_status = self.orchestrator.get_task_status(task_id)

                if task_status:
                    # Send progress update
                    await self._send_message(websocket, {
                        "type": "task_progress",
                        "task_id": task_id,
                        "status": task_status.status.value,
                        "progress": task_status.progress,
                        "content": f"📊 Task {task_status.status.value}: {task_status.progress:.1%} complete"
                    })

                    if task_status.status.value == 'completed':
                        # Task completed successfully
                        result_summary = await self._generate_task_result_summary(task_status)

                        await self._send_message(websocket, {
                            "type": "task_completed",
                            "task_id": task_id,
                            "content": f"✅ Task completed successfully!",
                            "results": result_summary
                        })

                        # Also send as conversation message for context
                        await self._send_message(websocket, {
                            "type": "agent_message",
                            "content": f"✅ I've successfully completed your task: {result_summary.get('description', 'Desktop automation task')}.\n\n{result_summary.get('details', '')}",
                            "timestamp": datetime.utcnow().isoformat(),
                            "language": "auto"
                        })
                        break

                    elif task_status.status.value == 'failed':
                        # Task failed
                        await self._send_message(websocket, {
                            "type": "task_failed",
                            "task_id": task_id,
                            "content": f"❌ Task failed: {task_status.error_message or 'Unknown error'}",
                            "error": task_status.error_message
                        })
                        break

                await asyncio.sleep(check_interval)
                elapsed_time += check_interval

            # Cleanup
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]

        except Exception as e:
            self.logger.error("Error monitoring task progress", error=str(e), task_id=task_id)

    async def _generate_task_result_summary(self, task_status) -> Dict[str, Any]:
        """Generate a summary of task results."""
        try:
            result = task_status.result or {}

            summary = {
                "description": task_status.task_name or "Desktop automation task",
                "duration": f"{task_status.execution_time_ms / 1000:.1f} seconds" if task_status.execution_time_ms else "Unknown",
                "details": ""
            }

            # Generate details based on result
            if isinstance(result, dict):
                details = []

                if 'files_processed' in result:
                    details.append(f"📁 Processed {result['files_processed']} files")

                if 'folders_created' in result:
                    details.append(f"📂 Created {result['folders_created']} folders")

                if 'screenshot_path' in result:
                    details.append(f"📸 Screenshot saved to: {result['screenshot_path']}")

                if 'windows_managed' in result:
                    details.append(f"🪟 Managed {result['windows_managed']} windows")

                if 'bytes_processed' in result:
                    size_mb = result['bytes_processed'] / (1024 * 1024)
                    details.append(f"💾 Processed {size_mb:.1f} MB of data")

                summary["details"] = "\n".join(details) if details else "Task completed successfully"

            return summary

        except Exception as e:
            self.logger.error("Error generating task summary", error=str(e))
            return {
                "description": "Desktop automation task",
                "duration": "Unknown",
                "details": "Task completed"
            }

    async def _handle_permission_response(self, websocket: WebSocket, user_id: str, message_data: Dict[str, Any]):
        """Handle user response to permission request."""
        try:
            permission_id = message_data.get('permission_id')
            approved = message_data.get('approved', False)

            if not permission_id or permission_id not in self.pending_permissions:
                await self._send_message(websocket, {
                    "type": "error",
                    "content": "❌ Invalid permission request"
                })
                return

            if approved:
                # User approved - execute the task
                await self._send_message(websocket, {
                    "type": "permission_approved",
                    "content": "✅ Permission granted. Executing task..."
                })

                # Here you would execute the originally requested task
                # For now, we'll send a placeholder
                await self._send_message(websocket, {
                    "type": "task_completed",
                    "content": "✅ Task executed with user approval"
                })
            else:
                # User denied
                await self._send_message(websocket, {
                    "type": "permission_denied",
                    "content": "🚫 Task cancelled by user"
                })

            # Cleanup
            del self.pending_permissions[permission_id]

        except Exception as e:
            self.logger.error("Error handling permission response", error=str(e))

    async def _handle_task_cancellation(self, websocket: WebSocket, user_id: str, message_data: Dict[str, Any]):
        """Handle task cancellation request."""
        try:
            task_id = message_data.get('task_id')

            if task_id and task_id in self.active_tasks:
                # Cancel the task
                success = await self.orchestrator.cancel_task(task_id)

                if success:
                    await self._send_message(websocket, {
                        "type": "task_cancelled",
                        "content": "🛑 Task cancelled successfully"
                    })
                else:
                    await self._send_message(websocket, {
                        "type": "error",
                        "content": "❌ Failed to cancel task"
                    })

                # Cleanup
                del self.active_tasks[task_id]
            else:
                await self._send_message(websocket, {
                    "type": "error",
                    "content": "❌ Task not found or already completed"
                })

        except Exception as e:
            self.logger.error("Error handling task cancellation", error=str(e))
    
    def _get_enhanced_chat_html(self) -> str:
        """Get enhanced HTML for chat interface with task execution when templates are not available."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Desktop AI Agent Chat</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .chat-header {
            background: #4f46e5;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8fafc;
        }
        .message {
            margin: 10px 0;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
        }
        .user-message {
            background: #4f46e5;
            color: white;
            margin-left: auto;
        }
        .agent-message {
            background: white;
            border: 1px solid #e2e8f0;
            margin-right: auto;
        }
        .chat-input {
            display: flex;
            padding: 20px;
            background: white;
            border-top: 1px solid #e2e8f0;
        }
        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
        }
        .chat-input button {
            margin-left: 10px;
            padding: 12px 24px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
        }
        .typing {
            font-style: italic;
            color: #6b7280;
        }
        .learning-update {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            text-align: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 Desktop AI Agent</h1>
            <p>Conversational AI with Self-Improvement</p>
        </div>
        <div class="chat-messages" id="messages"></div>
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="Type your message here..." />
            <button onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        const ws = new WebSocket(`ws://localhost:8000/ws/web_user`);
        const messages = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');

        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            addMessage(data);
        };

        function addMessage(data) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            
            if (data.type === 'agent_message') {
                messageDiv.className += ' agent-message';
                messageDiv.innerHTML = `🤖 ${data.content}`;
            } else if (data.type === 'user_message') {
                messageDiv.className += ' user-message';
                messageDiv.innerHTML = data.content;
            } else if (data.type === 'typing') {
                messageDiv.className += ' agent-message typing';
                messageDiv.innerHTML = data.content;
                messageDiv.id = 'typing-indicator';
            } else if (data.type === 'learning_update') {
                messageDiv.className = 'learning-update';
                messageDiv.innerHTML = `📚 ${data.content}`;
            }
            
            // Remove typing indicator if this is a real message
            if (data.type === 'agent_message') {
                const typing = document.getElementById('typing-indicator');
                if (typing) typing.remove();
            }
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (message) {
                // Add user message to chat
                addMessage({type: 'user_message', content: message});
                
                // Send to server
                ws.send(JSON.stringify({
                    type: 'user_message',
                    content: message
                }));
                
                messageInput.value = '';
            }
        }

        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
        """
