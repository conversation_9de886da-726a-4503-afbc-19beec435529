"""
Web-based Chat Interface for the Desktop AI Agent.

This module provides a modern web interface for conversational interaction
with the AI agent, including real-time chat and learning management.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from fastapi import <PERSON>AP<PERSON>, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse
import structlog

from ..conversation.engine import ConversationEngine
from ..conversation.models import MessageType, ConversationLanguage
from ..learning.engine import LearningEngine
from ..ai.models import InferenceRequest


class WebChatInterface:
    """Web-based chat interface for the Desktop AI Agent."""
    
    def __init__(self, conversation_engine: ConversationEngine, learning_engine: LearningEngine):
        self.conversation_engine = conversation_engine
        self.learning_engine = learning_engine
        self.logger = structlog.get_logger(__name__)
        
        # Active WebSocket connections
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, str] = {}  # websocket_id -> session_id
        
        # Initialize FastAPI app
        self.app = FastAPI(title="Desktop AI Agent Chat Interface")
        self._setup_routes()
        
        # Setup static files and templates
        static_dir = Path(__file__).parent / "static"
        templates_dir = Path(__file__).parent / "templates"
        
        if static_dir.exists():
            self.app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
        
        if templates_dir.exists():
            self.templates = Jinja2Templates(directory=str(templates_dir))
        else:
            self.templates = None
    
    def _setup_routes(self):
        """Setup FastAPI routes."""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def chat_page(request: Request):
            """Serve the main chat interface."""
            if self.templates:
                return self.templates.TemplateResponse("chat.html", {"request": request})
            else:
                return HTMLResponse(self._get_default_chat_html())
        
        @self.app.websocket("/ws/{user_id}")
        async def websocket_endpoint(websocket: WebSocket, user_id: str):
            """WebSocket endpoint for real-time chat."""
            await self._handle_websocket_connection(websocket, user_id)
        
        @self.app.get("/api/learning/status")
        async def learning_status():
            """Get learning system status."""
            return await self.learning_engine.get_learning_statistics()
        
        @self.app.get("/api/learning/proposals")
        async def get_proposals():
            """Get pending improvement proposals."""
            proposals = await self.learning_engine.get_pending_proposals()
            return [
                {
                    "id": p.proposal_id,
                    "title": p.title,
                    "description": p.description,
                    "status": p.status.value,
                    "timeline": p.estimated_timeline,
                    "risks": p.risks
                }
                for p in proposals
            ]
        
        @self.app.post("/api/learning/proposals/{proposal_id}/approve")
        async def approve_proposal(proposal_id: str):
            """Approve an improvement proposal."""
            success = await self.learning_engine.approve_proposal(proposal_id, "web_user")
            return {"success": success}
        
        @self.app.post("/api/learning/proposals/{proposal_id}/reject")
        async def reject_proposal(proposal_id: str, reason: str = "No reason provided"):
            """Reject an improvement proposal."""
            success = await self.learning_engine.reject_proposal(proposal_id, reason)
            return {"success": success}
    
    async def _handle_websocket_connection(self, websocket: WebSocket, user_id: str):
        """Handle WebSocket connection for real-time chat."""
        await websocket.accept()
        connection_id = f"{user_id}_{datetime.now().timestamp()}"
        self.active_connections[connection_id] = websocket
        
        try:
            # Start conversation session
            session = await self.conversation_engine.start_conversation(
                user_id=user_id,
                platform="web",
                voice_enabled=False
            )
            
            self.user_sessions[connection_id] = session.session_id
            
            # Send welcome message
            if session.messages:
                welcome_msg = session.messages[0]
                await self._send_message(websocket, {
                    "type": "agent_message",
                    "content": welcome_msg.content,
                    "timestamp": welcome_msg.timestamp.isoformat(),
                    "language": welcome_msg.language.value if welcome_msg.language else "auto"
                })
            
            # Listen for messages
            while True:
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                await self._process_websocket_message(websocket, connection_id, message_data)
                
        except WebSocketDisconnect:
            self.logger.info("WebSocket disconnected", user_id=user_id)
        except Exception as e:
            self.logger.error("WebSocket error", error=str(e))
        finally:
            # Cleanup
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
            
            if connection_id in self.user_sessions:
                session_id = self.user_sessions[connection_id]
                await self.conversation_engine.end_conversation(session_id)
                del self.user_sessions[connection_id]
    
    async def _process_websocket_message(
        self, 
        websocket: WebSocket, 
        connection_id: str, 
        message_data: Dict[str, Any]
    ):
        """Process a message received via WebSocket."""
        try:
            message_type = message_data.get("type", "user_message")
            content = message_data.get("content", "")
            
            if message_type == "user_message" and content:
                session_id = self.user_sessions.get(connection_id)
                if not session_id:
                    return
                
                # Send typing indicator
                await self._send_message(websocket, {
                    "type": "typing",
                    "content": "Agent is thinking..."
                })
                
                # Process message
                response = await self.conversation_engine.process_user_message(
                    session_id,
                    content,
                    MessageType.USER_TEXT
                )
                
                # Send response
                await self._send_message(websocket, {
                    "type": "agent_message",
                    "content": response.content,
                    "timestamp": response.timestamp.isoformat(),
                    "language": response.language.value if response.language else "auto",
                    "metadata": response.metadata
                })
                
                # Check for learning updates
                await self._send_learning_updates(websocket)
            
            elif message_type == "feedback":
                # Handle user feedback
                rating = message_data.get("rating")
                comment = message_data.get("comment", "")
                
                if rating:
                    # Record feedback for learning
                    from ..learning.models import PerformanceMetric
                    metric = PerformanceMetric(
                        metric_name="user_satisfaction",
                        value=float(rating),
                        unit="stars",
                        task_type="conversation",
                        user_id=message_data.get("user_id", "web_user")
                    )
                    
                    await self.learning_engine.record_performance_metric(metric)
                    
                    await self._send_message(websocket, {
                        "type": "feedback_received",
                        "content": f"Thank you for the {rating}-star rating!"
                    })
            
        except Exception as e:
            self.logger.error("Error processing WebSocket message", error=str(e))
            await self._send_message(websocket, {
                "type": "error",
                "content": "Sorry, I encountered an error processing your message."
            })
    
    async def _send_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Send a message via WebSocket."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            self.logger.error("Error sending WebSocket message", error=str(e))
    
    async def _send_learning_updates(self, websocket: WebSocket):
        """Send learning system updates to the client."""
        try:
            # Check for new proposals
            proposals = await self.learning_engine.get_pending_proposals()
            
            if proposals:
                await self._send_message(websocket, {
                    "type": "learning_update",
                    "content": f"I have {len(proposals)} new improvement proposals!",
                    "proposals_count": len(proposals)
                })
        
        except Exception as e:
            self.logger.error("Error sending learning updates", error=str(e))
    
    def _get_default_chat_html(self) -> str:
        """Get default HTML for chat interface when templates are not available."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Desktop AI Agent Chat</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .chat-header {
            background: #4f46e5;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8fafc;
        }
        .message {
            margin: 10px 0;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
        }
        .user-message {
            background: #4f46e5;
            color: white;
            margin-left: auto;
        }
        .agent-message {
            background: white;
            border: 1px solid #e2e8f0;
            margin-right: auto;
        }
        .chat-input {
            display: flex;
            padding: 20px;
            background: white;
            border-top: 1px solid #e2e8f0;
        }
        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
        }
        .chat-input button {
            margin-left: 10px;
            padding: 12px 24px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
        }
        .typing {
            font-style: italic;
            color: #6b7280;
        }
        .learning-update {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            text-align: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 Desktop AI Agent</h1>
            <p>Conversational AI with Self-Improvement</p>
        </div>
        <div class="chat-messages" id="messages"></div>
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="Type your message here..." />
            <button onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        const ws = new WebSocket(`ws://localhost:8000/ws/web_user`);
        const messages = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');

        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            addMessage(data);
        };

        function addMessage(data) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            
            if (data.type === 'agent_message') {
                messageDiv.className += ' agent-message';
                messageDiv.innerHTML = `🤖 ${data.content}`;
            } else if (data.type === 'user_message') {
                messageDiv.className += ' user-message';
                messageDiv.innerHTML = data.content;
            } else if (data.type === 'typing') {
                messageDiv.className += ' agent-message typing';
                messageDiv.innerHTML = data.content;
                messageDiv.id = 'typing-indicator';
            } else if (data.type === 'learning_update') {
                messageDiv.className = 'learning-update';
                messageDiv.innerHTML = `📚 ${data.content}`;
            }
            
            // Remove typing indicator if this is a real message
            if (data.type === 'agent_message') {
                const typing = document.getElementById('typing-indicator');
                if (typing) typing.remove();
            }
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (message) {
                // Add user message to chat
                addMessage({type: 'user_message', content: message});
                
                // Send to server
                ws.send(JSON.stringify({
                    type: 'user_message',
                    content: message
                }));
                
                messageInput.value = '';
            }
        }

        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
        """
