"""
Task management API routes.
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status

from desktop_ai_agent.api.dependencies import get_orchestrator, require_authentication
from desktop_ai_agent.api.models import (
    ErrorResponse,
    SuccessResponse,
    TaskCreateRequest,
    TaskListResponse,
    TaskResponse,
)
from desktop_ai_agent.core.orchestrator import CentralOrchestrator, Task, TaskStatus

router = APIRouter(prefix="/api/v1/tasks", tags=["tasks"])


@router.post("/", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task(
    task_request: TaskCreateRequest,
    orchestrator: CentralOrchestrator = Depends(get_orchestrator),
    current_user: str = Depends(require_authentication)
):
    """Create a new task."""
    try:
        # Create task object
        task = Task(
            name=task_request.name,
            description=task_request.description,
            type=task_request.type,
            priority=task_request.priority,
            parameters=task_request.parameters,
            dependencies=task_request.dependencies,
            timeout=task_request.timeout
        )
        
        # Submit task to orchestrator
        task_id = await orchestrator.submit_task(task)
        
        # Get task status
        task_status = orchestrator.get_task_status(task_id)
        if not task_status:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create task"
            )
        
        return TaskResponse(
            task_id=task_status.task_id,
            name=task_status.name,
            description=task_status.description,
            type=task_status.type,
            status=task_status.status,
            priority=task_status.priority,
            progress=task_status.progress,
            current_step=task_status.current_step,
            steps_completed=task_status.steps_completed,
            total_steps=task_status.total_steps,
            created_at=task_status.created_at,
            started_at=task_status.started_at,
            completed_at=task_status.completed_at,
            error_message=task_status.error_message,
            result=task_status.result
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: str,
    orchestrator: CentralOrchestrator = Depends(get_orchestrator),
    current_user: str = Depends(require_authentication)
):
    """Get task status by ID."""
    task_status = orchestrator.get_task_status(task_id)
    if not task_status:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return TaskResponse(
        task_id=task_status.task_id,
        name=task_status.name,
        description=task_status.description,
        type=task_status.type,
        status=task_status.status,
        priority=task_status.priority,
        progress=task_status.progress,
        current_step=task_status.current_step,
        steps_completed=task_status.steps_completed,
        total_steps=task_status.total_steps,
        created_at=task_status.created_at,
        started_at=task_status.started_at,
        completed_at=task_status.completed_at,
        error_message=task_status.error_message,
        result=task_status.result
    )


@router.get("/", response_model=TaskListResponse)
async def list_tasks(
    status: Optional[str] = Query(None, description="Filter by task status"),
    limit: int = Query(50, ge=1, le=200, description="Number of tasks to return"),
    offset: int = Query(0, ge=0, description="Number of tasks to skip"),
    orchestrator: CentralOrchestrator = Depends(get_orchestrator),
    current_user: str = Depends(require_authentication)
):
    """List tasks with optional filtering."""
    try:
        # Convert status string to enum if provided
        status_filter = None
        if status:
            try:
                status_filter = TaskStatus(status)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status: {status}"
                )
        
        # Get tasks from orchestrator
        tasks = orchestrator.list_tasks(status_filter)
        
        # Apply pagination
        total = len(tasks)
        paginated_tasks = tasks[offset:offset + limit]
        
        # Convert to response format
        task_responses = []
        for task in paginated_tasks:
            task_responses.append(TaskResponse(
                task_id=task.task_id,
                name=task.name,
                description=task.description,
                type=task.type,
                status=task.status,
                priority=task.priority,
                progress=task.progress,
                current_step=task.current_step,
                steps_completed=task.steps_completed,
                total_steps=task.total_steps,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
                result=task.result
            ))
        
        return TaskListResponse(
            tasks=task_responses,
            total=total,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.delete("/{task_id}", response_model=SuccessResponse)
async def cancel_task(
    task_id: str,
    orchestrator: CentralOrchestrator = Depends(get_orchestrator),
    current_user: str = Depends(require_authentication)
):
    """Cancel a task."""
    try:
        # Check if task exists
        task_status = orchestrator.get_task_status(task_id)
        if not task_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        # Cancel task (simplified for Phase 1)
        # In a full implementation, this would properly cancel running tasks
        success = True  # Placeholder
        
        if success:
            return SuccessResponse(
                success=True,
                message=f"Task {task_id} cancelled successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to cancel task"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
