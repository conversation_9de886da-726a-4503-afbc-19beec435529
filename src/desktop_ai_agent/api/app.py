"""
FastAPI application for Desktop AI Agent.

This module creates and configures the FastAPI application with all routes,
middleware, and error handling.
"""

from datetime import datetime
from typing import Dict

from fastapi import Fast<PERSON><PERSON>, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import structlog

from desktop_ai_agent.api.dependencies import set_orchestrator
from desktop_ai_agent.api.models import ErrorResponse, HealthResponse
from desktop_ai_agent.api.routes import desktop, tasks
from desktop_ai_agent.core.config import Settings
from desktop_ai_agent.core.orchestrator import CentralOrchestrator


def create_app(settings: Settings, orchestrator: CentralOrchestrator) -> FastAPI:
    """Create and configure FastAPI application."""
    
    # Create FastAPI app
    app = FastAPI(
        title="Desktop AI Agent API",
        description="Advanced autonomous desktop AI agent for task automation",
        version="0.1.0",
        docs_url="/docs" if settings.api.debug else None,
        redoc_url="/redoc" if settings.api.debug else None,
    )
    
    # Set up logging
    logger = structlog.get_logger(__name__)
    
    # Set orchestrator for dependency injection
    set_orchestrator(orchestrator)
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.api.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add request logging middleware
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        start_time = datetime.utcnow()
        
        # Log request
        logger.info(
            "Request started",
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None
        )
        
        # Process request
        response = await call_next(request)
        
        # Log response
        process_time = (datetime.utcnow() - start_time).total_seconds()
        logger.info(
            "Request completed",
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            process_time=process_time
        )
        
        return response
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        logger.error(
            "Unhandled exception",
            method=request.method,
            url=str(request.url),
            error=str(exc),
            exc_info=True
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ErrorResponse(
                error={
                    "code": "internal_server_error",
                    "message": "An internal server error occurred",
                    "details": str(exc) if settings.api.debug else None,
                    "timestamp": datetime.utcnow().isoformat(),
                    "request_id": getattr(request.state, "request_id", None)
                }
            ).dict()
        )
    
    # HTTP exception handler
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        logger.warning(
            "HTTP exception",
            method=request.method,
            url=str(request.url),
            status_code=exc.status_code,
            detail=exc.detail
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content=ErrorResponse(
                error={
                    "code": "http_error",
                    "message": exc.detail,
                    "timestamp": datetime.utcnow().isoformat(),
                    "request_id": getattr(request.state, "request_id", None)
                }
            ).dict()
        )
    
    # Health check endpoint
    @app.get("/health", response_model=HealthResponse, tags=["health"])
    async def health_check():
        """Health check endpoint."""
        try:
            # Check orchestrator health
            components = {}
            
            # Get component statuses from orchestrator
            orchestrator_components = orchestrator.list_components()
            for component in orchestrator_components:
                components[component.component_id] = component.status
            
            return HealthResponse(
                status="healthy",
                timestamp=datetime.utcnow(),
                version="0.1.0",
                components=components
            )
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return JSONResponse(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                content=HealthResponse(
                    status="unhealthy",
                    timestamp=datetime.utcnow(),
                    version="0.1.0",
                    components={"error": str(e)}
                ).dict()
            )
    
    # Root endpoint
    @app.get("/", tags=["root"])
    async def root():
        """Root endpoint."""
        return {
            "name": "Desktop AI Agent API",
            "version": "0.1.0",
            "status": "running",
            "timestamp": datetime.utcnow().isoformat(),
            "docs_url": "/docs" if settings.api.debug else None
        }
    
    # Include routers
    app.include_router(tasks.router)
    app.include_router(desktop.router)
    
    # Startup event
    @app.on_event("startup")
    async def startup_event():
        logger.info("FastAPI application starting up")
    
    # Shutdown event
    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("FastAPI application shutting down")
    
    logger.info("FastAPI application created")
    return app
