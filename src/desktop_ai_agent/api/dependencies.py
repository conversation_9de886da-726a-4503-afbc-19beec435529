"""
API dependencies for Desktop AI Agent.

This module provides dependency injection for FastAPI endpoints.
"""

from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from desktop_ai_agent.core.orchestrator import CentralOrchestrator

# Global orchestrator instance (will be set by main app)
_orchestrator: Optional[CentralOrchestrator] = None

# Security
security = HTTPBearer(auto_error=False)


def set_orchestrator(orchestrator: CentralOrchestrator) -> None:
    """Set the global orchestrator instance."""
    global _orchestrator
    _orchestrator = orchestrator


def get_orchestrator() -> CentralOrchestrator:
    """Get the orchestrator instance."""
    if _orchestrator is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not available"
        )
    return _orchestrator


def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[str]:
    """Get current user from authentication token."""
    # Simplified authentication for Phase 1
    # In production, this would validate JWT tokens
    if credentials:
        # For now, just return a default user ID
        return "default_user"
    return None


def require_authentication(
    current_user: Optional[str] = Depends(get_current_user)
) -> str:
    """Require authentication for protected endpoints."""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return current_user
