"""
API data models for Desktop AI Agent.

This module provides Pydantic models for API request/response validation.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


# Task API Models

class TaskCreateRequest(BaseModel):
    """Task creation request model."""
    name: str
    description: Optional[str] = None
    type: str = "simple"
    priority: str = "normal"
    parameters: Dict[str, Any] = Field(default_factory=dict)
    dependencies: List[str] = Field(default_factory=list)
    timeout: Optional[int] = None


class TaskResponse(BaseModel):
    """Task response model."""
    task_id: str
    name: str
    description: Optional[str] = None
    type: str
    status: str
    priority: str
    progress: float
    current_step: Optional[str] = None
    steps_completed: int
    total_steps: int
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


class TaskListResponse(BaseModel):
    """Task list response model."""
    tasks: List[TaskResponse]
    total: int
    limit: int
    offset: int


# Desktop API Models

class WindowInfo(BaseModel):
    """Window information model."""
    window_id: str
    title: str
    application: str
    process_id: int
    position: Dict[str, int]
    size: Dict[str, int]
    state: str
    is_active: bool
    is_visible: bool


class WindowListResponse(BaseModel):
    """Window list response model."""
    windows: List[WindowInfo]


class WindowActionRequest(BaseModel):
    """Window action request model."""
    action: str
    parameters: Dict[str, Any] = Field(default_factory=dict)


class ScreenshotRequest(BaseModel):
    """Screenshot request model."""
    region: Optional[Dict[str, int]] = None
    format: str = "PNG"
    quality: int = 95
    include_cursor: bool = False


class ScreenshotResponse(BaseModel):
    """Screenshot response model."""
    image_id: str
    image_data: str  # Base64 encoded
    metadata: Dict[str, Any]


class KeyboardInputRequest(BaseModel):
    """Keyboard input request model."""
    action: str
    keys: List[str] = Field(default_factory=list)
    text: Optional[str] = None
    modifiers: List[str] = Field(default_factory=list)
    delay: float = 0.0


class MouseInputRequest(BaseModel):
    """Mouse input request model."""
    action: str
    position: Optional[Dict[str, int]] = None
    button: Optional[str] = None
    scroll_direction: Optional[str] = None
    scroll_amount: int = 1
    drag_to: Optional[Dict[str, int]] = None
    delay: float = 0.0


# AI API Models

class InferenceRequest(BaseModel):
    """AI inference request model."""
    model_id: str
    prompt: str
    max_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    stop_sequences: List[str] = Field(default_factory=list)
    context_id: Optional[str] = None
    system_prompt: Optional[str] = None
    stream: bool = False


class InferenceResponse(BaseModel):
    """AI inference response model."""
    response_id: str
    text: str
    finish_reason: str
    model_info: Dict[str, Any]
    tokens_used: int
    inference_time_ms: float


class ModelInfo(BaseModel):
    """Model information model."""
    model_id: str
    name: str
    type: str
    version: str
    status: str
    capabilities: List[str]
    resource_usage: Dict[str, Any]


class ModelListResponse(BaseModel):
    """Model list response model."""
    models: List[ModelInfo]


# Security API Models

class PermissionRequest(BaseModel):
    """Permission request model."""
    operation: str
    resource: str
    justification: Optional[str] = None
    duration: Optional[int] = None
    risk_level: str = "low"


class PermissionResponse(BaseModel):
    """Permission response model."""
    permission_id: str
    status: str
    expires_at: Optional[datetime] = None
    conditions: List[str] = Field(default_factory=list)


class AuditLogResponse(BaseModel):
    """Audit log response model."""
    logs: List[Dict[str, Any]]


# General API Models

class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime
    version: str
    components: Dict[str, str]


class ErrorResponse(BaseModel):
    """Error response model."""
    error: Dict[str, Any]


class SuccessResponse(BaseModel):
    """Success response model."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
