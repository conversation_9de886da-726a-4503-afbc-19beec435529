"""
Cross-platform window management for Desktop AI Agent.

This module provides window detection, manipulation, and monitoring capabilities
across Windows, macOS, and Linux platforms.
"""

import platform
import time
from typing import List, Optional

import psutil
import structlog

from desktop_ai_agent.desktop.models import Position, <PERSON>ze, WindowInfo, WindowState


class WindowManager:
    """Cross-platform window management system."""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.logger = structlog.get_logger(__name__)
        
        # Initialize platform-specific components
        if self.platform == "windows":
            self._init_windows()
        elif self.platform == "darwin":
            self._init_macos()
        elif self.platform == "linux":
            self._init_linux()
        else:
            raise RuntimeError(f"Unsupported platform: {self.platform}")
        
        self.logger.info("Window manager initialized", platform=self.platform)
    
    def _init_windows(self) -> None:
        """Initialize Windows-specific window management."""
        try:
            import win32gui
            import win32con
            import win32process
            self.win32gui = win32gui
            self.win32con = win32con
            self.win32process = win32process
        except ImportError:
            raise RuntimeError("Windows platform requires pywin32 package")
    
    def _init_macos(self) -> None:
        """Initialize macOS-specific window management."""
        try:
            from AppKit import NSWorkspace, NSApplication
            from Quartz import CGWindowListCopyWindowInfo, kCGWindowListOptionOnScreenOnly, kCGNullWindowID
            self.NSWorkspace = NSWorkspace
            self.NSApplication = NSApplication
            self.CGWindowListCopyWindowInfo = CGWindowListCopyWindowInfo
            self.kCGWindowListOptionOnScreenOnly = kCGWindowListOptionOnScreenOnly
            self.kCGNullWindowID = kCGNullWindowID
        except ImportError:
            raise RuntimeError("macOS platform requires pyobjc-framework-Cocoa package")
    
    def _init_linux(self) -> None:
        """Initialize Linux-specific window management."""
        try:
            import Xlib.display
            import Xlib.X
            self.display = Xlib.display.Display()
            self.root = self.display.screen().root
            self.Xlib = Xlib
        except ImportError:
            raise RuntimeError("Linux platform requires python-xlib package")
    
    def list_windows(self, include_hidden: bool = False) -> List[WindowInfo]:
        """List all windows on the desktop."""
        try:
            if self.platform == "windows":
                return self._list_windows_windows(include_hidden)
            elif self.platform == "darwin":
                return self._list_windows_macos(include_hidden)
            elif self.platform == "linux":
                return self._list_windows_linux(include_hidden)
            else:
                return []
        except Exception as e:
            self.logger.error("Failed to list windows", error=str(e))
            return []
    
    def _list_windows_windows(self, include_hidden: bool) -> List[WindowInfo]:
        """List windows on Windows platform."""
        windows = []
        
        def enum_window_callback(hwnd, windows_list):
            if self.win32gui.IsWindow(hwnd):
                title = self.win32gui.GetWindowText(hwnd)
                if not title and not include_hidden:
                    return True
                
                # Get window rectangle
                try:
                    rect = self.win32gui.GetWindowRect(hwnd)
                    x, y, right, bottom = rect
                    width = right - x
                    height = bottom - y
                except:
                    return True
                
                # Get process information
                try:
                    _, process_id = self.win32process.GetWindowThreadProcessId(hwnd)
                    process = psutil.Process(process_id)
                    application = process.name()
                except:
                    process_id = 0
                    application = "Unknown"
                
                # Determine window state
                state = WindowState.NORMAL
                if self.win32gui.IsIconic(hwnd):
                    state = WindowState.MINIMIZED
                elif self.win32gui.IsZoomed(hwnd):
                    state = WindowState.MAXIMIZED
                
                # Check if window is visible and active
                is_visible = self.win32gui.IsWindowVisible(hwnd)
                is_active = self.win32gui.GetForegroundWindow() == hwnd
                
                window_info = WindowInfo(
                    window_id=str(hwnd),
                    title=title,
                    application=application,
                    process_id=process_id,
                    position=Position(x=x, y=y),
                    size=Size(width=width, height=height),
                    state=state,
                    is_active=is_active,
                    is_visible=is_visible
                )
                windows_list.append(window_info)
            
            return True
        
        self.win32gui.EnumWindows(enum_window_callback, windows)
        return windows
    
    def _list_windows_macos(self, include_hidden: bool) -> List[WindowInfo]:
        """List windows on macOS platform."""
        windows = []
        
        try:
            window_list = self.CGWindowListCopyWindowInfo(
                self.kCGWindowListOptionOnScreenOnly,
                self.kCGNullWindowID
            )
            
            for window in window_list:
                title = window.get('kCGWindowName', '')
                if not title and not include_hidden:
                    continue
                
                # Get window bounds
                bounds = window.get('kCGWindowBounds', {})
                x = int(bounds.get('X', 0))
                y = int(bounds.get('Y', 0))
                width = int(bounds.get('Width', 0))
                height = int(bounds.get('Height', 0))
                
                # Get process information
                process_id = window.get('kCGWindowOwnerPID', 0)
                try:
                    process = psutil.Process(process_id)
                    application = process.name()
                except:
                    application = window.get('kCGWindowOwnerName', 'Unknown')
                
                window_info = WindowInfo(
                    window_id=str(window.get('kCGWindowNumber', 0)),
                    title=title,
                    application=application,
                    process_id=process_id,
                    position=Position(x=x, y=y),
                    size=Size(width=width, height=height),
                    state=WindowState.NORMAL,  # macOS doesn't provide state info easily
                    is_active=False,  # Would need additional API calls
                    is_visible=True
                )
                windows.append(window_info)
        
        except Exception as e:
            self.logger.error("Failed to list macOS windows", error=str(e))
        
        return windows
    
    def _list_windows_linux(self, include_hidden: bool) -> List[WindowInfo]:
        """List windows on Linux platform."""
        windows = []
        
        try:
            # Get all windows
            window_ids = self.root.get_full_property(
                self.display.intern_atom('_NET_CLIENT_LIST'),
                self.Xlib.X.AnyPropertyType
            ).value
            
            for window_id in window_ids:
                try:
                    window = self.display.create_resource_object('window', window_id)
                    
                    # Get window title
                    title_prop = window.get_full_property(
                        self.display.intern_atom('_NET_WM_NAME'),
                        self.Xlib.X.AnyPropertyType
                    )
                    title = title_prop.value.decode('utf-8') if title_prop else ''
                    
                    if not title and not include_hidden:
                        continue
                    
                    # Get window geometry
                    geometry = window.get_geometry()
                    
                    # Get process ID
                    pid_prop = window.get_full_property(
                        self.display.intern_atom('_NET_WM_PID'),
                        self.Xlib.X.AnyPropertyType
                    )
                    process_id = pid_prop.value[0] if pid_prop else 0
                    
                    try:
                        process = psutil.Process(process_id)
                        application = process.name()
                    except:
                        application = "Unknown"
                    
                    window_info = WindowInfo(
                        window_id=str(window_id),
                        title=title,
                        application=application,
                        process_id=process_id,
                        position=Position(x=geometry.x, y=geometry.y),
                        size=Size(width=geometry.width, height=geometry.height),
                        state=WindowState.NORMAL,
                        is_active=False,
                        is_visible=True
                    )
                    windows.append(window_info)
                
                except Exception as e:
                    self.logger.debug("Failed to get window info", window_id=window_id, error=str(e))
                    continue
        
        except Exception as e:
            self.logger.error("Failed to list Linux windows", error=str(e))
        
        return windows
    
    def get_window_by_id(self, window_id: str) -> Optional[WindowInfo]:
        """Get window information by ID."""
        windows = self.list_windows(include_hidden=True)
        for window in windows:
            if window.window_id == window_id:
                return window
        return None
    
    def get_window_by_title(self, title: str, exact_match: bool = False) -> Optional[WindowInfo]:
        """Get window by title."""
        windows = self.list_windows()
        for window in windows:
            if exact_match:
                if window.title == title:
                    return window
            else:
                if title.lower() in window.title.lower():
                    return window
        return None
    
    def get_active_window(self) -> Optional[WindowInfo]:
        """Get the currently active window."""
        windows = self.list_windows()
        for window in windows:
            if window.is_active:
                return window
        return None
    
    def focus_window(self, window_id: str) -> bool:
        """Focus a window by ID."""
        try:
            if self.platform == "windows":
                return self._focus_window_windows(window_id)
            elif self.platform == "darwin":
                return self._focus_window_macos(window_id)
            elif self.platform == "linux":
                return self._focus_window_linux(window_id)
            return False
        except Exception as e:
            self.logger.error("Failed to focus window", window_id=window_id, error=str(e))
            return False
    
    def _focus_window_windows(self, window_id: str) -> bool:
        """Focus window on Windows."""
        try:
            hwnd = int(window_id)
            self.win32gui.SetForegroundWindow(hwnd)
            return True
        except:
            return False
    
    def _focus_window_macos(self, window_id: str) -> bool:
        """Focus window on macOS."""
        # macOS window focusing requires more complex implementation
        # This is a simplified version
        return False
    
    def _focus_window_linux(self, window_id: str) -> bool:
        """Focus window on Linux."""
        try:
            window = self.display.create_resource_object('window', int(window_id))
            window.set_input_focus(self.Xlib.X.RevertToParent, self.Xlib.X.CurrentTime)
            self.display.sync()
            return True
        except:
            return False
    
    def minimize_window(self, window_id: str) -> bool:
        """Minimize a window."""
        try:
            if self.platform == "windows":
                hwnd = int(window_id)
                self.win32gui.ShowWindow(hwnd, self.win32con.SW_MINIMIZE)
                return True
            # Add macOS and Linux implementations
            return False
        except Exception as e:
            self.logger.error("Failed to minimize window", window_id=window_id, error=str(e))
            return False
    
    def maximize_window(self, window_id: str) -> bool:
        """Maximize a window."""
        try:
            if self.platform == "windows":
                hwnd = int(window_id)
                self.win32gui.ShowWindow(hwnd, self.win32con.SW_MAXIMIZE)
                return True
            # Add macOS and Linux implementations
            return False
        except Exception as e:
            self.logger.error("Failed to maximize window", window_id=window_id, error=str(e))
            return False
    
    def close_window(self, window_id: str) -> bool:
        """Close a window."""
        try:
            if self.platform == "windows":
                hwnd = int(window_id)
                self.win32gui.PostMessage(hwnd, self.win32con.WM_CLOSE, 0, 0)
                return True
            # Add macOS and Linux implementations
            return False
        except Exception as e:
            self.logger.error("Failed to close window", window_id=window_id, error=str(e))
            return False

    def move_window(self, window_id: str, position: Position) -> bool:
        """Move a window to a new position."""
        try:
            if self.platform == "windows":
                hwnd = int(window_id)
                self.win32gui.SetWindowPos(
                    hwnd, 0, position.x, position.y, 0, 0,
                    self.win32con.SWP_NOSIZE | self.win32con.SWP_NOZORDER
                )
                return True
            # Add macOS and Linux implementations
            return False
        except Exception as e:
            self.logger.error("Failed to move window", window_id=window_id, error=str(e))
            return False

    def resize_window(self, window_id: str, size: Size) -> bool:
        """Resize a window."""
        try:
            if self.platform == "windows":
                hwnd = int(window_id)
                self.win32gui.SetWindowPos(
                    hwnd, 0, 0, 0, size.width, size.height,
                    self.win32con.SWP_NOMOVE | self.win32con.SWP_NOZORDER
                )
                return True
            # Add macOS and Linux implementations
            return False
        except Exception as e:
            self.logger.error("Failed to resize window", window_id=window_id, error=str(e))
            return False
