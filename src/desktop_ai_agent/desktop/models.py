"""
Data models for desktop interaction components.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from pydantic import BaseModel, Field


class WindowState(str, Enum):
    """Window state enumeration."""
    NORMAL = "normal"
    MINIMIZED = "minimized"
    MAXIMIZED = "maximized"
    FULLSCREEN = "fullscreen"


class MouseButton(str, Enum):
    """Mouse button enumeration."""
    LEFT = "left"
    RIGHT = "right"
    MIDDLE = "middle"


class ScrollDirection(str, Enum):
    """Scroll direction enumeration."""
    UP = "up"
    DOWN = "down"
    LEFT = "left"
    RIGHT = "right"


class KeyAction(str, Enum):
    """Keyboard action enumeration."""
    PRESS = "press"
    RELEASE = "release"
    TYPE = "type"


class Position(BaseModel):
    """2D position model."""
    x: int
    y: int


class Size(BaseModel):
    """2D size model."""
    width: int
    height: int


class Rectangle(BaseModel):
    """Rectangle model combining position and size."""
    x: int
    y: int
    width: int
    height: int
    
    @property
    def position(self) -> Position:
        return Position(x=self.x, y=self.y)
    
    @property
    def size(self) -> Size:
        return Size(width=self.width, height=self.height)
    
    @property
    def center(self) -> Position:
        return Position(x=self.x + self.width // 2, y=self.y + self.height // 2)


class WindowInfo(BaseModel):
    """Window information model."""
    window_id: str
    title: str
    application: str
    process_id: int
    position: Position
    size: Size
    state: WindowState = WindowState.NORMAL
    is_active: bool = False
    is_visible: bool = True
    z_order: int = 0
    
    @property
    def rectangle(self) -> Rectangle:
        return Rectangle(
            x=self.position.x,
            y=self.position.y,
            width=self.size.width,
            height=self.size.height
        )


class ScreenInfo(BaseModel):
    """Screen information model."""
    screen_id: int
    is_primary: bool
    position: Position
    size: Size
    scale_factor: float = 1.0
    
    @property
    def rectangle(self) -> Rectangle:
        return Rectangle(
            x=self.position.x,
            y=self.position.y,
            width=self.size.width,
            height=self.size.height
        )


class Screenshot(BaseModel):
    """Screenshot model."""
    image_id: str
    image_data: bytes
    format: str = "PNG"
    width: int
    height: int
    region: Optional[Rectangle] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class KeyboardInput(BaseModel):
    """Keyboard input model."""
    action: KeyAction
    keys: List[str] = Field(default_factory=list)
    text: Optional[str] = None
    modifiers: List[str] = Field(default_factory=list)
    delay: float = 0.0


class MouseInput(BaseModel):
    """Mouse input model."""
    action: str
    position: Optional[Position] = None
    button: Optional[MouseButton] = None
    scroll_direction: Optional[ScrollDirection] = None
    scroll_amount: int = 1
    drag_to: Optional[Position] = None
    delay: float = 0.0


class ApplicationInfo(BaseModel):
    """Application information model."""
    name: str
    executable: str
    process_id: Optional[int] = None
    window_ids: List[str] = Field(default_factory=list)
    is_running: bool = False
    start_time: Optional[datetime] = None


class SystemInfo(BaseModel):
    """System information model."""
    platform: str
    version: str
    architecture: str
    cpu_count: int
    memory_total: int
    memory_available: int
    disk_usage: Dict[str, Any] = Field(default_factory=dict)
    screens: List[ScreenInfo] = Field(default_factory=list)


class InputEvent(BaseModel):
    """Input event model for logging and replay."""
    event_id: str
    event_type: str  # keyboard, mouse, window
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    data: Dict[str, Any] = Field(default_factory=dict)
    source: str = "user"  # user, system, automation


class DesktopAction(BaseModel):
    """Desktop action model for task execution."""
    action_id: str
    action_type: str
    target: Optional[str] = None
    parameters: Dict[str, Any] = Field(default_factory=dict)
    expected_result: Optional[Dict[str, Any]] = None
    timeout: float = 5.0
    retry_count: int = 0
    max_retries: int = 3


class DesktopActionResult(BaseModel):
    """Result of a desktop action."""
    action_id: str
    success: bool
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time: float = 0.0
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    screenshots: List[str] = Field(default_factory=list)  # Screenshot IDs
