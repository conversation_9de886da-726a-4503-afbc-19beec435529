"""
Desktop Interaction Engine (DIE) for Desktop AI Agent.

This module provides the main interface for desktop interaction capabilities,
coordinating between window management, input simulation, and screen analysis.
"""

import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog

from desktop_ai_agent.core.config import Settings
from desktop_ai_agent.core.orchestrator import Component, ComponentStatus
from desktop_ai_agent.desktop.input_controller import Input<PERSON>ontroller
from desktop_ai_agent.desktop.models import (
    DesktopAction,
    DesktopActionResult,
    KeyboardInput,
    MouseInput,
    Position,
    Rectangle,
    Screenshot,
    Size,
    WindowInfo,
)
from desktop_ai_agent.desktop.screen_analyzer import ScreenAnalyzer
from desktop_ai_agent.desktop.window_manager import WindowManager


class DesktopInteractionEngine:
    """
    Desktop Interaction Engine (DIE) - Main interface for desktop interaction.
    
    Coordinates between window management, input simulation, and screen analysis
    to provide comprehensive desktop automation capabilities.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = structlog.get_logger(__name__)
        
        # Initialize sub-components
        self.window_manager = WindowManager()
        self.input_controller = InputController(
            safe_mode=settings.desktop.safe_mode,
            default_delay=settings.desktop.input_delay
        )
        self.screen_analyzer = ScreenAnalyzer(
            default_format=settings.desktop.screenshot_format,
            default_quality=settings.desktop.screenshot_quality
        )
        
        # Component registration info
        self.component_info = Component(
            component_id="desktop_interaction_engine",
            name="Desktop Interaction Engine",
            version="0.1.0",
            status=ComponentStatus.HEALTHY,
            capabilities=[
                "window_management",
                "input_simulation", 
                "screen_capture",
                "image_analysis",
                "desktop_automation"
            ],
            metadata={
                "platform": self.settings.platform,
                "safe_mode": settings.desktop.safe_mode
            }
        )
        
        # Action execution tracking
        self.active_actions: Dict[str, DesktopAction] = {}
        
        self.logger.info("Desktop Interaction Engine initialized")
    
    async def start(self) -> None:
        """Start the desktop interaction engine."""
        try:
            self.component_info.status = ComponentStatus.HEALTHY
            self.component_info.last_heartbeat = datetime.utcnow()
            self.logger.info("Desktop Interaction Engine started")
        except Exception as e:
            self.component_info.status = ComponentStatus.UNHEALTHY
            self.logger.error("Failed to start Desktop Interaction Engine", error=str(e))
            raise
    
    async def stop(self) -> None:
        """Stop the desktop interaction engine."""
        try:
            self.component_info.status = ComponentStatus.OFFLINE
            self.logger.info("Desktop Interaction Engine stopped")
        except Exception as e:
            self.logger.error("Failed to stop Desktop Interaction Engine", error=str(e))
    
    def get_component_info(self) -> Component:
        """Get component information for registration."""
        self.component_info.last_heartbeat = datetime.utcnow()
        return self.component_info
    
    # Window Management Methods
    
    async def list_windows(self, include_hidden: bool = False) -> List[WindowInfo]:
        """List all windows on the desktop."""
        try:
            return self.window_manager.list_windows(include_hidden)
        except Exception as e:
            self.logger.error("Failed to list windows", error=str(e))
            return []
    
    async def get_window_by_id(self, window_id: str) -> Optional[WindowInfo]:
        """Get window information by ID."""
        try:
            return self.window_manager.get_window_by_id(window_id)
        except Exception as e:
            self.logger.error("Failed to get window by ID", window_id=window_id, error=str(e))
            return None
    
    async def get_window_by_title(self, title: str, exact_match: bool = False) -> Optional[WindowInfo]:
        """Get window by title."""
        try:
            return self.window_manager.get_window_by_title(title, exact_match)
        except Exception as e:
            self.logger.error("Failed to get window by title", title=title, error=str(e))
            return None
    
    async def get_active_window(self) -> Optional[WindowInfo]:
        """Get the currently active window."""
        try:
            return self.window_manager.get_active_window()
        except Exception as e:
            self.logger.error("Failed to get active window", error=str(e))
            return None
    
    async def focus_window(self, window_id: str) -> bool:
        """Focus a window by ID."""
        try:
            return self.window_manager.focus_window(window_id)
        except Exception as e:
            self.logger.error("Failed to focus window", window_id=window_id, error=str(e))
            return False
    
    async def minimize_window(self, window_id: str) -> bool:
        """Minimize a window."""
        try:
            return self.window_manager.minimize_window(window_id)
        except Exception as e:
            self.logger.error("Failed to minimize window", window_id=window_id, error=str(e))
            return False
    
    async def maximize_window(self, window_id: str) -> bool:
        """Maximize a window."""
        try:
            return self.window_manager.maximize_window(window_id)
        except Exception as e:
            self.logger.error("Failed to maximize window", window_id=window_id, error=str(e))
            return False
    
    async def close_window(self, window_id: str) -> bool:
        """Close a window."""
        try:
            return self.window_manager.close_window(window_id)
        except Exception as e:
            self.logger.error("Failed to close window", window_id=window_id, error=str(e))
            return False
    
    async def move_window(self, window_id: str, position: Position) -> bool:
        """Move a window to a new position."""
        try:
            return self.window_manager.move_window(window_id, position)
        except Exception as e:
            self.logger.error("Failed to move window", window_id=window_id, position=position, error=str(e))
            return False
    
    async def resize_window(self, window_id: str, size: Size) -> bool:
        """Resize a window."""
        try:
            return self.window_manager.resize_window(window_id, size)
        except Exception as e:
            self.logger.error("Failed to resize window", window_id=window_id, size=size, error=str(e))
            return False
    
    # Input Simulation Methods
    
    async def simulate_keyboard_input(self, input_data: KeyboardInput) -> bool:
        """Simulate keyboard input."""
        try:
            return self.input_controller.simulate_keyboard_input(input_data)
        except Exception as e:
            self.logger.error("Failed to simulate keyboard input", error=str(e))
            return False
    
    async def simulate_mouse_input(self, input_data: MouseInput) -> bool:
        """Simulate mouse input."""
        try:
            return self.input_controller.simulate_mouse_input(input_data)
        except Exception as e:
            self.logger.error("Failed to simulate mouse input", error=str(e))
            return False
    
    async def type_text(self, text: str) -> bool:
        """Type text using the keyboard."""
        try:
            keyboard_input = KeyboardInput(action="type", text=text)
            return await self.simulate_keyboard_input(keyboard_input)
        except Exception as e:
            self.logger.error("Failed to type text", text=text, error=str(e))
            return False
    
    async def click_at_position(self, position: Position, button: str = "left") -> bool:
        """Click at a specific position."""
        try:
            mouse_input = MouseInput(action="click", position=position, button=button)
            return await self.simulate_mouse_input(mouse_input)
        except Exception as e:
            self.logger.error("Failed to click at position", position=position, error=str(e))
            return False
    
    # Screen Analysis Methods
    
    async def capture_screenshot(
        self,
        region: Optional[Rectangle] = None,
        format: Optional[str] = None,
        quality: Optional[int] = None
    ) -> Optional[Screenshot]:
        """Capture a screenshot."""
        try:
            return self.screen_analyzer.capture_screenshot(region, format, quality)
        except Exception as e:
            self.logger.error("Failed to capture screenshot", region=region, error=str(e))
            return None
    
    async def find_image_on_screen(
        self,
        template_image: bytes,
        confidence: float = 0.8,
        region: Optional[Rectangle] = None
    ) -> Optional[Position]:
        """Find a template image on the screen."""
        try:
            return self.screen_analyzer.find_image_on_screen(template_image, confidence, region)
        except Exception as e:
            self.logger.error("Failed to find image on screen", error=str(e))
            return None
    
    async def analyze_screen_elements(self, region: Optional[Rectangle] = None) -> Dict[str, Any]:
        """Analyze screen elements and UI components."""
        try:
            return self.screen_analyzer.analyze_screen_elements(region)
        except Exception as e:
            self.logger.error("Failed to analyze screen elements", error=str(e))
            return {}
    
    # High-level Desktop Actions
    
    async def execute_desktop_action(self, action: DesktopAction) -> DesktopActionResult:
        """Execute a high-level desktop action."""
        start_time = datetime.utcnow()
        action_id = action.action_id
        
        try:
            self.active_actions[action_id] = action
            self.logger.info("Executing desktop action", action_id=action_id, action_type=action.action_type)
            
            # Route action to appropriate handler
            result = None
            action_type = action.action_type.value if hasattr(action.action_type, 'value') else str(action.action_type)

            if action_type == "window_focus":
                result = await self._handle_window_focus_action(action)
            elif action_type == "click":
                result = await self._handle_click_action(action)
            elif action_type == "type_text":
                result = await self._handle_type_text_action(action)
            elif action_type == "screenshot":
                result = await self._handle_screenshot_action(action)
            elif action_type == "mouse_move":
                result = await self._handle_mouse_move_action(action)
            elif action_type == "get_screen_size":
                result = await self._handle_get_screen_size_action(action)
            elif action_type == "file_operation":
                result = await self._handle_file_operation_action(action)
            else:
                raise ValueError(f"Unknown action type: {action_type}")
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            action_result = DesktopActionResult(
                action_id=action_id,
                success=True,
                result=result,
                execution_time=execution_time,
                timestamp=datetime.utcnow()
            )
            
            self.logger.info("Desktop action completed", action_id=action_id, execution_time=execution_time)
            return action_result
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            action_result = DesktopActionResult(
                action_id=action_id,
                success=False,
                error_message=str(e),
                execution_time=execution_time,
                timestamp=datetime.utcnow()
            )
            
            self.logger.error("Desktop action failed", action_id=action_id, error=str(e))
            return action_result
            
        finally:
            self.active_actions.pop(action_id, None)
    
    async def _handle_window_focus_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle window focus action."""
        window_id = action.parameters.get("window_id")
        if not window_id:
            raise ValueError("window_id parameter required for window_focus action")

        success = await self.focus_window(window_id)
        return {"window_id": window_id, "focused": success}

    async def _handle_click_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle click action."""
        x = action.parameters.get("x")
        y = action.parameters.get("y")
        button = action.parameters.get("button", "left")

        if x is None or y is None:
            raise ValueError("x and y coordinates required for click action")

        success = await self.click(x, y, button)
        return {"x": x, "y": y, "button": button, "clicked": success}

    async def _handle_type_text_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle type text action."""
        text = action.parameters.get("text")
        if not text:
            raise ValueError("text parameter required for type_text action")

        success = await self.type_text(text)
        return {"text": text, "typed": success}

    async def _handle_screenshot_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle screenshot action."""
        format_type = action.parameters.get("format", "PNG")
        quality = action.parameters.get("quality", 95)
        filename = action.parameters.get("filename", "screenshot.png")
        save_path = action.parameters.get("save_path", "/tmp")

        screenshot_data = await self.take_screenshot()

        if not screenshot_data:
            raise RuntimeError("Failed to capture screenshot")

        # Save screenshot to file
        import os
        from PIL import Image
        import io

        # Ensure save directory exists
        os.makedirs(save_path, exist_ok=True)
        file_path = os.path.join(save_path, filename)

        try:
            # Convert screenshot data to PIL Image and save
            image = Image.open(io.BytesIO(screenshot_data))

            if format_type.upper() == "JPEG":
                # Convert to RGB for JPEG (remove alpha channel)
                if image.mode in ("RGBA", "LA", "P"):
                    image = image.convert("RGB")
                image.save(file_path, format="JPEG", quality=quality)
            else:
                # Save as PNG (default)
                image.save(file_path, format="PNG")

            # Get actual file size
            actual_size = os.path.getsize(file_path)

            self.logger.info("Screenshot saved successfully",
                           file_path=file_path, size=actual_size)

            return {
                "format": format_type,
                "quality": quality,
                "file_path": file_path,
                "size": actual_size,
                "saved": True
            }

        except Exception as e:
            self.logger.error("Failed to save screenshot", error=str(e))
            raise RuntimeError(f"Failed to save screenshot: {str(e)}")

    async def _handle_mouse_move_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle mouse move action."""
        x = action.parameters.get("x")
        y = action.parameters.get("y")

        if x is None or y is None:
            raise ValueError("x and y coordinates required for mouse_move action")

        success = await self.move_mouse(x, y)
        return {"x": x, "y": y, "moved": success}

    async def _handle_get_screen_size_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle get screen size action."""
        try:
            # Get actual screen size using multiple methods
            width, height = None, None

            # Method 1: Try pyautogui
            try:
                import pyautogui
                size = pyautogui.size()
                width, height = size.width, size.height
                self.logger.info("Screen size detected via pyautogui", width=width, height=height)
            except ImportError:
                pass

            # Method 2: Try tkinter (fallback)
            if width is None or height is None:
                try:
                    import tkinter as tk
                    root = tk.Tk()
                    width = root.winfo_screenwidth()
                    height = root.winfo_screenheight()
                    root.destroy()
                    self.logger.info("Screen size detected via tkinter", width=width, height=height)
                except ImportError:
                    pass

            # Method 3: Try platform-specific methods
            if width is None or height is None:
                import platform
                system = platform.system()

                if system == "Linux":
                    try:
                        import subprocess
                        result = subprocess.run(['xrandr'], capture_output=True, text=True)
                        for line in result.stdout.split('\n'):
                            if ' connected primary' in line or ' connected' in line:
                                parts = line.split()
                                for part in parts:
                                    if 'x' in part and '+' in part:
                                        resolution = part.split('+')[0]
                                        width, height = map(int, resolution.split('x'))
                                        break
                                if width and height:
                                    break
                        self.logger.info("Screen size detected via xrandr", width=width, height=height)
                    except Exception:
                        pass

            # Fallback to common resolution if all methods fail
            if width is None or height is None:
                width, height = 1920, 1080
                self.logger.warning("Could not detect screen size, using fallback", width=width, height=height)

            return {"width": width, "height": height, "detected": True}

        except Exception as e:
            self.logger.error("Failed to get screen size", error=str(e))
            # Return fallback resolution
            return {"width": 1920, "height": 1080, "detected": False, "error": str(e)}

    async def _handle_file_operation_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle file operation action."""
        import os
        operation = action.parameters.get("operation")
        path = action.parameters.get("path", "/tmp")

        if operation == "create_folder":
            folder_name = action.parameters.get("folder_name", "new_folder")
            full_path = os.path.join(path, folder_name)

            try:
                os.makedirs(full_path, exist_ok=True)

                # Verify folder was created
                if os.path.exists(full_path) and os.path.isdir(full_path):
                    self.logger.info("Folder created successfully", path=full_path)
                    return {
                        "operation": operation,
                        "path": full_path,
                        "created": True,
                        "exists": True
                    }
                else:
                    return {
                        "operation": operation,
                        "path": full_path,
                        "created": False,
                        "error": "Folder creation verification failed"
                    }

            except Exception as e:
                self.logger.error("Failed to create folder", path=full_path, error=str(e))
                return {
                    "operation": operation,
                    "path": full_path,
                    "created": False,
                    "error": str(e)
                }

        elif operation == "delete_folder":
            folder_path = action.parameters.get("folder_path", path)

            try:
                import shutil
                if os.path.exists(folder_path) and os.path.isdir(folder_path):
                    shutil.rmtree(folder_path)
                    self.logger.info("Folder deleted successfully", path=folder_path)
                    return {
                        "operation": operation,
                        "path": folder_path,
                        "deleted": True
                    }
                else:
                    return {
                        "operation": operation,
                        "path": folder_path,
                        "deleted": False,
                        "error": "Folder does not exist"
                    }
            except Exception as e:
                self.logger.error("Failed to delete folder", path=folder_path, error=str(e))
                return {
                    "operation": operation,
                    "path": folder_path,
                    "deleted": False,
                    "error": str(e)
                }

        elif operation == "move_file":
            source = action.parameters.get("source")
            destination = action.parameters.get("destination")

            if not source or not destination:
                return {
                    "operation": operation,
                    "completed": False,
                    "error": "Source and destination paths required"
                }

            try:
                import shutil
                shutil.move(source, destination)
                self.logger.info("File moved successfully", source=source, destination=destination)
                return {
                    "operation": operation,
                    "source": source,
                    "destination": destination,
                    "completed": True
                }
            except Exception as e:
                self.logger.error("Failed to move file", source=source, destination=destination, error=str(e))
                return {
                    "operation": operation,
                    "source": source,
                    "destination": destination,
                    "completed": False,
                    "error": str(e)
                }

        elif operation == "list_files":
            try:
                files = []
                if os.path.exists(path) and os.path.isdir(path):
                    for item in os.listdir(path):
                        item_path = os.path.join(path, item)
                        files.append({
                            "name": item,
                            "path": item_path,
                            "is_directory": os.path.isdir(item_path),
                            "size": os.path.getsize(item_path) if os.path.isfile(item_path) else 0
                        })

                self.logger.info("Files listed successfully", path=path, count=len(files))
                return {
                    "operation": operation,
                    "path": path,
                    "files": files,
                    "count": len(files),
                    "completed": True
                }
            except Exception as e:
                self.logger.error("Failed to list files", path=path, error=str(e))
                return {
                    "operation": operation,
                    "path": path,
                    "completed": False,
                    "error": str(e)
                }

        else:
            return {
                "operation": operation,
                "path": path,
                "completed": False,
                "error": f"Unknown file operation: {operation}"
            }
    
    async def _handle_click_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle click action."""
        position_data = action.parameters.get("position")
        if not position_data:
            raise ValueError("position parameter required for click action")
        
        position = Position(x=position_data["x"], y=position_data["y"])
        button = action.parameters.get("button", "left")
        
        success = await self.click_at_position(position, button)
        return {"position": position_data, "button": button, "clicked": success}
    
    async def _handle_type_text_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle type text action."""
        text = action.parameters.get("text")
        if not text:
            raise ValueError("text parameter required for type_text action")
        
        success = await self.type_text(text)
        return {"text": text, "typed": success}
    
    async def _handle_screenshot_action(self, action: DesktopAction) -> Dict[str, Any]:
        """Handle screenshot action."""
        region_data = action.parameters.get("region")
        region = None
        if region_data:
            region = Rectangle(**region_data)
        
        screenshot = await self.capture_screenshot(region)
        if screenshot:
            return {
                "screenshot_id": screenshot.image_id,
                "width": screenshot.width,
                "height": screenshot.height,
                "format": screenshot.format,
                "region": region_data
            }
        else:
            raise RuntimeError("Failed to capture screenshot")
    
    def get_mouse_position(self) -> Position:
        """Get current mouse position."""
        return self.input_controller.get_mouse_position()
    
    def is_safe_position(self, position: Position) -> bool:
        """Check if position is safe for interaction."""
        return self.input_controller.is_safe_position(position)
