"""
Cross-platform input simulation for Desktop AI Agent.

This module provides keyboard and mouse input simulation capabilities
across Windows, macOS, and Linux platforms.
"""

import platform
import time
from typing import List, Optional

import pyautogui
import structlog
from pynput import keyboard, mouse
from pynput.keyboard import Key
from pynput.mouse import Button

from desktop_ai_agent.desktop.models import (
    KeyAction,
    KeyboardInput,
    MouseButton,
    MouseInput,
    Position,
    ScrollDirection,
)


class InputController:
    """Cross-platform input simulation system."""
    
    def __init__(self, safe_mode: bool = True, default_delay: float = 0.1):
        self.platform = platform.system().lower()
        self.safe_mode = safe_mode
        self.default_delay = default_delay
        self.logger = structlog.get_logger(__name__)
        
        # Configure pyautogui
        pyautogui.FAILSAFE = safe_mode
        pyautogui.PAUSE = default_delay
        
        # Initialize input controllers
        self.keyboard_controller = keyboard.Controller()
        self.mouse_controller = mouse.Controller()
        
        # Key mapping for cross-platform compatibility
        self.key_mapping = {
            'ctrl': Key.ctrl,
            'alt': Key.alt,
            'shift': Key.shift,
            'cmd': Key.cmd,
            'meta': Key.cmd if self.platform == 'darwin' else Key.ctrl,
            'enter': Key.enter,
            'return': Key.enter,
            'space': Key.space,
            'tab': Key.tab,
            'escape': Key.esc,
            'esc': Key.esc,
            'backspace': Key.backspace,
            'delete': Key.delete,
            'home': Key.home,
            'end': Key.end,
            'page_up': Key.page_up,
            'page_down': Key.page_down,
            'up': Key.up,
            'down': Key.down,
            'left': Key.left,
            'right': Key.right,
            'f1': Key.f1, 'f2': Key.f2, 'f3': Key.f3, 'f4': Key.f4,
            'f5': Key.f5, 'f6': Key.f6, 'f7': Key.f7, 'f8': Key.f8,
            'f9': Key.f9, 'f10': Key.f10, 'f11': Key.f11, 'f12': Key.f12,
        }
        
        # Mouse button mapping
        self.mouse_button_mapping = {
            MouseButton.LEFT: Button.left,
            MouseButton.RIGHT: Button.right,
            MouseButton.MIDDLE: Button.middle,
        }
        
        self.logger.info("Input controller initialized", platform=self.platform, safe_mode=safe_mode)
    
    def simulate_keyboard_input(self, input_data: KeyboardInput) -> bool:
        """Simulate keyboard input."""
        try:
            if input_data.delay > 0:
                time.sleep(input_data.delay)
            
            if input_data.action == KeyAction.TYPE and input_data.text:
                return self._type_text(input_data.text)
            elif input_data.action == KeyAction.PRESS:
                return self._press_keys(input_data.keys, input_data.modifiers)
            elif input_data.action == KeyAction.RELEASE:
                return self._release_keys(input_data.keys, input_data.modifiers)
            else:
                self.logger.error("Unknown keyboard action", action=input_data.action)
                return False
                
        except Exception as e:
            self.logger.error("Keyboard input simulation failed", error=str(e))
            return False
    
    def _type_text(self, text: str) -> bool:
        """Type text using the keyboard."""
        try:
            if self.safe_mode and len(text) > 1000:
                self.logger.warning("Text too long for safe mode", length=len(text))
                return False
            
            pyautogui.typewrite(text, interval=self.default_delay / 10)
            self.logger.debug("Text typed successfully", length=len(text))
            return True
            
        except Exception as e:
            self.logger.error("Failed to type text", error=str(e))
            return False
    
    def _press_keys(self, keys: List[str], modifiers: List[str]) -> bool:
        """Press keys with optional modifiers."""
        try:
            # Convert string keys to Key objects
            key_objects = []
            for key in keys:
                if key.lower() in self.key_mapping:
                    key_objects.append(self.key_mapping[key.lower()])
                else:
                    key_objects.append(key)
            
            modifier_objects = []
            for modifier in modifiers:
                if modifier.lower() in self.key_mapping:
                    modifier_objects.append(self.key_mapping[modifier.lower()])
            
            # Press modifiers first
            for modifier in modifier_objects:
                self.keyboard_controller.press(modifier)
            
            # Press main keys
            for key in key_objects:
                self.keyboard_controller.press(key)
                self.keyboard_controller.release(key)
            
            # Release modifiers
            for modifier in reversed(modifier_objects):
                self.keyboard_controller.release(modifier)
            
            self.logger.debug("Keys pressed successfully", keys=keys, modifiers=modifiers)
            return True
            
        except Exception as e:
            self.logger.error("Failed to press keys", keys=keys, modifiers=modifiers, error=str(e))
            return False
    
    def _release_keys(self, keys: List[str], modifiers: List[str]) -> bool:
        """Release keys."""
        try:
            # This is mainly for completeness - most use cases don't need explicit release
            self.logger.debug("Keys released", keys=keys, modifiers=modifiers)
            return True
        except Exception as e:
            self.logger.error("Failed to release keys", error=str(e))
            return False
    
    def simulate_mouse_input(self, input_data: MouseInput) -> bool:
        """Simulate mouse input."""
        try:
            if input_data.delay > 0:
                time.sleep(input_data.delay)
            
            action = input_data.action.lower()
            
            if action == "move":
                return self._move_mouse(input_data.position)
            elif action == "click":
                return self._click_mouse(input_data.position, input_data.button)
            elif action == "double_click":
                return self._double_click_mouse(input_data.position, input_data.button)
            elif action == "right_click":
                return self._right_click_mouse(input_data.position)
            elif action == "scroll":
                return self._scroll_mouse(input_data.position, input_data.scroll_direction, input_data.scroll_amount)
            elif action == "drag":
                return self._drag_mouse(input_data.position, input_data.drag_to, input_data.button)
            else:
                self.logger.error("Unknown mouse action", action=action)
                return False
                
        except Exception as e:
            self.logger.error("Mouse input simulation failed", error=str(e))
            return False
    
    def _move_mouse(self, position: Optional[Position]) -> bool:
        """Move mouse to position."""
        try:
            if not position:
                return False
            
            if self.safe_mode:
                # Check if position is within screen bounds
                screen_width, screen_height = pyautogui.size()
                if not (0 <= position.x <= screen_width and 0 <= position.y <= screen_height):
                    self.logger.warning("Mouse position out of bounds", position=position)
                    return False
            
            pyautogui.moveTo(position.x, position.y, duration=self.default_delay)
            self.logger.debug("Mouse moved", position=position)
            return True
            
        except Exception as e:
            self.logger.error("Failed to move mouse", position=position, error=str(e))
            return False
    
    def _click_mouse(self, position: Optional[Position], button: Optional[MouseButton]) -> bool:
        """Click mouse at position."""
        try:
            button_str = 'left'
            if button == MouseButton.RIGHT:
                button_str = 'right'
            elif button == MouseButton.MIDDLE:
                button_str = 'middle'
            
            if position:
                pyautogui.click(position.x, position.y, button=button_str)
            else:
                pyautogui.click(button=button_str)
            
            self.logger.debug("Mouse clicked", position=position, button=button)
            return True
            
        except Exception as e:
            self.logger.error("Failed to click mouse", position=position, button=button, error=str(e))
            return False
    
    def _double_click_mouse(self, position: Optional[Position], button: Optional[MouseButton]) -> bool:
        """Double-click mouse at position."""
        try:
            button_str = 'left'
            if button == MouseButton.RIGHT:
                button_str = 'right'
            elif button == MouseButton.MIDDLE:
                button_str = 'middle'
            
            if position:
                pyautogui.doubleClick(position.x, position.y, button=button_str)
            else:
                pyautogui.doubleClick(button=button_str)
            
            self.logger.debug("Mouse double-clicked", position=position, button=button)
            return True
            
        except Exception as e:
            self.logger.error("Failed to double-click mouse", position=position, button=button, error=str(e))
            return False
    
    def _right_click_mouse(self, position: Optional[Position]) -> bool:
        """Right-click mouse at position."""
        try:
            if position:
                pyautogui.rightClick(position.x, position.y)
            else:
                pyautogui.rightClick()
            
            self.logger.debug("Mouse right-clicked", position=position)
            return True
            
        except Exception as e:
            self.logger.error("Failed to right-click mouse", position=position, error=str(e))
            return False
    
    def _scroll_mouse(self, position: Optional[Position], direction: Optional[ScrollDirection], amount: int) -> bool:
        """Scroll mouse at position."""
        try:
            if position:
                pyautogui.moveTo(position.x, position.y)
            
            scroll_amount = amount
            if direction == ScrollDirection.DOWN or direction == ScrollDirection.LEFT:
                scroll_amount = -amount
            
            if direction in [ScrollDirection.UP, ScrollDirection.DOWN]:
                pyautogui.scroll(scroll_amount)
            else:
                pyautogui.hscroll(scroll_amount)
            
            self.logger.debug("Mouse scrolled", position=position, direction=direction, amount=amount)
            return True
            
        except Exception as e:
            self.logger.error("Failed to scroll mouse", position=position, direction=direction, error=str(e))
            return False
    
    def _drag_mouse(self, start_pos: Optional[Position], end_pos: Optional[Position], button: Optional[MouseButton]) -> bool:
        """Drag mouse from start to end position."""
        try:
            if not start_pos or not end_pos:
                return False
            
            button_str = 'left'
            if button == MouseButton.RIGHT:
                button_str = 'right'
            elif button == MouseButton.MIDDLE:
                button_str = 'middle'
            
            pyautogui.dragTo(end_pos.x, end_pos.y, duration=self.default_delay * 2, button=button_str)
            
            self.logger.debug("Mouse dragged", start_pos=start_pos, end_pos=end_pos, button=button)
            return True
            
        except Exception as e:
            self.logger.error("Failed to drag mouse", start_pos=start_pos, end_pos=end_pos, error=str(e))
            return False
    
    def get_mouse_position(self) -> Position:
        """Get current mouse position."""
        x, y = pyautogui.position()
        return Position(x=x, y=y)
    
    def is_safe_position(self, position: Position) -> bool:
        """Check if position is safe for interaction."""
        if not self.safe_mode:
            return True
        
        try:
            screen_width, screen_height = pyautogui.size()
            return 0 <= position.x <= screen_width and 0 <= position.y <= screen_height
        except:
            return False
