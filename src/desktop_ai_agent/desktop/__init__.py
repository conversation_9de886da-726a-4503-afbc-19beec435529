"""
Desktop Interaction Engine (DIE) for Desktop AI Agent.

This package provides cross-platform desktop interaction capabilities including
window management, input simulation, screen capture, and application control.
"""

from desktop_ai_agent.desktop.engine import DesktopInteractionEngine
from desktop_ai_agent.desktop.window_manager import WindowManager
from desktop_ai_agent.desktop.input_controller import InputController
from desktop_ai_agent.desktop.screen_analyzer import ScreenAnalyzer

__all__ = [
    "DesktopInteractionEngine",
    "WindowManager", 
    "InputController",
    "ScreenAnalyzer",
]
