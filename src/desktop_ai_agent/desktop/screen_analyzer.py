"""
Screen analysis and capture for Desktop AI Agent.

This module provides screen capture, image analysis, and visual element detection
capabilities across different platforms.
"""

import base64
import io
import platform
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import cv2
import numpy as np
import pyautogui
import structlog
from PIL import Image, ImageDraw, ImageFont

from desktop_ai_agent.desktop.models import Position, Rectangle, Screenshot, ScreenInfo, Size


class ScreenAnalyzer:
    """Screen capture and analysis system."""
    
    def __init__(self, default_format: str = "PNG", default_quality: int = 95):
        self.platform = platform.system().lower()
        self.default_format = default_format.upper()
        self.default_quality = default_quality
        self.logger = structlog.get_logger(__name__)
        
        # Disable pyautogui failsafe for screenshots
        pyautogui.FAILSAFE = False
        
        self.logger.info("Screen analyzer initialized", platform=self.platform)
    
    def get_screen_info(self) -> List[ScreenInfo]:
        """Get information about all available screens."""
        screens = []
        
        try:
            # Get primary screen info
            width, height = pyautogui.size()
            primary_screen = ScreenInfo(
                screen_id=0,
                is_primary=True,
                position=Position(x=0, y=0),
                size=Size(width=width, height=height),
                scale_factor=1.0
            )
            screens.append(primary_screen)
            
            # TODO: Add multi-monitor support for different platforms
            # This would require platform-specific implementations
            
        except Exception as e:
            self.logger.error("Failed to get screen info", error=str(e))
        
        return screens
    
    def capture_screenshot(
        self,
        region: Optional[Rectangle] = None,
        format: Optional[str] = None,
        quality: Optional[int] = None,
        include_cursor: bool = False
    ) -> Optional[Screenshot]:
        """Capture a screenshot of the screen or a specific region."""
        try:
            format = format or self.default_format
            quality = quality or self.default_quality
            
            # Capture screenshot
            if region:
                # Capture specific region
                pil_image = pyautogui.screenshot(region=(region.x, region.y, region.width, region.height))
            else:
                # Capture full screen
                pil_image = pyautogui.screenshot()
            
            # Convert to bytes
            image_buffer = io.BytesIO()
            if format.upper() == "JPEG" or format.upper() == "JPG":
                pil_image.save(image_buffer, format="JPEG", quality=quality)
            else:
                pil_image.save(image_buffer, format=format)
            
            image_data = image_buffer.getvalue()
            
            # Create screenshot object
            screenshot = Screenshot(
                image_id=str(uuid.uuid4()),
                image_data=image_data,
                format=format,
                width=pil_image.width,
                height=pil_image.height,
                region=region,
                timestamp=datetime.utcnow(),
                metadata={
                    "quality": quality,
                    "include_cursor": include_cursor,
                    "file_size": len(image_data)
                }
            )
            
            self.logger.debug(
                "Screenshot captured",
                image_id=screenshot.image_id,
                width=screenshot.width,
                height=screenshot.height,
                region=region,
                format=format
            )
            
            return screenshot
            
        except Exception as e:
            self.logger.error("Failed to capture screenshot", region=region, error=str(e))
            return None
    
    def find_image_on_screen(
        self,
        template_image: bytes,
        confidence: float = 0.8,
        region: Optional[Rectangle] = None
    ) -> Optional[Position]:
        """Find a template image on the screen using template matching."""
        try:
            # Capture current screen
            screenshot = self.capture_screenshot(region=region)
            if not screenshot:
                return None
            
            # Convert images to OpenCV format
            screen_image = self._bytes_to_cv2(screenshot.image_data)
            template = self._bytes_to_cv2(template_image)
            
            if screen_image is None or template is None:
                return None
            
            # Perform template matching
            result = cv2.matchTemplate(screen_image, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= confidence:
                # Calculate center position of found template
                template_height, template_width = template.shape[:2]
                center_x = max_loc[0] + template_width // 2
                center_y = max_loc[1] + template_height // 2
                
                # Adjust for region offset if applicable
                if region:
                    center_x += region.x
                    center_y += region.y
                
                position = Position(x=center_x, y=center_y)
                
                self.logger.debug(
                    "Image found on screen",
                    position=position,
                    confidence=max_val
                )
                
                return position
            else:
                self.logger.debug("Image not found on screen", max_confidence=max_val, required=confidence)
                return None
                
        except Exception as e:
            self.logger.error("Failed to find image on screen", error=str(e))
            return None
    
    def find_text_on_screen(
        self,
        text: str,
        region: Optional[Rectangle] = None,
        case_sensitive: bool = False
    ) -> List[Position]:
        """Find text on screen using OCR."""
        try:
            # Take screenshot of the region or full screen
            screenshot = self.take_screenshot()
            if not screenshot:
                return []

            # Convert to PIL Image for OCR processing
            import io
            from PIL import Image

            image = Image.open(io.BytesIO(screenshot))

            # Crop to region if specified
            if region:
                image = image.crop((region.x, region.y,
                                  region.x + region.width,
                                  region.y + region.height))

            # Try to use pytesseract for OCR
            try:
                import pytesseract

                # Extract text with bounding boxes
                ocr_data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)

                positions = []
                search_text = text if case_sensitive else text.lower()

                for i, word in enumerate(ocr_data['text']):
                    if word.strip():  # Skip empty strings
                        word_to_check = word if case_sensitive else word.lower()

                        if search_text in word_to_check:
                            x = ocr_data['left'][i]
                            y = ocr_data['top'][i]

                            # Adjust coordinates if we cropped the image
                            if region:
                                x += region.x
                                y += region.y

                            positions.append(Position(x=x, y=y))

                self.logger.info("OCR text detection completed",
                               text=text, matches=len(positions))
                return positions

            except ImportError:
                self.logger.warning("pytesseract not available - install with: pip install pytesseract")
                # Fallback: simple pattern matching on screenshot (limited functionality)
                return []

        except Exception as e:
            self.logger.error("Failed to find text on screen", text=text, error=str(e))
            return []
    
    def analyze_screen_elements(
        self,
        region: Optional[Rectangle] = None
    ) -> Dict[str, Any]:
        """Analyze screen elements and UI components."""
        try:
            screenshot = self.capture_screenshot(region=region)
            if not screenshot:
                return {}
            
            # Convert to OpenCV format for analysis
            cv_image = self._bytes_to_cv2(screenshot.image_data)
            if cv_image is None:
                return {}
            
            # Basic image analysis
            analysis = {
                "screenshot_id": screenshot.image_id,
                "dimensions": {
                    "width": screenshot.width,
                    "height": screenshot.height
                },
                "color_analysis": self._analyze_colors(cv_image),
                "edge_analysis": self._analyze_edges(cv_image),
                "region": region.dict() if region else None,
                "timestamp": screenshot.timestamp.isoformat()
            }
            
            self.logger.debug("Screen elements analyzed", screenshot_id=screenshot.image_id)
            return analysis
            
        except Exception as e:
            self.logger.error("Failed to analyze screen elements", error=str(e))
            return {}
    
    def _bytes_to_cv2(self, image_bytes: bytes) -> Optional[np.ndarray]:
        """Convert image bytes to OpenCV format."""
        try:
            # Convert bytes to PIL Image
            pil_image = Image.open(io.BytesIO(image_bytes))
            
            # Convert PIL to OpenCV format
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            return cv_image
            
        except Exception as e:
            self.logger.error("Failed to convert image to OpenCV format", error=str(e))
            return None
    
    def _analyze_colors(self, cv_image: np.ndarray) -> Dict[str, Any]:
        """Analyze color distribution in image."""
        try:
            # Calculate color histogram
            hist_b = cv2.calcHist([cv_image], [0], None, [256], [0, 256])
            hist_g = cv2.calcHist([cv_image], [1], None, [256], [0, 256])
            hist_r = cv2.calcHist([cv_image], [2], None, [256], [0, 256])
            
            # Calculate dominant colors (simplified)
            mean_color = cv_image.mean(axis=(0, 1))
            
            return {
                "mean_color": {
                    "b": float(mean_color[0]),
                    "g": float(mean_color[1]),
                    "r": float(mean_color[2])
                },
                "brightness": float(cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY).mean())
            }
            
        except Exception as e:
            self.logger.error("Failed to analyze colors", error=str(e))
            return {}
    
    def _analyze_edges(self, cv_image: np.ndarray) -> Dict[str, Any]:
        """Analyze edges and contours in image."""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply Canny edge detection
            edges = cv2.Canny(gray, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Analyze contours
            contour_areas = [cv2.contourArea(contour) for contour in contours]
            
            return {
                "edge_density": float(np.sum(edges > 0) / edges.size),
                "contour_count": len(contours),
                "average_contour_area": float(np.mean(contour_areas)) if contour_areas else 0.0,
                "max_contour_area": float(np.max(contour_areas)) if contour_areas else 0.0
            }
            
        except Exception as e:
            self.logger.error("Failed to analyze edges", error=str(e))
            return {}
    
    def create_annotated_screenshot(
        self,
        screenshot: Screenshot,
        annotations: List[Dict[str, Any]]
    ) -> Optional[Screenshot]:
        """Create an annotated version of a screenshot."""
        try:
            # Load original image
            pil_image = Image.open(io.BytesIO(screenshot.image_data))
            draw = ImageDraw.Draw(pil_image)
            
            # Try to load a font (fallback to default if not available)
            try:
                font = ImageFont.truetype("arial.ttf", 12)
            except:
                font = ImageFont.load_default()
            
            # Add annotations
            for annotation in annotations:
                annotation_type = annotation.get("type", "rectangle")
                
                if annotation_type == "rectangle":
                    rect = annotation.get("rectangle")
                    if rect:
                        draw.rectangle(
                            [rect["x"], rect["y"], rect["x"] + rect["width"], rect["y"] + rect["height"]],
                            outline="red",
                            width=2
                        )
                
                elif annotation_type == "point":
                    point = annotation.get("position")
                    if point:
                        radius = 5
                        draw.ellipse(
                            [point["x"] - radius, point["y"] - radius, point["x"] + radius, point["y"] + radius],
                            fill="red"
                        )
                
                elif annotation_type == "text":
                    position = annotation.get("position")
                    text = annotation.get("text", "")
                    if position and text:
                        draw.text((position["x"], position["y"]), text, fill="red", font=font)
            
            # Convert back to bytes
            image_buffer = io.BytesIO()
            pil_image.save(image_buffer, format=screenshot.format)
            annotated_data = image_buffer.getvalue()
            
            # Create new screenshot object
            annotated_screenshot = Screenshot(
                image_id=str(uuid.uuid4()),
                image_data=annotated_data,
                format=screenshot.format,
                width=pil_image.width,
                height=pil_image.height,
                region=screenshot.region,
                timestamp=datetime.utcnow(),
                metadata={
                    **screenshot.metadata,
                    "annotated": True,
                    "original_id": screenshot.image_id,
                    "annotation_count": len(annotations)
                }
            )
            
            return annotated_screenshot
            
        except Exception as e:
            self.logger.error("Failed to create annotated screenshot", error=str(e))
            return None
    
    def screenshot_to_base64(self, screenshot: Screenshot) -> str:
        """Convert screenshot to base64 string."""
        return base64.b64encode(screenshot.image_data).decode('utf-8')
    
    def base64_to_screenshot(self, base64_data: str, format: str = "PNG") -> Optional[Screenshot]:
        """Convert base64 string to screenshot object."""
        try:
            image_data = base64.b64decode(base64_data)
            pil_image = Image.open(io.BytesIO(image_data))
            
            screenshot = Screenshot(
                image_id=str(uuid.uuid4()),
                image_data=image_data,
                format=format,
                width=pil_image.width,
                height=pil_image.height,
                timestamp=datetime.utcnow()
            )
            
            return screenshot
            
        except Exception as e:
            self.logger.error("Failed to convert base64 to screenshot", error=str(e))
            return None
