"""
Main application entry point for Desktop AI Agent.

This module provides the main application initialization, component setup,
and startup sequence for the Desktop AI Agent system.
"""

import asyncio
import signal
import sys
from pathlib import Path
from typing import Optional

import structlog
import uvicorn

from desktop_ai_agent.ai.engine import ModelInferenceEngine
from desktop_ai_agent.api.app import create_app
from desktop_ai_agent.core.config import Settings, get_settings
from desktop_ai_agent.core.orchestrator import CentralOrchestrator, Component
from desktop_ai_agent.desktop.engine import DesktopInteractionEngine
from desktop_ai_agent.monitoring.health import HealthMonitor
from desktop_ai_agent.monitoring.logger import setup_logging
from desktop_ai_agent.monitoring.metrics import MetricsCollector
from desktop_ai_agent.planning.engine import TaskPlanningEngine
from desktop_ai_agent.safety.engine import SafetyEngine
from desktop_ai_agent.storage.engine import StateManagementEngine


class DesktopAIAgent:
    """Main Desktop AI Agent application."""
    
    def __init__(self, settings: Optional[Settings] = None):
        self.settings = settings or get_settings()
        self.logger = None  # Will be set after logging setup
        
        # Core components
        self.orchestrator: Optional[CentralOrchestrator] = None
        self.desktop_engine: Optional[DesktopInteractionEngine] = None
        self.planning_engine: Optional[TaskPlanningEngine] = None
        self.safety_engine: Optional[SafetyEngine] = None
        self.ai_engine: Optional[ModelInferenceEngine] = None
        self.storage_engine: Optional[StateManagementEngine] = None
        
        # Monitoring components
        self.metrics_collector: Optional[MetricsCollector] = None
        self.health_monitor: Optional[HealthMonitor] = None
        
        # FastAPI app
        self.app = None
        
        # Shutdown event
        self.shutdown_event = asyncio.Event()
    
    async def initialize(self) -> None:
        """Initialize the Desktop AI Agent application."""
        # Set up logging first
        setup_logging(self.settings)
        self.logger = structlog.get_logger(__name__)
        
        self.logger.info("Initializing Desktop AI Agent", version="0.1.0")
        
        try:
            # Initialize monitoring components
            await self._initialize_monitoring()
            
            # Initialize core engines
            await self._initialize_engines()
            
            # Initialize orchestrator
            await self._initialize_orchestrator()
            
            # Set up component integrations
            await self._setup_integrations()
            
            # Initialize FastAPI app
            self._initialize_api()
            
            self.logger.info("Desktop AI Agent initialization completed")
            
        except Exception as e:
            self.logger.error("Desktop AI Agent initialization failed", error=str(e))
            raise
    
    async def _initialize_monitoring(self) -> None:
        """Initialize monitoring components."""
        self.logger.info("Initializing monitoring components")
        
        # Initialize metrics collector
        self.metrics_collector = MetricsCollector(retention_hours=24)
        await self.metrics_collector.start()
        
        # Initialize health monitor
        self.health_monitor = HealthMonitor(check_interval=30)
        await self.health_monitor.start()
        
        self.logger.info("Monitoring components initialized")
    
    async def _initialize_engines(self) -> None:
        """Initialize all core engines."""
        self.logger.info("Initializing core engines")
        
        # Initialize storage engine first (other components depend on it)
        self.storage_engine = StateManagementEngine(self.settings)
        await self.storage_engine.start()
        
        # Initialize safety engine
        self.safety_engine = SafetyEngine(self.settings)
        await self.safety_engine.start()
        
        # Initialize desktop interaction engine
        self.desktop_engine = DesktopInteractionEngine(self.settings)
        await self.desktop_engine.start()
        
        # Initialize AI engine
        self.ai_engine = ModelInferenceEngine(self.settings)
        await self.ai_engine.start()
        
        # Initialize task planning engine
        self.planning_engine = TaskPlanningEngine(self.settings)
        await self.planning_engine.start()
        
        self.logger.info("Core engines initialized")
    
    async def _initialize_orchestrator(self) -> None:
        """Initialize the central orchestrator."""
        self.logger.info("Initializing central orchestrator")
        
        # Create orchestrator
        self.orchestrator = CentralOrchestrator(self.settings)
        
        # Register all components
        components = [
            self.storage_engine.get_component_info(),
            self.safety_engine.get_component_info(),
            self.desktop_engine.get_component_info(),
            self.ai_engine.get_component_info(),
            self.planning_engine.get_component_info(),
        ]
        
        for component in components:
            self.orchestrator.register_component(component)
        
        # Start orchestrator
        await self.orchestrator.start()
        
        self.logger.info("Central orchestrator initialized")
    
    async def _setup_integrations(self) -> None:
        """Set up integrations between components."""
        self.logger.info("Setting up component integrations")
        
        # Set up task planning engine executor callback
        self.planning_engine.set_executor_callback(self._execute_subtask)
        
        # Register components with health monitor
        if self.health_monitor:
            self.health_monitor.register_component("database_manager", self.storage_engine.database_manager)
            self.health_monitor.register_component("model_manager", self.ai_engine.model_manager)
        
        self.logger.info("Component integrations set up")
    
    def _initialize_api(self) -> None:
        """Initialize FastAPI application."""
        self.logger.info("Initializing API application")
        
        if not self.orchestrator:
            raise RuntimeError("Orchestrator not initialized")
        
        self.app = create_app(self.settings, self.orchestrator)
        
        self.logger.info("API application initialized")
    
    async def _execute_subtask(self, subtask, context) -> dict:
        """Execute a subtask (callback for task planning engine)."""
        try:
            self.logger.debug("Executing subtask", subtask_id=subtask.subtask_id, action_type=subtask.action_type)
            
            # Route subtask to appropriate engine based on action type
            if subtask.action_type.startswith("window_"):
                return await self._execute_desktop_subtask(subtask, context)
            elif subtask.action_type in ["click", "type_text", "screenshot"]:
                return await self._execute_desktop_subtask(subtask, context)
            elif subtask.action_type.startswith("ai_"):
                return await self._execute_ai_subtask(subtask, context)
            else:
                return await self._execute_generic_subtask(subtask, context)
                
        except Exception as e:
            self.logger.error("Subtask execution failed", subtask_id=subtask.subtask_id, error=str(e))
            return {"success": False, "error": str(e)}
    
    async def _execute_desktop_subtask(self, subtask, context) -> dict:
        """Execute a desktop-related subtask."""
        if not self.desktop_engine:
            return {"success": False, "error": "Desktop engine not available"}
        
        try:
            # Create desktop action from subtask
            from desktop_ai_agent.desktop.models import DesktopAction
            
            action = DesktopAction(
                action_id=subtask.subtask_id,
                action_type=subtask.action_type,
                parameters=subtask.parameters
            )
            
            # Execute action
            result = await self.desktop_engine.execute_desktop_action(action)
            
            return {
                "success": result.success,
                "result": result.result,
                "error": result.error_message,
                "execution_time": result.execution_time
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _execute_ai_subtask(self, subtask, context) -> dict:
        """Execute an AI-related subtask."""
        if not self.ai_engine:
            return {"success": False, "error": "AI engine not available"}

        try:
            from desktop_ai_agent.ai.models import InferenceRequest

            # Determine the type of AI task
            task_type = subtask.get("type", "general")
            task_description = subtask.get("description", "")

            if task_type == "planning":
                # AI planning task
                prompt = f"""You are a Desktop AI Agent. Create a detailed plan for this task:

Task: {task_description}
Context: {context}

Provide a step-by-step plan with specific actions that can be executed on a desktop computer."""

            elif task_type == "analysis":
                # AI analysis task
                prompt = f"""You are a Desktop AI Agent. Analyze this situation and provide recommendations:

Situation: {task_description}
Context: {context}

Provide analysis and actionable recommendations."""

            elif task_type == "decision":
                # AI decision making task
                prompt = f"""You are a Desktop AI Agent. Make a decision for this scenario:

Scenario: {task_description}
Context: {context}

Provide your decision with reasoning."""

            else:
                # General AI task
                prompt = f"""You are a Desktop AI Agent. Help with this task:

Task: {task_description}
Context: {context}

Provide helpful assistance and specific actions."""

            # Generate AI response
            request = InferenceRequest(
                model_id='local_codellama-7b-instruct.Q4_K_M',
                prompt=prompt,
                max_tokens=200,
                temperature=0.3
            )

            response = await self.ai_engine.generate_response(request)

            if response.success:
                return {
                    "success": True,
                    "result": response.text,
                    "task_type": task_type,
                    "inference_time": response.inference_time_ms,
                    "tokens": response.total_tokens
                }
            else:
                return {
                    "success": False,
                    "error": f"AI inference failed: {response.error_message}"
                }

        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _execute_generic_subtask(self, subtask, context) -> dict:
        """Execute a generic subtask."""
        try:
            # For Phase 1, simulate task execution
            await asyncio.sleep(0.1)  # Simulate work
            
            return {
                "success": True,
                "result": f"Generic subtask '{subtask.name}' completed",
                "action_type": subtask.action_type
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def start(self) -> None:
        """Start the Desktop AI Agent application."""
        self.logger.info("Starting Desktop AI Agent")
        
        # Set up signal handlers
        self._setup_signal_handlers()
        
        # Start API server
        config = uvicorn.Config(
            app=self.app,
            host=self.settings.api.host,
            port=self.settings.api.port,
            log_level="info",
            access_log=True,
            reload=self.settings.api.reload and self.settings.is_development
        )
        
        server = uvicorn.Server(config)
        
        # Start server in background
        server_task = asyncio.create_task(server.serve())
        
        self.logger.info(
            "Desktop AI Agent started",
            host=self.settings.api.host,
            port=self.settings.api.port,
            environment=self.settings.environment
        )
        
        try:
            # Wait for shutdown signal
            await self.shutdown_event.wait()
        finally:
            # Shutdown server
            server.should_exit = True
            await server_task
    
    async def stop(self) -> None:
        """Stop the Desktop AI Agent application."""
        self.logger.info("Stopping Desktop AI Agent")
        
        try:
            # Stop components in reverse order
            if self.orchestrator:
                await self.orchestrator.stop()
            
            if self.planning_engine:
                await self.planning_engine.stop()
            
            if self.ai_engine:
                await self.ai_engine.stop()
            
            if self.desktop_engine:
                await self.desktop_engine.stop()
            
            if self.safety_engine:
                await self.safety_engine.stop()
            
            if self.storage_engine:
                await self.storage_engine.stop()
            
            # Stop monitoring
            if self.health_monitor:
                await self.health_monitor.stop()
            
            if self.metrics_collector:
                await self.metrics_collector.stop()
            
            self.logger.info("Desktop AI Agent stopped")
            
        except Exception as e:
            self.logger.error("Error during shutdown", error=str(e))
    
    def _setup_signal_handlers(self) -> None:
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            self.logger.info("Received shutdown signal", signal=signum)
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


async def main() -> None:
    """Main entry point."""
    # Load settings
    settings = get_settings()
    
    # Create and initialize application
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        await app.start()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Application failed: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        await app.stop()


def cli_main() -> None:
    """CLI entry point."""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Fatal error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    cli_main()
