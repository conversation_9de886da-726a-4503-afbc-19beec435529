"""
Metrics collection for Desktop AI Agent.

This module provides comprehensive metrics collection including
performance metrics, resource usage, and business metrics.
"""

import asyncio
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import psutil
import structlog


class MetricsCollector:
    """Comprehensive metrics collection system."""
    
    def __init__(self, retention_hours: int = 24):
        self.retention_hours = retention_hours
        self.logger = structlog.get_logger(__name__)
        
        # Metrics storage
        self.counters: Dict[str, int] = defaultdict(int)
        self.gauges: Dict[str, float] = {}
        self.histograms: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.timeseries: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1440))  # 24 hours of minutes
        
        # System metrics
        self.system_metrics = {}
        
        # Performance tracking
        self.request_times: deque = deque(maxlen=1000)
        self.task_times: deque = deque(maxlen=1000)
        
        # Background task
        self._metrics_task: Optional[asyncio.Task] = None
        
        self.logger.info("Metrics collector initialized", retention_hours=retention_hours)
    
    async def start(self) -> None:
        """Start metrics collection."""
        self._metrics_task = asyncio.create_task(self._collect_system_metrics())
        self.logger.info("Metrics collection started")
    
    async def stop(self) -> None:
        """Stop metrics collection."""
        if self._metrics_task:
            self._metrics_task.cancel()
            try:
                await self._metrics_task
            except asyncio.CancelledError:
                pass
        self.logger.info("Metrics collection stopped")
    
    # Counter methods
    
    def increment_counter(self, name: str, value: int = 1, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment a counter metric."""
        key = self._make_key(name, tags)
        self.counters[key] += value
    
    def get_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> int:
        """Get counter value."""
        key = self._make_key(name, tags)
        return self.counters.get(key, 0)
    
    # Gauge methods
    
    def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Set a gauge metric."""
        key = self._make_key(name, tags)
        self.gauges[key] = value
        
        # Also add to timeseries
        timestamp = datetime.utcnow()
        self.timeseries[key].append((timestamp, value))
        
        # Clean old timeseries data
        cutoff = timestamp - timedelta(hours=self.retention_hours)
        while self.timeseries[key] and self.timeseries[key][0][0] < cutoff:
            self.timeseries[key].popleft()
    
    def get_gauge(self, name: str, tags: Optional[Dict[str, str]] = None) -> Optional[float]:
        """Get gauge value."""
        key = self._make_key(name, tags)
        return self.gauges.get(key)
    
    # Histogram methods
    
    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a histogram value."""
        key = self._make_key(name, tags)
        self.histograms[key].append((datetime.utcnow(), value))
    
    def get_histogram_stats(self, name: str, tags: Optional[Dict[str, str]] = None) -> Dict[str, float]:
        """Get histogram statistics."""
        key = self._make_key(name, tags)
        values = [v for _, v in self.histograms[key]]
        
        if not values:
            return {}
        
        values.sort()
        n = len(values)
        
        return {
            "count": n,
            "min": values[0],
            "max": values[-1],
            "mean": sum(values) / n,
            "median": values[n // 2],
            "p95": values[int(n * 0.95)] if n > 0 else 0,
            "p99": values[int(n * 0.99)] if n > 0 else 0,
        }
    
    # Timing methods
    
    def record_request_time(self, duration_ms: float) -> None:
        """Record API request time."""
        self.request_times.append((datetime.utcnow(), duration_ms))
        self.record_histogram("api.request_time", duration_ms)
    
    def record_task_time(self, duration_ms: float, task_type: str = "unknown") -> None:
        """Record task execution time."""
        self.task_times.append((datetime.utcnow(), duration_ms))
        self.record_histogram("task.execution_time", duration_ms, {"type": task_type})
    
    # System metrics collection
    
    async def _collect_system_metrics(self) -> None:
        """Background task to collect system metrics."""
        while True:
            try:
                await self._update_system_metrics()
                await asyncio.sleep(60)  # Collect every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("System metrics collection error", error=str(e))
                await asyncio.sleep(60)
    
    async def _update_system_metrics(self) -> None:
        """Update system metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.set_gauge("system.cpu_percent", cpu_percent)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.set_gauge("system.memory_percent", memory.percent)
            self.set_gauge("system.memory_available_mb", memory.available / 1024 / 1024)
            self.set_gauge("system.memory_used_mb", memory.used / 1024 / 1024)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            self.set_gauge("system.disk_percent", (disk.used / disk.total) * 100)
            self.set_gauge("system.disk_free_gb", disk.free / 1024 / 1024 / 1024)
            
            # Network metrics (if available)
            try:
                net_io = psutil.net_io_counters()
                self.set_gauge("system.network_bytes_sent", net_io.bytes_sent)
                self.set_gauge("system.network_bytes_recv", net_io.bytes_recv)
            except:
                pass
            
            # Process metrics
            process = psutil.Process()
            self.set_gauge("process.cpu_percent", process.cpu_percent())
            self.set_gauge("process.memory_mb", process.memory_info().rss / 1024 / 1024)
            self.set_gauge("process.num_threads", process.num_threads())
            
        except Exception as e:
            self.logger.error("Failed to update system metrics", error=str(e))
    
    # Business metrics
    
    def record_task_created(self, task_type: str = "unknown") -> None:
        """Record task creation."""
        self.increment_counter("tasks.created", tags={"type": task_type})
    
    def record_task_completed(self, task_type: str = "unknown", success: bool = True) -> None:
        """Record task completion."""
        status = "success" if success else "failure"
        self.increment_counter("tasks.completed", tags={"type": task_type, "status": status})
    
    def record_api_request(self, method: str, endpoint: str, status_code: int) -> None:
        """Record API request."""
        self.increment_counter("api.requests", tags={
            "method": method,
            "endpoint": endpoint,
            "status": str(status_code)
        })
    
    def record_model_inference(self, model_id: str, tokens: int, duration_ms: float) -> None:
        """Record model inference."""
        self.increment_counter("ai.inferences", tags={"model": model_id})
        self.record_histogram("ai.inference_time", duration_ms, {"model": model_id})
        self.record_histogram("ai.tokens_generated", tokens, {"model": model_id})
    
    # Utility methods
    
    def _make_key(self, name: str, tags: Optional[Dict[str, str]] = None) -> str:
        """Create metric key with tags."""
        if not tags:
            return name
        
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """Get all current metrics."""
        return {
            "counters": dict(self.counters),
            "gauges": dict(self.gauges),
            "histograms": {k: self.get_histogram_stats(k.split('[')[0], 
                                                      self._parse_tags(k)) 
                          for k in self.histograms.keys()},
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def _parse_tags(self, key: str) -> Optional[Dict[str, str]]:
        """Parse tags from metric key."""
        if '[' not in key:
            return None
        
        tag_part = key.split('[')[1].rstrip(']')
        tags = {}
        for tag in tag_part.split(','):
            if '=' in tag:
                k, v = tag.split('=', 1)
                tags[k] = v
        
        return tags if tags else None
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        now = datetime.utcnow()
        hour_ago = now - timedelta(hours=1)
        
        # Recent request times
        recent_requests = [duration for timestamp, duration in self.request_times 
                          if timestamp > hour_ago]
        
        # Recent task times
        recent_tasks = [duration for timestamp, duration in self.task_times 
                       if timestamp > hour_ago]
        
        return {
            "requests_last_hour": len(recent_requests),
            "avg_request_time_ms": sum(recent_requests) / len(recent_requests) if recent_requests else 0,
            "tasks_last_hour": len(recent_tasks),
            "avg_task_time_ms": sum(recent_tasks) / len(recent_tasks) if recent_tasks else 0,
            "current_cpu_percent": self.get_gauge("system.cpu_percent", {}),
            "current_memory_percent": self.get_gauge("system.memory_percent", {}),
            "timestamp": now.isoformat()
        }
