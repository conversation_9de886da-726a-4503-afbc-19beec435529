"""
Logging configuration for Desktop AI Agent.

This module provides structured logging setup with multiple output formats
and log levels for comprehensive system monitoring.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

import structlog
from structlog.stdlib import LoggerFactory

from desktop_ai_agent.core.config import Settings


def setup_logging(settings: Settings) -> None:
    """Set up structured logging for the application."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.logging.structured else structlog.dev.Console<PERSON>enderer(),
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format=settings.logging.format,
        level=getattr(logging, settings.logging.level.upper()),
        stream=sys.stdout
    )
    
    # Set up file logging if configured
    log_file_path = settings.get_log_file_path()
    if log_file_path:
        # Ensure log directory exists
        log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Set up rotating file handler
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file_path,
            maxBytes=settings.logging.max_file_size,
            backupCount=settings.logging.backup_count,
            encoding='utf-8'
        )
        
        file_formatter = logging.Formatter(settings.logging.format)
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(getattr(logging, settings.logging.level.upper()))
        
        # Add file handler to root logger
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)
    
    # Configure specific loggers
    _configure_loggers(settings)
    
    logger = structlog.get_logger(__name__)
    logger.info("Logging configured", 
                level=settings.logging.level,
                structured=settings.logging.structured,
                file_logging=log_file_path is not None)


def _configure_loggers(settings: Settings) -> None:
    """Configure specific logger levels."""
    
    # Set levels for third-party libraries
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    # Set debug level for our components in development
    if settings.is_development:
        logging.getLogger("desktop_ai_agent").setLevel(logging.DEBUG)
    
    # Quiet down noisy loggers in production
    if settings.is_production:
        logging.getLogger("asyncio").setLevel(logging.WARNING)
        logging.getLogger("concurrent.futures").setLevel(logging.WARNING)


class ContextualLogger:
    """Contextual logger that maintains request/task context."""
    
    def __init__(self, name: str):
        self.logger = structlog.get_logger(name)
        self.context = {}
    
    def bind(self, **kwargs) -> 'ContextualLogger':
        """Bind context to logger."""
        new_logger = ContextualLogger(self.logger.name)
        new_logger.logger = self.logger.bind(**kwargs)
        new_logger.context = {**self.context, **kwargs}
        return new_logger
    
    def debug(self, msg: str, **kwargs) -> None:
        """Log debug message."""
        self.logger.debug(msg, **kwargs)
    
    def info(self, msg: str, **kwargs) -> None:
        """Log info message."""
        self.logger.info(msg, **kwargs)
    
    def warning(self, msg: str, **kwargs) -> None:
        """Log warning message."""
        self.logger.warning(msg, **kwargs)
    
    def error(self, msg: str, **kwargs) -> None:
        """Log error message."""
        self.logger.error(msg, **kwargs)
    
    def critical(self, msg: str, **kwargs) -> None:
        """Log critical message."""
        self.logger.critical(msg, **kwargs)


def get_logger(name: str) -> ContextualLogger:
    """Get a contextual logger instance."""
    return ContextualLogger(name)
