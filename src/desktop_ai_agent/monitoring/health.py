"""
Health monitoring for Desktop AI Agent.

This module provides comprehensive health monitoring for all system
components with automated health checks and alerting.
"""

import asyncio
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional

import structlog
from pydantic import BaseModel

from desktop_ai_agent.core.orchestrator import ComponentStatus


class HealthStatus(str, Enum):
    """Health status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class HealthCheck(BaseModel):
    """Health check result model."""
    component: str
    status: HealthStatus
    message: str
    timestamp: datetime
    response_time_ms: float
    details: Dict[str, Any] = {}


class HealthMonitor:
    """Comprehensive health monitoring system."""
    
    def __init__(self, check_interval: int = 30):
        self.check_interval = check_interval
        self.logger = structlog.get_logger(__name__)
        
        # Health check results
        self.health_checks: Dict[str, HealthCheck] = {}
        self.health_history: List[HealthCheck] = []
        
        # Component references (will be set by orchestrator)
        self.components: Dict[str, Any] = {}
        
        # Background task
        self._monitor_task: Optional[asyncio.Task] = None
        
        # Health check functions
        self.check_functions = {
            "system": self._check_system_health,
            "database": self._check_database_health,
            "models": self._check_model_health,
            "api": self._check_api_health,
        }
        
        self.logger.info("Health monitor initialized", check_interval=check_interval)
    
    async def start(self) -> None:
        """Start health monitoring."""
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        self.logger.info("Health monitoring started")
    
    async def stop(self) -> None:
        """Stop health monitoring."""
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        self.logger.info("Health monitoring stopped")
    
    def register_component(self, name: str, component: Any) -> None:
        """Register a component for health monitoring."""
        self.components[name] = component
        self.logger.debug("Component registered for health monitoring", component=name)
    
    async def _monitor_loop(self) -> None:
        """Main health monitoring loop."""
        while True:
            try:
                await self._run_health_checks()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Health monitoring error", error=str(e))
                await asyncio.sleep(self.check_interval)
    
    async def _run_health_checks(self) -> None:
        """Run all health checks."""
        check_tasks = []
        
        for check_name, check_func in self.check_functions.items():
            task = asyncio.create_task(self._run_single_check(check_name, check_func))
            check_tasks.append(task)
        
        # Wait for all checks to complete
        results = await asyncio.gather(*check_tasks, return_exceptions=True)
        
        # Process results
        for i, result in enumerate(results):
            check_name = list(self.check_functions.keys())[i]
            
            if isinstance(result, Exception):
                self.logger.error("Health check failed", check=check_name, error=str(result))
                self._record_health_check(HealthCheck(
                    component=check_name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check failed: {str(result)}",
                    timestamp=datetime.utcnow(),
                    response_time_ms=0.0
                ))
            elif isinstance(result, HealthCheck):
                self._record_health_check(result)
    
    async def _run_single_check(self, check_name: str, check_func) -> HealthCheck:
        """Run a single health check with timing."""
        start_time = datetime.utcnow()
        
        try:
            result = await check_func()
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            result.response_time_ms = response_time
            return result
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            return HealthCheck(
                component=check_name,
                status=HealthStatus.UNHEALTHY,
                message=f"Check failed: {str(e)}",
                timestamp=datetime.utcnow(),
                response_time_ms=response_time
            )
    
    def _record_health_check(self, check: HealthCheck) -> None:
        """Record a health check result."""
        self.health_checks[check.component] = check
        self.health_history.append(check)
        
        # Keep only recent history (last 1000 checks)
        if len(self.health_history) > 1000:
            self.health_history = self.health_history[-500:]
        
        # Log status changes
        if check.status != HealthStatus.HEALTHY:
            self.logger.warning(
                "Component health issue",
                component=check.component,
                status=check.status,
                message=check.message
            )
    
    # Individual health check functions
    
    async def _check_system_health(self) -> HealthCheck:
        """Check system health."""
        try:
            import psutil
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Check memory usage
            memory = psutil.virtual_memory()
            
            # Check disk usage
            disk = psutil.disk_usage('/')
            
            # Determine status
            status = HealthStatus.HEALTHY
            issues = []
            
            if cpu_percent > 90:
                status = HealthStatus.DEGRADED
                issues.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if memory.percent > 90:
                status = HealthStatus.DEGRADED
                issues.append(f"High memory usage: {memory.percent:.1f}%")
            
            if (disk.used / disk.total) > 0.95:
                status = HealthStatus.DEGRADED
                issues.append(f"Low disk space: {(disk.free / 1024**3):.1f}GB free")
            
            message = "System healthy" if status == HealthStatus.HEALTHY else "; ".join(issues)
            
            return HealthCheck(
                component="system",
                status=status,
                message=message,
                timestamp=datetime.utcnow(),
                response_time_ms=0.0,
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_free_gb": disk.free / 1024**3
                }
            )
            
        except Exception as e:
            return HealthCheck(
                component="system",
                status=HealthStatus.UNHEALTHY,
                message=f"System check failed: {str(e)}",
                timestamp=datetime.utcnow(),
                response_time_ms=0.0
            )
    
    async def _check_database_health(self) -> HealthCheck:
        """Check database health."""
        try:
            database_manager = self.components.get("database_manager")
            if not database_manager:
                return HealthCheck(
                    component="database",
                    status=HealthStatus.UNKNOWN,
                    message="Database manager not registered",
                    timestamp=datetime.utcnow(),
                    response_time_ms=0.0
                )
            
            # Try a simple database operation
            with database_manager.get_session() as session:
                session.execute("SELECT 1")
            
            return HealthCheck(
                component="database",
                status=HealthStatus.HEALTHY,
                message="Database connection healthy",
                timestamp=datetime.utcnow(),
                response_time_ms=0.0
            )
            
        except Exception as e:
            return HealthCheck(
                component="database",
                status=HealthStatus.UNHEALTHY,
                message=f"Database check failed: {str(e)}",
                timestamp=datetime.utcnow(),
                response_time_ms=0.0
            )
    
    async def _check_model_health(self) -> HealthCheck:
        """Check AI model health."""
        try:
            model_manager = self.components.get("model_manager")
            if not model_manager:
                return HealthCheck(
                    component="models",
                    status=HealthStatus.UNKNOWN,
                    message="Model manager not registered",
                    timestamp=datetime.utcnow(),
                    response_time_ms=0.0
                )
            
            # Check if any models are loaded
            loaded_models = model_manager.list_loaded_models()
            available_models = model_manager.list_models()
            
            if not available_models:
                status = HealthStatus.DEGRADED
                message = "No AI models available"
            elif not loaded_models:
                status = HealthStatus.DEGRADED
                message = "No AI models loaded"
            else:
                status = HealthStatus.HEALTHY
                message = f"{len(loaded_models)} models loaded, {len(available_models)} available"
            
            return HealthCheck(
                component="models",
                status=status,
                message=message,
                timestamp=datetime.utcnow(),
                response_time_ms=0.0,
                details={
                    "loaded_models": len(loaded_models),
                    "available_models": len(available_models)
                }
            )
            
        except Exception as e:
            return HealthCheck(
                component="models",
                status=HealthStatus.UNHEALTHY,
                message=f"Model check failed: {str(e)}",
                timestamp=datetime.utcnow(),
                response_time_ms=0.0
            )
    
    async def _check_api_health(self) -> HealthCheck:
        """Check API health."""
        try:
            # Simple API health check
            # In a full implementation, this might make an HTTP request to the API
            
            return HealthCheck(
                component="api",
                status=HealthStatus.HEALTHY,
                message="API service healthy",
                timestamp=datetime.utcnow(),
                response_time_ms=0.0
            )
            
        except Exception as e:
            return HealthCheck(
                component="api",
                status=HealthStatus.UNHEALTHY,
                message=f"API check failed: {str(e)}",
                timestamp=datetime.utcnow(),
                response_time_ms=0.0
            )
    
    # Public API methods
    
    def get_overall_health(self) -> HealthStatus:
        """Get overall system health status."""
        if not self.health_checks:
            return HealthStatus.UNKNOWN
        
        statuses = [check.status for check in self.health_checks.values()]
        
        if any(status == HealthStatus.UNHEALTHY for status in statuses):
            return HealthStatus.UNHEALTHY
        elif any(status == HealthStatus.DEGRADED for status in statuses):
            return HealthStatus.DEGRADED
        elif all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary."""
        overall_status = self.get_overall_health()
        
        component_statuses = {}
        for component, check in self.health_checks.items():
            component_statuses[component] = {
                "status": check.status,
                "message": check.message,
                "last_check": check.timestamp.isoformat(),
                "response_time_ms": check.response_time_ms
            }
        
        return {
            "overall_status": overall_status,
            "components": component_statuses,
            "last_updated": datetime.utcnow().isoformat()
        }
    
    def get_health_history(self, component: Optional[str] = None, hours: int = 24) -> List[HealthCheck]:
        """Get health check history."""
        cutoff = datetime.utcnow() - timedelta(hours=hours)
        
        history = [check for check in self.health_history if check.timestamp > cutoff]
        
        if component:
            history = [check for check in history if check.component == component]
        
        return sorted(history, key=lambda x: x.timestamp, reverse=True)
