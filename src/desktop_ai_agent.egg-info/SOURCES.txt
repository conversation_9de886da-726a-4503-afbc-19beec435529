README.md
pyproject.toml
setup.py
src/desktop_ai_agent/__init__.py
src/desktop_ai_agent/cli.py
src/desktop_ai_agent/main.py
src/desktop_ai_agent.egg-info/PKG-INFO
src/desktop_ai_agent.egg-info/SOURCES.txt
src/desktop_ai_agent.egg-info/dependency_links.txt
src/desktop_ai_agent.egg-info/entry_points.txt
src/desktop_ai_agent.egg-info/not-zip-safe
src/desktop_ai_agent.egg-info/requires.txt
src/desktop_ai_agent.egg-info/top_level.txt
src/desktop_ai_agent/ai/__init__.py
src/desktop_ai_agent/ai/context_manager.py
src/desktop_ai_agent/ai/engine.py
src/desktop_ai_agent/ai/model_manager.py
src/desktop_ai_agent/ai/models.py
src/desktop_ai_agent/api/__init__.py
src/desktop_ai_agent/api/app.py
src/desktop_ai_agent/api/dependencies.py
src/desktop_ai_agent/api/models.py
src/desktop_ai_agent/api/routes/__init__.py
src/desktop_ai_agent/api/routes/desktop.py
src/desktop_ai_agent/api/routes/tasks.py
src/desktop_ai_agent/core/__init__.py
src/desktop_ai_agent/core/config.py
src/desktop_ai_agent/core/orchestrator.py
src/desktop_ai_agent/desktop/__init__.py
src/desktop_ai_agent/desktop/engine.py
src/desktop_ai_agent/desktop/input_controller.py
src/desktop_ai_agent/desktop/models.py
src/desktop_ai_agent/desktop/screen_analyzer.py
src/desktop_ai_agent/desktop/window_manager.py
src/desktop_ai_agent/monitoring/__init__.py
src/desktop_ai_agent/monitoring/health.py
src/desktop_ai_agent/monitoring/logger.py
src/desktop_ai_agent/monitoring/metrics.py
src/desktop_ai_agent/planning/__init__.py
src/desktop_ai_agent/planning/decomposer.py
src/desktop_ai_agent/planning/engine.py
src/desktop_ai_agent/planning/models.py
src/desktop_ai_agent/planning/scheduler.py
src/desktop_ai_agent/safety/__init__.py
src/desktop_ai_agent/safety/audit_logger.py
src/desktop_ai_agent/safety/engine.py
src/desktop_ai_agent/safety/models.py
src/desktop_ai_agent/safety/permission_manager.py
src/desktop_ai_agent/safety/risk_assessor.py
src/desktop_ai_agent/storage/__init__.py
src/desktop_ai_agent/storage/database.py
src/desktop_ai_agent/storage/engine.py
src/desktop_ai_agent/storage/session_manager.py
src/desktop_ai_agent/utils/model_downloader.py
tests/test_core.py
tests/test_desktop.py