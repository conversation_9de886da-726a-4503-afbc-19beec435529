# Core framework - minimal for testing
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database and storage
sqlalchemy>=2.0.0

# Desktop interaction (basic)
pyautogui>=0.9.54
pynput>=1.7.6
psutil>=5.9.6

# Cross-platform support
python-xlib>=0.33; sys_platform == "linux"

# Logging and monitoring
structlog>=23.2.0

# HTTP and networking
httpx>=0.25.0

# Configuration and utilities
click>=8.1.0
python-dotenv>=1.0.0
pyyaml>=6.0.1

# Image processing
pillow>=10.1.0
