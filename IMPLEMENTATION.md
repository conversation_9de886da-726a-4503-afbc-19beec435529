# Desktop AI Agent - Implementation Guide

This document provides a comprehensive guide for implementing and using the Desktop AI Agent system.

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- Operating System: Windows 10+, macOS 10.14+, or Linux (Ubuntu 18.04+)
- Minimum 8GB RAM (16GB recommended for larger models)
- 10GB free disk space for models and data

### Installation

```bash
# Clone the repository
git clone https://github.com/desktop-ai-agent/desktop-ai-agent.git
cd desktop-ai-agent

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install the package in development mode
pip install -e .
```

### Setting Up AI Models

The Desktop AI Agent requires a local AI model for task planning and execution. Here's how to set one up:

#### Option 1: Automatic Model Discovery

```bash
# Check for existing models on your system
python -c "
from desktop_ai_agent.utils.model_downloader import ModelDownloader
from desktop_ai_agent.core.config import get_settings

settings = get_settings()
downloader = ModelDownloader(settings)
models = downloader.discover_existing_models()

print(f'Found {len(models)} models:')
for model in models:
    if model['suitable']:
        print(f'✓ {model[\"filename\"]} - {model[\"size_mb\"]:.1f}MB - SUITABLE')
    else:
        print(f'  {model[\"filename\"]} - {model[\"size_mb\"]:.1f}MB')
"
```

#### Option 2: Download a Recommended Model

```bash
# List recommended models
daa models list

# Download CodeLlama-7B-Instruct (recommended for task planning)
daa models download codellama-7b-instruct

# Or download Mistral-7B-Instruct (good general model)
daa models download mistral-7b-instruct
```

#### Option 3: Manual Model Setup

1. Download a GGUF model file (recommended: 7B or 13B parameter models)
2. Place it in the `data/models/` directory
3. Update the configuration to point to your model:

```bash
# Create configuration file
cat > config.json << EOF
{
  "ai_model": {
    "model_path": "data/models/your-model.gguf",
    "context_length": 4096,
    "temperature": 0.7
  }
}
EOF
```

### Starting the System

```bash
# Run system diagnostics
daa test

# Start the Desktop AI Agent service
daa start

# Or start with custom configuration
daa start --config config.json --host 0.0.0.0 --port 8080
```

## 🔧 Configuration

### Environment Variables

Set these environment variables to configure the system:

```bash
export DAA_ENVIRONMENT=production
export DAA_API_HOST=0.0.0.0
export DAA_API_PORT=8000
export DAA_DATABASE_URL=sqlite:///data/desktop_ai_agent.db
export DAA_AI_MODEL_PATH=data/models/your-model.gguf
export DAA_LOGGING_LEVEL=INFO
export DAA_DESKTOP_SAFE_MODE=true
```

### Configuration File

Create a `config.json` file:

```json
{
  "environment": "production",
  "api": {
    "host": "0.0.0.0",
    "port": 8000,
    "cors_origins": ["http://localhost:3000"],
    "debug": false
  },
  "database": {
    "url": "sqlite:///data/desktop_ai_agent.db",
    "echo": false,
    "pool_size": 10
  },
  "ai_model": {
    "model_path": "data/models/codellama-7b-instruct.Q4_K_M.gguf",
    "context_length": 4096,
    "temperature": 0.7,
    "n_threads": 4,
    "n_gpu_layers": 0
  },
  "desktop": {
    "safe_mode": true,
    "screenshot_format": "PNG",
    "screenshot_quality": 95,
    "input_delay": 0.1
  },
  "logging": {
    "level": "INFO",
    "structured": true,
    "file_path": "logs/desktop_ai_agent.log"
  }
}
```

## 📡 API Usage

### Basic Task Creation

```python
import requests
import json

# Create a screenshot task
response = requests.post("http://localhost:8000/api/v1/tasks", json={
    "name": "Take Screenshot",
    "description": "Capture a screenshot of the current desktop",
    "type": "simple",
    "parameters": {
        "action": "screenshot",
        "format": "PNG",
        "quality": 95
    }
})

task = response.json()
print(f"Task created: {task['task_id']}")

# Monitor task progress
import time
while True:
    status_response = requests.get(f"http://localhost:8000/api/v1/tasks/{task['task_id']}")
    status = status_response.json()
    
    print(f"Status: {status['status']}, Progress: {status['progress']:.1%}")
    
    if status['status'] in ['completed', 'failed']:
        break
    
    time.sleep(1)

if status['status'] == 'completed':
    print("Task completed successfully!")
    print(f"Result: {status['result']}")
else:
    print(f"Task failed: {status['error_message']}")
```

### Desktop Interaction

```python
# List all windows
windows_response = requests.get("http://localhost:8000/api/v1/desktop/windows")
windows = windows_response.json()

print("Open windows:")
for window in windows['windows']:
    print(f"- {window['title']} ({window['application']})")

# Focus a specific window
if windows['windows']:
    window_id = windows['windows'][0]['window_id']
    focus_response = requests.post(
        f"http://localhost:8000/api/v1/desktop/windows/{window_id}/actions",
        json={"action": "focus"}
    )
    print(f"Focus result: {focus_response.json()}")

# Simulate keyboard input
keyboard_response = requests.post("http://localhost:8000/api/v1/desktop/input/keyboard", json={
    "action": "type_text",
    "text": "Hello, Desktop AI Agent!",
    "delay": 0.1
})
print(f"Keyboard input result: {keyboard_response.json()}")

# Simulate mouse click
mouse_response = requests.post("http://localhost:8000/api/v1/desktop/input/mouse", json={
    "action": "click",
    "position": {"x": 500, "y": 300},
    "button": "left"
})
print(f"Mouse click result: {mouse_response.json()}")
```

### AI Model Interaction

```python
# Generate AI response
ai_response = requests.post("http://localhost:8000/api/v1/ai/inference", json={
    "model_id": "codellama-7b-instruct",
    "prompt": "How do I automate taking a screenshot and saving it to a file?",
    "max_tokens": 512,
    "temperature": 0.7
})

response_data = ai_response.json()
print(f"AI Response: {response_data['text']}")
```

## 🧪 Testing

### Running Tests

```bash
# Install test dependencies
pip install -e ".[dev]"

# Run all tests
pytest

# Run with coverage
pytest --cov=desktop_ai_agent --cov-report=html

# Run specific test categories
pytest tests/test_core.py -v
pytest tests/test_desktop.py -v
pytest tests/test_api.py -v

# Run integration tests (requires display)
pytest tests/test_integration.py -v
```

### Manual Testing

```bash
# Test system health
curl http://localhost:8000/health

# Test task creation
curl -X POST http://localhost:8000/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Task", "type": "simple"}'

# Test desktop interaction
curl http://localhost:8000/api/v1/desktop/windows

# Test AI inference
curl -X POST http://localhost:8000/api/v1/ai/inference \
  -H "Content-Type: application/json" \
  -d '{"model_id": "default", "prompt": "Hello, AI!", "max_tokens": 100}'
```

## 🔍 Monitoring and Debugging

### Health Monitoring

```bash
# Check overall system health
daa status

# Get detailed health information
curl http://localhost:8000/health | jq
```

### Logs and Metrics

```bash
# View logs
tail -f logs/desktop_ai_agent.log

# Get performance metrics
curl http://localhost:8000/api/v1/metrics | jq

# View audit logs
curl http://localhost:8000/api/v1/audit/logs | jq
```

### Debugging Common Issues

#### Model Loading Issues

```bash
# Check model file exists and is readable
ls -la data/models/

# Test model loading manually
python -c "
from desktop_ai_agent.ai.model_manager import ModelManager
from desktop_ai_agent.core.config import get_settings

settings = get_settings()
manager = ModelManager(settings)
models = manager.list_models()
print('Available models:', [m.model_id for m in models])
"
```

#### Desktop Interaction Issues

```bash
# Check display environment
echo $DISPLAY  # Linux
# Ensure you're running in a desktop environment, not headless

# Test basic desktop interaction
python -c "
import pyautogui
print('Screen size:', pyautogui.size())
print('Mouse position:', pyautogui.position())
"
```

#### Permission Issues

```bash
# Check file permissions
ls -la data/
ls -la logs/

# Ensure the user has necessary permissions for desktop interaction
# On Linux, you may need to be in certain groups or have accessibility permissions
```

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**:
   ```bash
   export DAA_ENVIRONMENT=production
   export DAA_API_HOST=0.0.0.0
   export DAA_API_PORT=8000
   export DAA_DATABASE_URL=postgresql://user:pass@localhost/desktop_ai_agent
   ```

2. **Database Setup**:
   ```bash
   # For PostgreSQL
   createdb desktop_ai_agent
   
   # The system will automatically create tables on first run
   ```

3. **Service Configuration**:
   ```bash
   # Create systemd service file
   sudo tee /etc/systemd/system/desktop-ai-agent.service << EOF
   [Unit]
   Description=Desktop AI Agent
   After=network.target
   
   [Service]
   Type=simple
   User=daa-user
   WorkingDirectory=/opt/desktop-ai-agent
   Environment=DAA_ENVIRONMENT=production
   ExecStart=/opt/desktop-ai-agent/venv/bin/python -m desktop_ai_agent.main
   Restart=always
   
   [Install]
   WantedBy=multi-user.target
   EOF
   
   sudo systemctl enable desktop-ai-agent
   sudo systemctl start desktop-ai-agent
   ```

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ src/
COPY setup.py .
RUN pip install -e .

EXPOSE 8000

CMD ["python", "-m", "desktop_ai_agent.main"]
```

```bash
# Build and run
docker build -t desktop-ai-agent .
docker run -p 8000:8000 -v $(pwd)/data:/app/data desktop-ai-agent
```

## 🤝 Contributing

See the main README.md for contribution guidelines.

## 📚 Additional Resources

- [Architecture Documentation](desktop_ai_agent_architecture.md)
- [API Specifications](api_specifications.md)
- [Technical Specifications](technical_specifications.md)
- [System Prompt](desktop_ai_agent_system_prompt.md)
