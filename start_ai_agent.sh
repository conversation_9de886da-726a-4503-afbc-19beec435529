#!/bin/bash

# 🚀 DESKTOP AI AGENT - QUICK START SCRIPT
# ========================================
# 
# This script starts the complete Desktop AI Agent system with:
# - Enhanced Web Interface (TinyLlama optimized)
# - Continuous Learning System
# - Performance Monitoring
# - Task Execution Engine
# - Safety Systems
#
# Usage:
#   ./start_ai_agent.sh [options]
#
# Options:
#   --port PORT         Web interface port (default: 8000)
#   --host HOST         Web interface host (default: localhost)
#   --no-learning       Disable continuous learning
#   --no-monitoring     Disable performance monitoring
#   --debug             Enable debug logging
#   --no-browser        Don't open browser automatically
#   --help              Show this help message

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default configuration
PORT=8000
HOST="localhost"
LEARNING=true
MONITORING=true
DEBUG=false
NO_BROWSER=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --port)
            PORT="$2"
            shift 2
            ;;
        --host)
            HOST="$2"
            shift 2
            ;;
        --no-learning)
            LEARNING=false
            shift
            ;;
        --no-monitoring)
            MONITORING=false
            shift
            ;;
        --debug)
            DEBUG=true
            shift
            ;;
        --no-browser)
            NO_BROWSER=true
            shift
            ;;
        --help)
            echo "Desktop AI Agent - Quick Start Script"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --port PORT         Web interface port (default: 8000)"
            echo "  --host HOST         Web interface host (default: localhost)"
            echo "  --no-learning       Disable continuous learning"
            echo "  --no-monitoring     Disable performance monitoring"
            echo "  --debug             Enable debug logging"
            echo "  --no-browser        Don't open browser automatically"
            echo "  --help              Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC}  $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC}  $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Check if we're in the right directory
if [[ ! -f "scripts/start_all_systems.py" ]]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Print startup banner
clear
print_header "🚀 DESKTOP AI AGENT - COMPLETE SYSTEM LAUNCHER"
print_header "============================================================"
echo ""
print_info "Configuration:"
print_info "  • Web Interface: http://${HOST}:${PORT}"
print_info "  • Continuous Learning: $([ "$LEARNING" = true ] && echo "Enabled" || echo "Disabled")"
print_info "  • Performance Monitoring: $([ "$MONITORING" = true ] && echo "Enabled" || echo "Disabled")"
print_info "  • Debug Logging: $([ "$DEBUG" = true ] && echo "Enabled" || echo "Disabled")"
print_info "  • Auto-open Browser: $([ "$NO_BROWSER" = false ] && echo "Yes" || echo "No")"
echo ""

# Check Python environment
print_info "Checking Python environment..."

if [[ ! -d "venv" ]]; then
    print_warning "Virtual environment not found. Creating one..."
    python3 -m venv venv
    print_status "Virtual environment created"
fi

# Activate virtual environment
source venv/bin/activate
print_status "Virtual environment activated"

# Check if required packages are installed
print_info "Checking dependencies..."
if ! python -c "import fastapi, uvicorn, websockets" 2>/dev/null; then
    print_warning "Installing missing dependencies..."
    pip install -r requirements.txt
    print_status "Dependencies installed"
else
    print_status "All dependencies are available"
fi

# Check if models directory exists
if [[ ! -d "models" ]] && [[ ! -d "$HOME/Desktop/models" ]]; then
    print_warning "Models directory not found. Creating models directory..."
    mkdir -p models
    print_info "Place your GGUF model files in the 'models' directory"
    print_info "Recommended models:"
    print_info "  • TinyLlama-1.1B-Chat-v1.0.Q4_K_M.gguf (fast, 637MB)"
    print_info "  • Phi-2.Q4_K_M.gguf (balanced, 1.7GB)"
    print_info "  • CodeLlama-7B-Instruct.Q4_K_M.gguf (powerful, 3.9GB)"
fi

# Build command line arguments
ARGS="--port $PORT --host $HOST"

if [[ "$LEARNING" = false ]]; then
    ARGS="$ARGS --no-learning"
fi

if [[ "$MONITORING" = false ]]; then
    ARGS="$ARGS --no-monitoring"
fi

if [[ "$DEBUG" = true ]]; then
    ARGS="$ARGS --debug"
fi

if [[ "$NO_BROWSER" = true ]]; then
    ARGS="$ARGS --no-browser"
fi

# Set up signal handling for graceful shutdown
cleanup() {
    print_info "Shutting down Desktop AI Agent..."
    # Kill any background processes
    jobs -p | xargs -r kill
    print_status "Desktop AI Agent stopped"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Start the system
print_header ""
print_header "🚀 STARTING DESKTOP AI AGENT..."
print_header "================================"
echo ""

# Export PYTHONPATH
export PYTHONPATH="${PWD}/src:${PYTHONPATH}"

# Start the complete system
print_info "Launching complete system with enhanced features..."
echo ""

# Run the Python launcher
python scripts/start_all_systems.py $ARGS

# If we get here, the system has stopped
print_info "Desktop AI Agent has stopped"
