{"environment": "development", "app_name": "Desktop AI Agent", "app_version": "0.1.0", "data_dir": "data", "api": {"host": "127.0.0.1", "port": 8000, "cors_origins": ["http://localhost:3000", "http://127.0.0.1:3000"], "debug": true, "reload": false}, "database": {"url": "sqlite:///data/desktop_ai_agent.db", "echo": false, "pool_size": 10, "max_overflow": 20}, "ai_model": {"model_path": "data/models/default.gguf", "model_name": "CodeLlama-7B-Instruct", "context_length": 4096, "temperature": 0.7, "top_p": 0.9, "top_k": 40, "n_threads": 4, "n_gpu_layers": 0, "rope_freq_base": 10000.0, "rope_freq_scale": 1.0}, "desktop": {"safe_mode": true, "screenshot_format": "PNG", "screenshot_quality": 95, "input_delay": 0.1, "max_screenshot_size": 1920}, "logging": {"level": "INFO", "structured": true, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "logs/desktop_ai_agent.log", "max_file_size": 10485760, "backup_count": 5}, "security": {"safe_mode": true, "require_explicit_permission": true, "auto_deny_high_risk": true, "audit_all_operations": true, "audit_retention_days": 90}, "monitoring": {"metrics_enabled": true, "health_check_interval": 30, "performance_tracking": true}}