#!/usr/bin/env python3
"""
Quick test for the enhanced task execution
"""

import asyncio
import websockets
import json

async def quick_test():
    """Quick test of task execution."""
    print('🧪 Quick Task Execution Test')
    print('=' * 40)
    
    try:
        uri = 'ws://localhost:8000/ws/test_user'
        
        async with websockets.connect(uri) as websocket:
            print('✅ Connected!')
            
            # Test simple screenshot task
            message = {
                'type': 'user_message',
                'content': 'take a screenshot',
                'user_id': 'test_user'
            }
            
            await websocket.send(json.dumps(message))
            print('📤 Sent: "take a screenshot"')
            
            # Wait for responses
            for i in range(5):  # Max 5 responses
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(response)
                    print(f'📥 {data.get("type", "unknown")}: {data.get("content", "")[:80]}...')
                    
                    if data.get('type') in ['task_completed', 'task_failed']:
                        break
                        
                except asyncio.TimeoutError:
                    print('⏰ Timeout - test complete')
                    break
            
            print('✅ Test completed!')
            
    except Exception as e:
        print(f'❌ Test failed: {e}')

if __name__ == "__main__":
    asyncio.run(quick_test())
