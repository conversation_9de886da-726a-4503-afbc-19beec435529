#!/usr/bin/env python3
"""
Web Interface for Desktop AI Agent

This script provides a standalone web interface for the conversational AI agent.
"""

import sys
import argparse
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.conversation.engine import ConversationEngine
from desktop_ai_agent.learning.engine import LearningEngine
from desktop_ai_agent.web.chat_interface import WebChatInterface

import uvicorn


async def initialize_agent():
    """Initialize the AI agent and components."""
    print("🚀 DESKTOP AI AGENT - WEB INTERFACE")
    print("=" * 50)
    print("🌐 Initializing web-based conversational interface...")
    
    # Initialize core agent
    settings = get_settings('config.json')
    agent = DesktopAIAgent(settings)
    await agent.initialize()
    
    # Initialize conversation engine
    conversation_engine = ConversationEngine(settings)
    
    # Initialize learning engine
    learning_engine = LearningEngine(settings)
    
    # Set up dependencies
    conversation_engine.set_dependencies(
        agent.ai_engine,
        agent.orchestrator,
        learning_engine
    )
    
    learning_engine.set_dependencies(
        agent.ai_engine,
        agent.storage_engine,
        conversation_engine
    )
    
    # Initialize web interface with task execution capabilities
    web_interface = WebChatInterface(
        conversation_engine,
        learning_engine,
        orchestrator=agent.orchestrator,
        safety_engine=agent.safety_engine,
        desktop_engine=agent.desktop_engine
    )
    
    print("✅ Web interface initialized successfully!")
    print(f"🤖 AI Model: CodeLlama-7B (Production Ready)")
    print(f"🔒 Safety System: Active")
    print(f"📚 Learning System: Active")
    
    return agent, web_interface


def main():
    """Main entry point for web interface."""
    parser = argparse.ArgumentParser(description="Desktop AI Agent Web Interface")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    
    args = parser.parse_args()
    
    print(f"🌐 Starting Desktop AI Agent Web Interface")
    print(f"📍 Server will be available at: http://{args.host}:{args.port}")
    print(f"🔄 Auto-reload: {'Enabled' if args.reload else 'Disabled'}")
    print("=" * 50)
    
    # Create a simple FastAPI app that initializes the agent
    from fastapi import FastAPI
    
    app = FastAPI(title="Desktop AI Agent Web Interface")
    
    # Global variables to store initialized components
    agent = None
    web_interface = None
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize the agent on startup."""
        global agent, web_interface
        try:
            agent, web_interface = await initialize_agent()
            
            # Mount the web interface routes
            app.mount("/", web_interface.app)
            
            print("🎉 Web interface ready!")
            print(f"🌐 Open your browser and go to: http://{args.host}:{args.port}")
            
        except Exception as e:
            print(f"❌ Failed to initialize: {e}")
            import traceback
            traceback.print_exc()
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Clean up on shutdown."""
        global agent
        if agent:
            await agent.stop()
            print("🛑 Desktop AI Agent stopped")
    
    # Run the server
    try:
        uvicorn.run(
            app,
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Web interface stopped by user")
    except Exception as e:
        print(f"❌ Error running web interface: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
