#!/usr/bin/env python3
"""
Model setup script for Desktop AI Agent.

This script helps discover existing AI models on the system and set up
a suitable model for the Desktop AI Agent.
"""

import argparse
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.utils.model_downloader import ModelDownloader, setup_model_for_agent


def main():
    parser = argparse.ArgumentParser(description="Set up AI models for Desktop AI Agent")
    parser.add_argument("--discover", action="store_true", help="Discover existing models")
    parser.add_argument("--download", type=str, help="Download a recommended model")
    parser.add_argument("--auto-setup", action="store_true", help="Automatically set up the best available model")
    parser.add_argument("--list-recommended", action="store_true", help="List recommended models")
    parser.add_argument("--config-file", type=str, help="Configuration file to update")
    
    args = parser.parse_args()
    
    # Load settings
    settings = get_settings()
    downloader = ModelDownloader(settings)
    
    if args.discover:
        print("🔍 Discovering existing AI models...")
        models = downloader.discover_existing_models()
        
        if not models:
            print("❌ No AI models found on this system.")
            print("\nTo get started, you can:")
            print("1. Download a recommended model: python scripts/setup_models.py --download codellama-7b-instruct")
            print("2. Use auto-setup: python scripts/setup_models.py --auto-setup")
            return
        
        print(f"✅ Found {len(models)} AI models:")
        print()
        
        suitable_models = []
        for model in models:
            status = "✅ SUITABLE" if model["suitable"] else "⚠️  Not ideal"
            size_gb = model["size_mb"] / 1024
            
            print(f"{status}")
            print(f"  📁 Path: {model['path']}")
            print(f"  📊 Size: {size_gb:.1f} GB")
            print(f"  🏷️  Type: {model['model_type']}")
            if model["parameter_count"]:
                print(f"  🧠 Parameters: {model['parameter_count']}")
            if model["quantization"]:
                print(f"  ⚡ Quantization: {model['quantization']}")
            print()
            
            if model["suitable"]:
                suitable_models.append(model)
        
        if suitable_models:
            print(f"🎯 Found {len(suitable_models)} suitable models for Desktop AI Agent.")
            best_model = suitable_models[0]
            print(f"💡 Recommended: {best_model['filename']}")
            print(f"   To use this model, update your configuration:")
            print(f"   ai_model.model_path = \"{best_model['path']}\"")
        else:
            print("⚠️  No suitable models found. Consider downloading a recommended model.")
    
    elif args.list_recommended:
        print("📋 Recommended AI models for Desktop AI Agent:")
        print()
        
        recommended = downloader.list_recommended_models()
        for key, model in recommended.items():
            print(f"🤖 {model['name']}")
            print(f"   Key: {key}")
            print(f"   Description: {model['description']}")
            print(f"   Size: {model['size_gb']} GB")
            print(f"   Context: {model['context_length']} tokens")
            print(f"   Download: python scripts/setup_models.py --download {key}")
            print()
    
    elif args.download:
        model_key = args.download
        print(f"📥 Downloading model: {model_key}")
        
        recommended = downloader.list_recommended_models()
        if model_key not in recommended:
            print(f"❌ Unknown model key: {model_key}")
            print("Available models:")
            for key in recommended.keys():
                print(f"  - {key}")
            return
        
        model_info = recommended[model_key]
        print(f"Model: {model_info['name']}")
        print(f"Size: {model_info['size_gb']} GB")
        print(f"Description: {model_info['description']}")
        print()
        
        def progress_callback(message):
            print(f"📊 {message}")
        
        success = downloader.download_model(model_key, progress_callback)
        
        if success:
            print("✅ Model downloaded successfully!")
            model_path = downloader.models_dir / model_info["filename"]
            print(f"📁 Model path: {model_path}")
            print()
            print("💡 To use this model, update your configuration:")
            print(f"   ai_model.model_path = \"{model_path}\"")
            
            # Update configuration if requested
            if args.config_file:
                update_config_file(args.config_file, str(model_path))
        else:
            print("❌ Model download failed!")
    
    elif args.auto_setup:
        print("🚀 Auto-setting up AI model for Desktop AI Agent...")
        
        found_model, result = setup_model_for_agent(settings)
        
        if found_model:
            print("✅ Found suitable existing model!")
            print(f"📁 Model path: {result}")
            print("🎯 Desktop AI Agent is ready to use!")
        elif result:
            print("💡 Recommended model for download:")
            recommended = downloader.list_recommended_models()
            model_info = recommended[result]
            print(f"   {model_info['name']} ({model_info['size_gb']} GB)")
            print(f"   {model_info['description']}")
            print()
            print(f"To download: python scripts/setup_models.py --download {result}")
        else:
            print("❌ No suitable models found and no recommendations available.")
            print("Please check your system requirements and try again.")
    
    else:
        parser.print_help()


def update_config_file(config_file: str, model_path: str):
    """Update configuration file with model path."""
    import json
    
    config_path = Path(config_file)
    
    try:
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = json.load(f)
        else:
            config = {}
        
        # Update model path
        if "ai_model" not in config:
            config["ai_model"] = {}
        
        config["ai_model"]["model_path"] = model_path
        
        # Write back to file
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Updated configuration file: {config_file}")
        
    except Exception as e:
        print(f"❌ Failed to update configuration file: {e}")


if __name__ == "__main__":
    main()
