#!/usr/bin/env python3
"""
Test script for AI model integration with Desktop AI Agent.

This script tests the model loading and basic inference capabilities
to ensure the AI model is properly integrated.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.ai.model_manager import ModelManager
from desktop_ai_agent.ai.engine import ModelInferenceEngine
from desktop_ai_agent.ai.models import InferenceRequest


async def test_model_discovery():
    """Test model discovery and registration."""
    print("🔍 Testing model discovery...")
    
    settings = get_settings()
    model_manager = ModelManager(settings)
    
    try:
        await model_manager.initialize()
        
        models = model_manager.list_models()
        print(f"✅ Found {len(models)} models:")
        
        for model in models:
            print(f"  📁 {model.name}")
            print(f"     ID: {model.model_id}")
            print(f"     Type: {model.type}")
            print(f"     Size: {model.file_size_mb:.1f} MB")
            print(f"     Status: {model.status}")
            print(f"     Path: {model.file_path}")
            print()
        
        return models
        
    except Exception as e:
        print(f"❌ Model discovery failed: {e}")
        return []
    finally:
        await model_manager.cleanup()


async def test_model_loading():
    """Test loading the default model."""
    print("🔄 Testing model loading...")
    
    settings = get_settings()
    model_manager = ModelManager(settings)
    
    try:
        await model_manager.initialize()
        
        # Get default model
        default_model_id = model_manager.get_default_model_id()
        if not default_model_id:
            print("❌ No default model available")
            return False
        
        print(f"📦 Loading model: {default_model_id}")
        
        # Load the model
        success = await model_manager.load_model(default_model_id)
        
        if success:
            print("✅ Model loaded successfully!")
            
            # Get model info
            model_info = model_manager.get_model_info(default_model_id)
            if model_info:
                print(f"   Load time: {model_info.load_time_ms:.1f} ms")
                print(f"   Memory usage: {model_info.memory_usage_mb or 'Unknown'} MB")
            
            return True
        else:
            print("❌ Model loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Model loading error: {e}")
        return False
    finally:
        await model_manager.cleanup()


async def test_model_inference():
    """Test basic model inference."""
    print("🧠 Testing model inference...")
    
    settings = get_settings()
    ai_engine = ModelInferenceEngine(settings)
    
    try:
        await ai_engine.start()
        
        # Get default model
        default_model_id = ai_engine.get_default_model_id()
        if not default_model_id:
            print("❌ No default model available for inference")
            return False
        
        print(f"🤖 Using model: {default_model_id}")
        
        # Create inference request
        request = InferenceRequest(
            model_id=default_model_id,
            prompt="Hello! Can you help me automate a simple desktop task?",
            max_tokens=100,
            temperature=0.7
        )
        
        print(f"💭 Prompt: {request.prompt}")
        print("🔄 Generating response...")
        
        # Generate response
        response = await ai_engine.generate_response(request)
        
        if response.success:
            print("✅ Inference successful!")
            print(f"📝 Response: {response.text}")
            print(f"⚡ Inference time: {response.inference_time_ms:.1f} ms")
            print(f"🔢 Tokens: {response.total_tokens} ({response.tokens_per_second:.1f} tokens/sec)")
            return True
        else:
            print(f"❌ Inference failed: {response.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Inference error: {e}")
        return False
    finally:
        await ai_engine.stop()


async def test_task_planning_prompt():
    """Test task planning specific prompt."""
    print("📋 Testing task planning capabilities...")
    
    settings = get_settings()
    ai_engine = ModelInferenceEngine(settings)
    
    try:
        await ai_engine.start()
        
        default_model_id = ai_engine.get_default_model_id()
        if not default_model_id:
            print("❌ No default model available")
            return False
        
        # Create a task planning prompt
        task_prompt = """You are a Desktop AI Agent. Break down this task into executable steps:

Task: "Take a screenshot of the current desktop and save it to a file named 'desktop_capture.png'"

Please provide a step-by-step plan with specific actions."""
        
        request = InferenceRequest(
            model_id=default_model_id,
            prompt=task_prompt,
            max_tokens=200,
            temperature=0.3  # Lower temperature for more focused responses
        )
        
        print("🎯 Testing task planning prompt...")
        response = await ai_engine.generate_response(request)
        
        if response.success:
            print("✅ Task planning successful!")
            print("📋 Generated plan:")
            print(response.text)
            print(f"⚡ Response time: {response.inference_time_ms:.1f} ms")
            return True
        else:
            print(f"❌ Task planning failed: {response.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Task planning error: {e}")
        return False
    finally:
        await ai_engine.stop()


async def run_model_integration_tests():
    """Run comprehensive model integration tests."""
    print("🚀 Desktop AI Agent - Model Integration Test")
    print("=" * 50)
    
    tests = [
        ("Model Discovery", test_model_discovery),
        ("Model Loading", test_model_loading),
        ("Basic Inference", test_model_inference),
        ("Task Planning", test_task_planning_prompt),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Model Integration Test Results:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All model integration tests passed!")
        print("🚀 Desktop AI Agent is ready for AI-powered automation!")
        return True
    elif passed >= total * 0.75:  # 75% pass rate
        print("⚠️  Most tests passed. Model integration is functional.")
        return True
    else:
        print("❌ Model integration tests failed.")
        print("💡 Check model files and configuration.")
        return False


def main():
    """Main entry point."""
    try:
        result = asyncio.run(run_model_integration_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
