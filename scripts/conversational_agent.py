#!/usr/bin/env python3
"""
Conversational Desktop AI Agent Interface

This script provides a natural language conversational interface
with self-improvement capabilities for the Desktop AI Agent.
"""

import asyncio
import sys
import json
import time
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.conversation.engine import ConversationEngine
from desktop_ai_agent.conversation.models import (
    MessageType, ConversationLanguage, TaskFeedback
)
from desktop_ai_agent.learning.engine import LearningEngine
from desktop_ai_agent.learning.models import (
    LearningType, PerformanceMetric, ImprovementStatus
)


class ConversationalAgent:
    """Advanced conversational interface with self-improvement."""
    
    def __init__(self):
        self.agent = None
        self.conversation_engine = None
        self.learning_engine = None
        self.current_session = None
        self.running = False
        
        # Voice capabilities (placeholder for future implementation)
        self.voice_enabled = False
        
    async def initialize(self):
        """Initialize the conversational agent."""
        print("🤖 CONVERSATIONAL DESKTOP AI AGENT")
        print("=" * 60)
        print("🧠 Advanced AI with Self-Improvement Capabilities")
        print("🗣️  Natural Language Interface (Romanian & English)")
        print("📈 Continuous Learning & Adaptation")
        print("=" * 60)
        
        # Initialize core agent
        settings = get_settings('config.json')
        self.agent = DesktopAIAgent(settings)
        await self.agent.initialize()
        
        # Initialize conversation engine
        self.conversation_engine = ConversationEngine(settings)
        self.conversation_engine.set_dependencies(
            self.agent.ai_engine,
            self.agent.orchestrator,
            None  # Will set learning engine after creation
        )
        
        # Initialize learning engine
        self.learning_engine = LearningEngine(settings)
        self.learning_engine.set_dependencies(
            self.agent.ai_engine,
            self.agent.storage_engine,
            self.conversation_engine
        )
        
        # Set cross-dependencies
        self.conversation_engine.learning_system = self.learning_engine
        
        print("✅ Conversational AI Agent initialized successfully!")
        print(f"🤖 AI Model: CodeLlama-7B (Real AI, not mock)")
        print(f"🔒 Safety System: Active")
        print(f"📚 Learning System: Active")
        print(f"🗣️  Voice Support: {'Enabled' if self.voice_enabled else 'Planned for future'}")
        
    async def start_conversation(self, user_id: str = "user"):
        """Start a new conversation session."""
        print(f"\n🎯 Starting conversation with user: {user_id}")
        
        # Start conversation session
        self.current_session = await self.conversation_engine.start_conversation(
            user_id=user_id,
            platform="cli",
            voice_enabled=self.voice_enabled
        )
        
        print(f"📝 Session ID: {self.current_session.session_id[:12]}...")
        
        # Display welcome message
        if self.current_session.messages:
            welcome_msg = self.current_session.messages[0]
            print(f"\n🤖 Agent: {welcome_msg.content}")
        
        self.running = True
        
        # Start learning session in background
        asyncio.create_task(self._background_learning())
        
    async def run_conversation_loop(self):
        """Run the main conversation loop."""
        print("\n" + "=" * 60)
        print("💬 CONVERSATION STARTED")
        print("=" * 60)
        print("💡 Tips:")
        print("  • Speak naturally in Romanian or English")
        print("  • Ask me to automate desktop tasks")
        print("  • Give me feedback to help me improve")
        print("  • Type 'help' for available commands")
        print("  • Type 'quit' to exit")
        print("=" * 60)
        
        while self.running:
            try:
                # Get user input
                user_input = input("\n👤 You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if await self._handle_special_commands(user_input):
                    continue
                
                # Process message with timing
                start_time = time.time()
                
                print("🤖 Agent: (thinking...)")
                response = await self.conversation_engine.process_user_message(
                    self.current_session.session_id,
                    user_input,
                    MessageType.USER_TEXT
                )
                
                response_time = (time.time() - start_time) * 1000
                
                # Display response
                print(f"\r🤖 Agent: {response.content}")
                
                # Show response metadata
                if response.metadata:
                    inference_time = response.metadata.get('inference_time_ms', 0)
                    tokens = response.metadata.get('tokens', 0)
                    print(f"   ⚡ Response: {response_time:.0f}ms | AI: {inference_time:.0f}ms | Tokens: {tokens}")
                
                # Record performance metric
                await self._record_performance_metric(
                    "response_time", response_time, "ms", "conversation"
                )
                
                # Check for task execution
                if response.task_id:
                    await self._monitor_task_execution(response.task_id)
                
                # Periodically ask for feedback
                if len(self.current_session.messages) % 10 == 0:
                    await self._request_feedback()
                
            except KeyboardInterrupt:
                print("\n👋 Conversation interrupted by user")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
                print("💡 Please try again or type 'help' for assistance")
        
        await self._end_conversation()
    
    async def _handle_special_commands(self, user_input: str) -> bool:
        """Handle special commands like help, quit, etc."""
        command = user_input.lower()
        
        if command in ['quit', 'exit', 'bye']:
            print("👋 Goodbye! Thanks for chatting with me.")
            self.running = False
            return True
        
        elif command == 'help':
            await self._show_help()
            return True
        
        elif command == 'status':
            await self._show_status()
            return True
        
        elif command == 'learning':
            await self._show_learning_status()
            return True
        
        elif command == 'proposals':
            await self._show_improvement_proposals()
            return True
        
        elif command.startswith('approve '):
            proposal_id = command.replace('approve ', '').strip()
            await self._approve_proposal(proposal_id)
            return True
        
        elif command.startswith('reject '):
            parts = command.split(' ', 2)
            if len(parts) >= 3:
                proposal_id = parts[1]
                reason = parts[2]
                await self._reject_proposal(proposal_id, reason)
            return True
        
        elif command == 'voice':
            await self._toggle_voice()
            return True
        
        return False
    
    async def _show_help(self):
        """Show help information."""
        print("\n🆘 HELP - Available Commands:")
        print("-" * 40)
        print("💬 Natural conversation:")
        print("  • Just type naturally in Romanian or English")
        print("  • Ask me to automate tasks: 'organize my files'")
        print("  • Request information: 'what can you do?'")
        print("")
        print("🔧 Special commands:")
        print("  • help     - Show this help")
        print("  • status   - Show system status")
        print("  • learning - Show learning progress")
        print("  • proposals - Show improvement proposals")
        print("  • approve <id> - Approve an improvement")
        print("  • reject <id> <reason> - Reject an improvement")
        print("  • voice    - Toggle voice capabilities")
        print("  • quit     - Exit conversation")
        print("")
        print("💡 Examples:")
        print("  • 'Take a screenshot of my desktop'")
        print("  • 'Organizează fișierele de pe desktop'")
        print("  • 'What did you learn from our last conversation?'")
    
    async def _show_status(self):
        """Show system status."""
        print("\n📊 SYSTEM STATUS:")
        print("-" * 30)
        print(f"🤖 AI Model: CodeLlama-7B ({'Loaded' if self.agent.ai_engine else 'Not loaded'})")
        print(f"💬 Session: {self.current_session.session_id[:12]}... ({len(self.current_session.messages)} messages)")
        print(f"🧠 Learning: {'Active' if self.learning_engine.learning_enabled else 'Disabled'}")
        print(f"🔒 Safety: Active")
        print(f"📈 Performance Metrics: {len(self.learning_engine.performance_metrics)} recorded")
        print(f"💡 Pending Proposals: {len(await self.learning_engine.get_pending_proposals())}")
    
    async def _show_learning_status(self):
        """Show learning system status."""
        print("\n🧠 LEARNING SYSTEM STATUS:")
        print("-" * 35)
        
        if self.learning_engine.active_learning_session:
            session = self.learning_engine.active_learning_session
            print(f"📚 Active Learning Session: {session.session_id[:12]}...")
            print(f"🔍 Insights Discovered: {len(session.insights_discovered)}")
            print(f"💡 Proposals Generated: {len(session.proposals_generated)}")
        else:
            print("📚 No active learning session")
        
        print(f"📊 Total Performance Metrics: {len(self.learning_engine.performance_metrics)}")
        print(f"✅ Implemented Improvements: {len(self.learning_engine.implemented_improvements)}")
        
        # Show recent insights
        if hasattr(self.learning_engine, 'recent_insights'):
            print("\n🔍 Recent Insights:")
            for insight in self.learning_engine.recent_insights[-3:]:
                print(f"  • {insight.title} (Confidence: {insight.confidence.value})")
    
    async def _show_improvement_proposals(self):
        """Show pending improvement proposals."""
        proposals = await self.learning_engine.get_pending_proposals()
        
        print(f"\n💡 IMPROVEMENT PROPOSALS ({len(proposals)} pending):")
        print("-" * 45)
        
        if not proposals:
            print("✨ No pending proposals. The system is learning and will suggest improvements soon!")
            return
        
        for i, proposal in enumerate(proposals, 1):
            print(f"\n{i}. {proposal.title}")
            print(f"   ID: {proposal.proposal_id[:12]}...")
            print(f"   Description: {proposal.description[:100]}...")
            print(f"   Impact: {proposal.rationale[:80]}...")
            print(f"   Timeline: {proposal.estimated_timeline} days")
            print(f"   Status: {proposal.status.value}")
        
        if proposals:
            print(f"\n💡 To approve: 'approve {proposals[0].proposal_id[:12]}'")
            print(f"💡 To reject: 'reject {proposals[0].proposal_id[:12]} reason here'")
    
    async def _approve_proposal(self, proposal_id: str):
        """Approve an improvement proposal."""
        # Find full proposal ID if partial was provided
        full_id = None
        for proposal in await self.learning_engine.get_pending_proposals():
            if proposal.proposal_id.startswith(proposal_id):
                full_id = proposal.proposal_id
                break
        
        if full_id:
            success = await self.learning_engine.approve_proposal(full_id, "user")
            if success:
                print(f"✅ Proposal approved! I'll implement this improvement.")
                print("📈 This will help me become more capable and efficient.")
            else:
                print("❌ Failed to approve proposal. Please try again.")
        else:
            print("❌ Proposal not found. Use 'proposals' to see available proposals.")
    
    async def _reject_proposal(self, proposal_id: str, reason: str):
        """Reject an improvement proposal."""
        # Find full proposal ID if partial was provided
        full_id = None
        for proposal in await self.learning_engine.get_pending_proposals():
            if proposal.proposal_id.startswith(proposal_id):
                full_id = proposal.proposal_id
                break
        
        if full_id:
            success = await self.learning_engine.reject_proposal(full_id, reason)
            if success:
                print(f"❌ Proposal rejected. I'll learn from your feedback: '{reason}'")
                print("📚 This helps me understand your preferences better.")
            else:
                print("❌ Failed to reject proposal. Please try again.")
        else:
            print("❌ Proposal not found. Use 'proposals' to see available proposals.")
    
    async def _toggle_voice(self):
        """Toggle voice capabilities."""
        print("🎤 Voice capabilities are planned for future implementation!")
        print("💡 This will include:")
        print("  • Speech-to-text for voice input")
        print("  • Text-to-speech for voice responses")
        print("  • Multi-language voice support")
        print("  • Voice command recognition")
    
    async def _record_performance_metric(
        self, 
        metric_name: str, 
        value: float, 
        unit: str, 
        task_type: str
    ):
        """Record a performance metric."""
        metric = PerformanceMetric(
            metric_name=metric_name,
            value=value,
            unit=unit,
            task_type=task_type,
            user_id=self.current_session.user_id
        )
        
        await self.learning_engine.record_performance_metric(metric)
    
    async def _background_learning(self):
        """Run background learning process."""
        while self.running:
            try:
                # Run learning every hour
                await asyncio.sleep(3600)  # 1 hour
                
                if len(self.learning_engine.performance_metrics) >= 10:
                    print("\n🧠 Running background learning analysis...")
                    await self.learning_engine.start_learning_session()
                    print("✅ Learning analysis completed!")
                    
                    # Check for new proposals
                    proposals = await self.learning_engine.get_pending_proposals()
                    if proposals:
                        print(f"💡 {len(proposals)} new improvement proposals available!")
                        print("💡 Type 'proposals' to review them.")
                
            except Exception as e:
                print(f"⚠️  Background learning error: {e}")
    
    async def _request_feedback(self):
        """Request user feedback periodically."""
        print("\n📝 Quick feedback: How am I doing? (1-5 stars, or just press Enter to skip)")
        try:
            feedback = input("⭐ Rating: ").strip()
            if feedback and feedback.isdigit():
                rating = int(feedback)
                if 1 <= rating <= 5:
                    await self._record_performance_metric(
                        "user_satisfaction", rating, "stars", "conversation"
                    )
                    print(f"🙏 Thank you for the {rating}-star rating!")
        except:
            pass  # Skip if user doesn't provide feedback
    
    async def _end_conversation(self):
        """End the conversation session."""
        if self.current_session:
            self.current_session.end_time = time.time()
            print(f"\n📊 Conversation Summary:")
            print(f"   Messages: {len(self.current_session.messages)}")
            print(f"   Duration: {(time.time() - self.current_session.start_time.timestamp())/60:.1f} minutes")
            print(f"   Tasks: {self.current_session.tasks_completed}")
        
        if self.agent:
            await self.agent.stop()
        
        print("👋 Thank you for using the Conversational Desktop AI Agent!")


async def main():
    """Main entry point."""
    agent = ConversationalAgent()
    
    try:
        await agent.initialize()
        await agent.start_conversation()
        await agent.run_conversation_loop()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 Starting Conversational Desktop AI Agent...")
    asyncio.run(main())
