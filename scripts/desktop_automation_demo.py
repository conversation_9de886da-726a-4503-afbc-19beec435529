#!/usr/bin/env python3
"""
Desktop Automation Demo - Real Visible Actions

This script demonstrates actual desktop automation with visible movements
that you can see happening on your screen in real-time.
"""

import asyncio
import sys
import time
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.desktop.models import DesktopAction
from desktop_ai_agent.ai.models import InferenceRequest


class DesktopAutomationDemo:
    """Demonstrates real desktop automation with visible actions."""
    
    def __init__(self):
        self.agent = None
        
    async def initialize(self):
        """Initialize the agent."""
        print("🚀 DESKTOP AUTOMATION DEMO - REAL VISIBLE ACTIONS")
        print("=" * 60)
        print("Initializing Desktop AI Agent...")
        
        settings = get_settings('config.json')
        self.agent = DesktopAIAgent(settings)
        
        await self.agent.initialize()
        print("✅ Agent ready for desktop automation!")
        
    async def demo_window_management(self):
        """Demonstrate window management with visible actions."""
        print("\n🖥️  DEMO 1: WINDOW MANAGEMENT")
        print("-" * 40)
        
        # List current windows
        print("📱 Listing current windows...")
        windows = await self.agent.desktop_engine.list_windows()
        
        print(f"✅ Found {len(windows)} open windows:")
        for i, window in enumerate(windows[:5], 1):
            print(f"  {i}. {window.title[:40]}... ({window.application})")
            
        if windows:
            # Focus on first window
            print(f"\n🎯 Focusing on window: {windows[0].title[:30]}...")
            
            action = DesktopAction(
                action_id="focus_001",
                action_type="window_focus",
                parameters={"window_id": windows[0].window_id}
            )
            
            result = await self.agent.desktop_engine.execute_desktop_action(action)
            
            if result.success:
                print("✅ Window focused successfully!")
                print("👀 You should see the window come to the front!")
            else:
                print(f"❌ Failed to focus window: {result.error_message}")
                
        await asyncio.sleep(2)  # Pause to see the action
        
    async def demo_screenshot_capture(self):
        """Demonstrate screenshot capture."""
        print("\n📸 DEMO 2: SCREENSHOT CAPTURE")
        print("-" * 40)
        
        print("📸 Taking screenshot of current desktop...")
        
        action = DesktopAction(
            action_id="screenshot_001",
            action_type="screenshot",
            parameters={
                "format": "PNG",
                "quality": 95,
                "filename": "demo_screenshot.png"
            }
        )
        
        result = await self.agent.desktop_engine.execute_desktop_action(action)
        
        if result.success:
            print("✅ Screenshot captured!")
            print(f"📁 Saved as: {result.result.get('file_path', 'demo_screenshot.png')}")
            print("👀 Check your file system - you should see the screenshot file!")
        else:
            print(f"❌ Screenshot failed: {result.error_message}")
            
        await asyncio.sleep(1)
        
    async def demo_mouse_movement(self):
        """Demonstrate mouse movement (if safe mode allows)."""
        print("\n🖱️  DEMO 3: MOUSE INTERACTION")
        print("-" * 40)
        
        # Get screen size first
        print("📐 Getting screen dimensions...")
        
        action = DesktopAction(
            action_id="screen_size_001",
            action_type="get_screen_size",
            parameters={}
        )
        
        result = await self.agent.desktop_engine.execute_desktop_action(action)
        
        if result.success:
            screen_size = result.result
            print(f"✅ Screen size: {screen_size.get('width', 1920)}x{screen_size.get('height', 1080)}")
            
            # Move mouse to center of screen
            center_x = screen_size.get('width', 1920) // 2
            center_y = screen_size.get('height', 1080) // 2
            
            print(f"🖱️  Moving mouse to center: ({center_x}, {center_y})")
            
            action = DesktopAction(
                action_id="mouse_move_001",
                action_type="mouse_move",
                parameters={"x": center_x, "y": center_y}
            )
            
            result = await self.agent.desktop_engine.execute_desktop_action(action)
            
            if result.success:
                print("✅ Mouse moved to center!")
                print("👀 You should see your mouse cursor move to the center of the screen!")
            else:
                print(f"❌ Mouse movement failed: {result.error_message}")
                print("💡 This might be disabled in safe mode")
        else:
            print(f"❌ Could not get screen size: {result.error_message}")
            
        await asyncio.sleep(2)
        
    async def demo_ai_task_planning(self):
        """Demonstrate AI-powered task planning."""
        print("\n🤖 DEMO 4: AI TASK PLANNING")
        print("-" * 40)
        
        task_request = "Help me organize my desktop by creating folders for different file types"
        
        print(f"💭 AI planning task: '{task_request}'")
        
        request = InferenceRequest(
            model_id='local_codellama-7b-instruct.Q4_K_M',
            prompt=f"""You are a Desktop AI Agent. Create a detailed step-by-step plan for this task: "{task_request}"

Provide specific, actionable steps that can be executed on a desktop computer. Be practical and safe.""",
            max_tokens=200,
            temperature=0.3
        )
        
        print("🤖 AI generating automation plan...")
        response = await self.agent.ai_engine.generate_response(request)
        
        if response.success:
            print("✅ AI Task Plan Generated:")
            print("-" * 30)
            print(response.text)
            print("-" * 30)
            print(f"⚡ Generated in {response.inference_time_ms:.1f}ms")
        else:
            print(f"❌ AI planning failed: {response.error_message}")
            
        await asyncio.sleep(1)
        
    async def demo_safety_system(self):
        """Demonstrate the safety system in action."""
        print("\n🔒 DEMO 5: SAFETY SYSTEM")
        print("-" * 40)
        
        # Test safe operation
        print("✅ Testing SAFE operation: read file")
        safe_permission = await self.agent.safety_engine.request_permission(
            operation='read_file',
            resource='/home/<USER>/documents/readme.txt',
            justification='Reading user documentation for demo'
        )
        
        print(f"   Risk Level: {safe_permission.risk_level}")
        print(f"   Status: {safe_permission.status}")
        
        # Test dangerous operation
        print("\n🚨 Testing DANGEROUS operation: system command")
        dangerous_permission = await self.agent.safety_engine.request_permission(
            operation='system_command_execution',
            resource='sudo rm -rf /',
            justification='Testing dangerous command detection'
        )
        
        print(f"   Risk Level: {dangerous_permission.risk_level}")
        print(f"   Status: {dangerous_permission.status}")
        print("✅ System correctly blocked the dangerous command!")
        
    async def demo_file_operations(self):
        """Demonstrate file operations."""
        print("\n📁 DEMO 6: FILE OPERATIONS")
        print("-" * 40)
        
        print("📁 Creating demo folder structure...")
        
        # This would be a simulated file operation for demo
        action = DesktopAction(
            action_id="file_op_001",
            action_type="file_operation",
            parameters={
                "operation": "create_folder",
                "path": "/tmp/desktop_ai_demo",
                "folder_name": "AI_Organized_Files"
            }
        )
        
        result = await self.agent.desktop_engine.execute_desktop_action(action)
        
        if result.success:
            print("✅ Demo folder created!")
            print("📁 Check /tmp/desktop_ai_demo/ for the new folder")
        else:
            print(f"❌ Folder creation failed: {result.error_message}")
            print("💡 This might be a simulated operation in demo mode")
            
    async def run_full_demo(self):
        """Run the complete demonstration."""
        await self.initialize()
        
        print("\n🎬 STARTING DESKTOP AUTOMATION DEMONSTRATION")
        print("👀 Watch your screen - you'll see real automation happening!")
        print("\nPress Ctrl+C at any time to stop the demo")
        
        try:
            demos = [
                ("Window Management", self.demo_window_management),
                ("Screenshot Capture", self.demo_screenshot_capture),
                ("Mouse Interaction", self.demo_mouse_movement),
                ("AI Task Planning", self.demo_ai_task_planning),
                ("Safety System", self.demo_safety_system),
                ("File Operations", self.demo_file_operations)
            ]
            
            for i, (name, demo_func) in enumerate(demos, 1):
                print(f"\n{'='*60}")
                print(f"🎯 DEMO {i}/6: {name.upper()}")
                print(f"{'='*60}")
                
                await demo_func()
                
                if i < len(demos):
                    print(f"\n⏳ Next demo in 3 seconds...")
                    await asyncio.sleep(3)
                    
            print(f"\n{'='*60}")
            print("🎉 DEMONSTRATION COMPLETE!")
            print("{'='*60}")
            print("✅ You've seen the Desktop AI Agent perform:")
            print("   🖥️  Real window management")
            print("   📸 Screenshot capture")
            print("   🖱️  Mouse control")
            print("   🤖 AI-powered task planning")
            print("   🔒 Safety system protection")
            print("   📁 File operations")
            print("\n🚀 The agent is ready for real desktop automation tasks!")
            
        except KeyboardInterrupt:
            print("\n⏹️  Demo stopped by user")
        except Exception as e:
            print(f"\n❌ Demo error: {e}")
        finally:
            if self.agent:
                await self.agent.stop()


async def main():
    """Main entry point."""
    demo = DesktopAutomationDemo()
    await demo.run_full_demo()


if __name__ == "__main__":
    print("🎬 Starting Desktop Automation Demo...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo stopped")
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        sys.exit(1)
