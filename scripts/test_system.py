#!/usr/bin/env python3
"""
System integration test for Desktop AI Agent.

This script performs comprehensive testing of the Desktop AI Agent system
to ensure all components are working correctly.
"""

import asyncio
import sys
import time
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent


async def test_system_initialization():
    """Test system initialization."""
    print("🔧 Testing system initialization...")
    
    settings = get_settings()
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        print("✅ System initialization successful")
        return app
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        return None


async def test_component_health(app):
    """Test component health."""
    print("🏥 Testing component health...")
    
    if not app.health_monitor:
        print("❌ Health monitor not available")
        return False
    
    try:
        health_summary = app.health_monitor.get_health_summary()
        overall_status = health_summary["overall_status"]
        
        print(f"Overall health: {overall_status}")
        
        for component, status in health_summary["components"].items():
            status_emoji = "✅" if status["status"] == "healthy" else "❌"
            print(f"  {status_emoji} {component}: {status['status']}")
        
        return overall_status in ["healthy", "degraded"]
        
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False


async def test_task_creation(app):
    """Test task creation and management."""
    print("📋 Testing task creation...")
    
    if not app.orchestrator:
        print("❌ Orchestrator not available")
        return False
    
    try:
        from desktop_ai_agent.core.orchestrator import Task
        
        # Create a simple test task
        test_task = Task(
            name="System Test Task",
            description="Test task for system validation",
            type="simple",
            parameters={"test": True}
        )
        
        task_id = await app.orchestrator.submit_task(test_task)
        print(f"✅ Task created: {task_id}")
        
        # Check task status
        task_status = app.orchestrator.get_task_status(task_id)
        if task_status:
            print(f"✅ Task status retrieved: {task_status.status}")
            return True
        else:
            print("❌ Failed to retrieve task status")
            return False
            
    except Exception as e:
        print(f"❌ Task creation failed: {e}")
        return False


async def test_ai_models(app):
    """Test AI model functionality."""
    print("🤖 Testing AI models...")
    
    if not app.ai_engine:
        print("❌ AI engine not available")
        return False
    
    try:
        # List available models
        models = app.ai_engine.list_models()
        print(f"Available models: {len(models)}")
        
        for model in models:
            print(f"  - {model.name} ({model.status})")
        
        if not models:
            print("⚠️  No AI models available")
            return False
        
        # Try to get default model
        default_model_id = app.ai_engine.get_default_model_id()
        if default_model_id:
            print(f"✅ Default model: {default_model_id}")
            
            # Test model loading if not already loaded
            model_info = app.ai_engine.get_model_info(default_model_id)
            if model_info and model_info.status != "loaded":
                print("🔄 Loading model...")
                success = await app.ai_engine.load_model(default_model_id)
                if success:
                    print("✅ Model loaded successfully")
                else:
                    print("❌ Model loading failed")
                    return False
            
            return True
        else:
            print("⚠️  No default model available")
            return False
            
    except Exception as e:
        print(f"❌ AI model test failed: {e}")
        return False


async def test_desktop_interaction(app):
    """Test desktop interaction capabilities."""
    print("🖥️  Testing desktop interaction...")
    
    if not app.desktop_engine:
        print("❌ Desktop engine not available")
        return False
    
    try:
        # Test window listing (this might fail on headless systems)
        try:
            windows = await app.desktop_engine.list_windows()
            print(f"✅ Found {len(windows)} windows")
        except Exception as e:
            print(f"⚠️  Window listing failed (expected on headless systems): {e}")
        
        # Test screen capture (this might fail on headless systems)
        try:
            screenshot = await app.desktop_engine.capture_screenshot()
            if screenshot:
                print(f"✅ Screenshot captured: {screenshot.width}x{screenshot.height}")
            else:
                print("⚠️  Screenshot capture failed (expected on headless systems)")
        except Exception as e:
            print(f"⚠️  Screenshot failed (expected on headless systems): {e}")
        
        # Desktop interaction tests are expected to fail on headless systems
        return True
        
    except Exception as e:
        print(f"❌ Desktop interaction test failed: {e}")
        return False


async def test_storage_system(app):
    """Test storage and persistence."""
    print("💾 Testing storage system...")
    
    if not app.storage_engine:
        print("❌ Storage engine not available")
        return False
    
    try:
        # Test configuration storage
        test_key = "system_test_key"
        test_value = {"test": True, "timestamp": time.time()}
        
        success = app.storage_engine.save_configuration(test_key, test_value, "json")
        if success:
            print("✅ Configuration save successful")
        else:
            print("❌ Configuration save failed")
            return False
        
        # Test configuration retrieval
        retrieved_value = app.storage_engine.load_configuration(test_key)
        if retrieved_value == test_value:
            print("✅ Configuration retrieval successful")
        else:
            print("❌ Configuration retrieval failed")
            return False
        
        # Test session management
        session_id = app.storage_engine.create_session(
            user_id="test_user",
            metadata={"test_session": True}
        )
        
        if session_id:
            print(f"✅ Session created: {session_id}")
            
            session_data = app.storage_engine.get_session(session_id)
            if session_data:
                print("✅ Session retrieval successful")
                return True
            else:
                print("❌ Session retrieval failed")
                return False
        else:
            print("❌ Session creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Storage system test failed: {e}")
        return False


async def test_safety_system(app):
    """Test safety and security features."""
    print("🔒 Testing safety system...")
    
    if not app.safety_engine:
        print("❌ Safety engine not available")
        return False
    
    try:
        # Test permission request
        permission = await app.safety_engine.request_permission(
            operation="test_operation",
            resource="test_resource",
            justification="System integration test"
        )
        
        if permission:
            print(f"✅ Permission requested: {permission.permission_id}")
            print(f"   Status: {permission.status}")
            print(f"   Risk level: {permission.risk_level}")
            return True
        else:
            print("❌ Permission request failed")
            return False
            
    except Exception as e:
        print(f"❌ Safety system test failed: {e}")
        return False


async def run_comprehensive_test():
    """Run comprehensive system test."""
    print("🚀 Starting Desktop AI Agent System Test")
    print("=" * 50)
    
    # Initialize system
    app = await test_system_initialization()
    if not app:
        print("❌ System test failed - initialization error")
        return False
    
    try:
        # Run all tests
        tests = [
            ("Component Health", test_component_health),
            ("Task Creation", test_task_creation),
            ("AI Models", test_ai_models),
            ("Desktop Interaction", test_desktop_interaction),
            ("Storage System", test_storage_system),
            ("Safety System", test_safety_system),
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n📊 Running {test_name} test...")
            try:
                result = await test_func(app)
                results.append((test_name, result))
                if result:
                    print(f"✅ {test_name} test passed")
                else:
                    print(f"❌ {test_name} test failed")
            except Exception as e:
                print(f"❌ {test_name} test error: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Results Summary:")
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status} {test_name}")
            if result:
                passed += 1
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Desktop AI Agent is ready to use.")
            return True
        elif passed >= total * 0.7:  # 70% pass rate
            print("⚠️  Most tests passed. System is functional with some limitations.")
            return True
        else:
            print("❌ System test failed. Please check the errors above.")
            return False
    
    finally:
        # Clean up
        if app:
            await app.stop()


def main():
    """Main entry point."""
    try:
        result = asyncio.run(run_comprehensive_test())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
