#!/usr/bin/env python3
"""
🚀 DESKTOP AI AGENT - COMPLETE SYSTEM LAUNCHER
==================================================

This script starts all systems including:
- Web Interface with Task Execution
- Continuous Learning System
- Performance Monitoring
- Health Monitoring
- Background AI Improvement
- Task Orchestration
- Safety Systems

Usage:
    python scripts/start_all_systems.py [options]

Options:
    --port PORT         Web interface port (default: 8000)
    --host HOST         Web interface host (default: localhost)
    --learning          Enable continuous learning (default: True)
    --monitoring        Enable performance monitoring (default: True)
    --debug             Enable debug logging
    --no-browser        Don't open browser automatically
"""

import sys
import argparse
import asyncio
import signal
import time
from pathlib import Path
from typing import Dict, Any, List
import subprocess
import threading
import webbrowser

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.conversation.engine import ConversationEngine
from desktop_ai_agent.learning.engine import LearningEngine
from desktop_ai_agent.web.chat_interface import WebChatInterface
from desktop_ai_agent.monitoring.metrics import MetricsCollector
from desktop_ai_agent.monitoring.health import HealthMonitor

import uvicorn
import structlog

class SystemManager:
    """Manages all Desktop AI Agent systems."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = structlog.get_logger(__name__)
        self.systems = {}
        self.running = False
        self.tasks = []
        
    async def initialize_all_systems(self):
        """Initialize all systems in the correct order."""
        print("🚀 DESKTOP AI AGENT - COMPLETE SYSTEM STARTUP")
        print("=" * 60)
        
        try:
            # 1. Initialize Core Agent
            print("🤖 Initializing Core AI Agent...")
            settings = get_settings('config.json')
            agent = DesktopAIAgent(settings)
            await agent.initialize()
            self.systems['agent'] = agent
            print("✅ Core AI Agent initialized")
            
            # 2. Initialize Conversation Engine
            print("💬 Initializing Conversation Engine...")
            conversation_engine = ConversationEngine(settings)
            self.systems['conversation'] = conversation_engine
            print("✅ Conversation Engine initialized")
            
            # 3. Initialize Learning Engine
            print("🧠 Initializing Learning Engine...")
            learning_engine = LearningEngine(settings)
            self.systems['learning'] = learning_engine
            print("✅ Learning Engine initialized")
            
            # 4. Set up dependencies
            print("🔗 Setting up system dependencies...")
            conversation_engine.set_dependencies(
                agent.ai_engine,
                agent.orchestrator,
                learning_engine
            )
            
            learning_engine.set_dependencies(
                agent.ai_engine,
                agent.storage_engine,
                conversation_engine
            )
            print("✅ Dependencies configured")
            
            # 5. Initialize Web Interface
            print("🌐 Initializing Enhanced Web Interface...")
            web_interface = WebChatInterface(
                conversation_engine,
                learning_engine,
                orchestrator=agent.orchestrator,
                safety_engine=agent.safety_engine,
                desktop_engine=agent.desktop_engine
            )
            self.systems['web'] = web_interface
            print("✅ Enhanced Web Interface initialized")
            
            # 6. Initialize Monitoring Systems
            if self.config.get('monitoring', True):
                print("📊 Initializing Monitoring Systems...")
                metrics_collector = MetricsCollector()
                health_monitor = HealthMonitor()
                self.systems['metrics'] = metrics_collector
                self.systems['health'] = health_monitor
                print("✅ Monitoring Systems initialized")
            
            print("\n🎉 ALL SYSTEMS INITIALIZED SUCCESSFULLY!")
            return True
            
        except Exception as e:
            self.logger.error("System initialization failed", error=str(e))
            print(f"❌ System initialization failed: {e}")
            return False
    
    async def start_continuous_learning(self):
        """Start continuous learning background process."""
        if not self.config.get('learning', True):
            return
            
        print("🧠 Starting Continuous Learning System...")
        
        learning_engine = self.systems.get('learning')
        if not learning_engine:
            print("⚠️ Learning engine not available")
            return
        
        async def learning_loop():
            """Continuous learning background loop."""
            while self.running:
                try:
                    # Analyze performance every 5 minutes
                    await learning_engine.analyze_performance()
                    
                    # Generate improvement proposals every 15 minutes
                    if int(time.time()) % 900 == 0:  # Every 15 minutes
                        await learning_engine.generate_improvement_proposals()
                    
                    # Learn from user interactions every minute
                    await learning_engine.learn_from_interactions()
                    
                    await asyncio.sleep(60)  # Check every minute
                    
                except Exception as e:
                    self.logger.error("Learning loop error", error=str(e))
                    await asyncio.sleep(60)
        
        # Start learning loop in background
        learning_task = asyncio.create_task(learning_loop())
        self.tasks.append(learning_task)
        print("✅ Continuous Learning System started")
    
    async def start_monitoring_systems(self):
        """Start monitoring and health check systems."""
        if not self.config.get('monitoring', True):
            return
            
        print("📊 Starting Monitoring Systems...")
        
        metrics_collector = self.systems.get('metrics')
        health_monitor = self.systems.get('health')
        
        if metrics_collector:
            # Start metrics collection
            metrics_task = asyncio.create_task(self._metrics_loop())
            self.tasks.append(metrics_task)
        
        if health_monitor:
            # Start health monitoring
            health_task = asyncio.create_task(self._health_loop())
            self.tasks.append(health_task)
        
        print("✅ Monitoring Systems started")
    
    async def _metrics_loop(self):
        """Background metrics collection loop."""
        metrics_collector = self.systems['metrics']
        
        while self.running:
            try:
                # Collect system metrics
                await metrics_collector.collect_system_metrics()
                
                # Collect AI performance metrics
                agent = self.systems.get('agent')
                if agent and hasattr(agent, 'ai_engine'):
                    await metrics_collector.collect_ai_metrics(agent.ai_engine)
                
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except Exception as e:
                self.logger.error("Metrics collection error", error=str(e))
                await asyncio.sleep(30)
    
    async def _health_loop(self):
        """Background health monitoring loop."""
        health_monitor = self.systems['health']
        
        while self.running:
            try:
                # Check system health
                health_status = await health_monitor.check_all_components()
                
                # Log any issues
                for component, status in health_status.items():
                    if not status.get('healthy', True):
                        self.logger.warning(f"Health issue detected", component=component, status=status)
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error("Health monitoring error", error=str(e))
                await asyncio.sleep(60)
    
    async def start_web_interface(self):
        """Start the web interface server."""
        print(f"🌐 Starting Web Interface on {self.config['host']}:{self.config['port']}...")
        
        web_interface = self.systems['web']
        
        # Create FastAPI app
        from fastapi import FastAPI
        app = FastAPI(title="Desktop AI Agent - Complete System")
        
        # Mount web interface
        app.mount("/", web_interface.app)
        
        # Configure uvicorn
        config = uvicorn.Config(
            app=app,
            host=self.config['host'],
            port=self.config['port'],
            log_level="info" if not self.config.get('debug') else "debug"
        )
        
        server = uvicorn.Server(config)
        
        # Start server in background
        server_task = asyncio.create_task(server.serve())
        self.tasks.append(server_task)
        
        print(f"✅ Web Interface started at http://{self.config['host']}:{self.config['port']}")
        
        # Open browser if requested
        if not self.config.get('no_browser', False):
            def open_browser():
                time.sleep(2)  # Wait for server to start
                webbrowser.open(f"http://{self.config['host']}:{self.config['port']}")
            
            threading.Thread(target=open_browser, daemon=True).start()
    
    async def start_all(self):
        """Start all systems."""
        self.running = True
        
        # Initialize all systems
        if not await self.initialize_all_systems():
            return False
        
        print("\n🚀 STARTING ALL SYSTEMS...")
        print("=" * 40)
        
        # Start all background systems
        await self.start_continuous_learning()
        await self.start_monitoring_systems()
        await self.start_web_interface()
        
        print("\n🎉 ALL SYSTEMS RUNNING!")
        print("=" * 40)
        print(f"🌐 Web Interface: http://{self.config['host']}:{self.config['port']}")
        print("🧠 Continuous Learning: Active")
        print("📊 Performance Monitoring: Active")
        print("🔒 Safety Systems: Active")
        print("🤖 AI Task Execution: Ready")
        print("\n💡 Try these commands in the web interface:")
        print("   • 'take a screenshot'")
        print("   • 'organize my desktop files'")
        print("   • 'create a new folder'")
        print("   • 'arrange windows'")
        print("\n⌨️  Press Ctrl+C to stop all systems")
        
        return True
    
    async def stop_all(self):
        """Stop all systems gracefully."""
        print("\n🛑 STOPPING ALL SYSTEMS...")
        self.running = False
        
        # Cancel all background tasks
        for task in self.tasks:
            if not task.done():
                task.cancel()
        
        # Stop agent
        agent = self.systems.get('agent')
        if agent:
            await agent.stop()
        
        print("✅ All systems stopped")

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Desktop AI Agent - Complete System Launcher")
    parser.add_argument("--port", type=int, default=8000, help="Web interface port")
    parser.add_argument("--host", default="localhost", help="Web interface host")
    parser.add_argument("--no-learning", action="store_true", help="Disable continuous learning")
    parser.add_argument("--no-monitoring", action="store_true", help="Disable monitoring")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--no-browser", action="store_true", help="Don't open browser")
    
    args = parser.parse_args()
    
    config = {
        'port': args.port,
        'host': args.host,
        'learning': not args.no_learning,
        'monitoring': not args.no_monitoring,
        'debug': args.debug,
        'no_browser': args.no_browser
    }
    
    system_manager = SystemManager(config)
    
    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        print(f"\n📡 Received signal {signum}")
        asyncio.create_task(system_manager.stop_all())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start all systems
        success = await system_manager.start_all()
        
        if success:
            # Keep running until interrupted
            while system_manager.running:
                await asyncio.sleep(1)
        
    except KeyboardInterrupt:
        print("\n⌨️  Keyboard interrupt received")
    except Exception as e:
        print(f"❌ System error: {e}")
    finally:
        await system_manager.stop_all()

if __name__ == "__main__":
    asyncio.run(main())
