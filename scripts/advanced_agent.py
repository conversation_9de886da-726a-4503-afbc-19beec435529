#!/usr/bin/env python3
"""
Advanced Desktop AI Agent with Conversational Interface and Self-Improvement

This script provides multiple interface options:
1. CLI Conversational Interface
2. Web-based Chat Interface  
3. Learning Management Interface

Features:
- Natural language conversation (Romanian & English)
- Continuous self-improvement and learning
- Real-time performance monitoring
- User feedback integration
- Autonomous capability expansion
"""

import asyncio
import sys
import argparse
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.conversation.engine import ConversationEngine
from desktop_ai_agent.learning.engine import LearningEngine
from desktop_ai_agent.web.chat_interface import WebChatInterface

import uvicorn
import structlog


class AdvancedDesktopAgent:
    """Advanced Desktop AI Agent with multiple interfaces."""
    
    def __init__(self):
        self.agent: Optional[DesktopAIAgent] = None
        self.conversation_engine: Optional[ConversationEngine] = None
        self.learning_engine: Optional[LearningEngine] = None
        self.web_interface: Optional[WebChatInterface] = None
        self.logger = structlog.get_logger(__name__)
    
    async def initialize(self):
        """Initialize all components."""
        print("🚀 ADVANCED DESKTOP AI AGENT")
        print("=" * 60)
        print("🧠 Conversational AI with Self-Improvement")
        print("🌐 Multiple Interface Options Available")
        print("📈 Continuous Learning & Adaptation")
        print("=" * 60)
        
        # Initialize core agent
        settings = get_settings('config.json')
        self.agent = DesktopAIAgent(settings)
        await self.agent.initialize()
        
        # Initialize conversation engine
        self.conversation_engine = ConversationEngine(settings)
        
        # Initialize learning engine
        self.learning_engine = LearningEngine(settings)
        
        # Set up dependencies
        self.conversation_engine.set_dependencies(
            self.agent.ai_engine,
            self.agent.orchestrator,
            self.learning_engine
        )
        
        self.learning_engine.set_dependencies(
            self.agent.ai_engine,
            self.agent.storage_engine,
            self.conversation_engine
        )
        
        # Initialize web interface
        self.web_interface = WebChatInterface(
            self.conversation_engine,
            self.learning_engine
        )
        
        print("✅ Advanced Desktop AI Agent initialized!")
        print(f"🤖 AI Model: CodeLlama-7B (Production Ready)")
        print(f"🔒 Safety System: Active")
        print(f"📚 Learning System: Active")
        print(f"🌐 Web Interface: Ready")
        print(f"💬 CLI Interface: Ready")
    
    async def run_cli_interface(self, user_id: str = "cli_user"):
        """Run the CLI conversational interface."""
        print(f"\n🎯 Starting CLI conversation with user: {user_id}")
        
        # Start conversation session
        session = await self.conversation_engine.start_conversation(
            user_id=user_id,
            platform="cli",
            voice_enabled=False
        )
        
        print(f"📝 Session ID: {session.session_id[:12]}...")
        
        # Display welcome message
        if session.messages:
            welcome_msg = session.messages[0]
            print(f"\n🤖 Agent: {welcome_msg.content}")
        
        # Start background learning
        learning_task = asyncio.create_task(self._background_learning())
        
        print("\n" + "=" * 60)
        print("💬 CLI CONVERSATION STARTED")
        print("=" * 60)
        print("💡 Tips:")
        print("  • Speak naturally in Romanian or English")
        print("  • Ask me to automate desktop tasks")
        print("  • Give me feedback to help me improve")
        print("  • Type 'help' for commands, 'quit' to exit")
        print("=" * 60)
        
        try:
            while True:
                # Get user input
                user_input = input("\n👤 You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Goodbye! Thanks for chatting with me.")
                    break
                
                elif user_input.lower() == 'help':
                    await self._show_help()
                    continue
                
                elif user_input.lower() == 'learning':
                    await self._show_learning_status()
                    continue
                
                elif user_input.lower() == 'proposals':
                    await self._show_proposals()
                    continue
                
                # Process message
                print("🤖 Agent: (thinking...)")
                
                response = await self.conversation_engine.process_user_message(
                    session.session_id,
                    user_input
                )
                
                print(f"\r🤖 Agent: {response.content}")
                
                # Show metadata
                if response.metadata:
                    inference_time = response.metadata.get('inference_time_ms', 0)
                    tokens = response.metadata.get('tokens', 0)
                    print(f"   ⚡ AI Response: {inference_time:.0f}ms | Tokens: {tokens}")
        
        except KeyboardInterrupt:
            print("\n👋 Conversation interrupted by user")
        
        finally:
            learning_task.cancel()
            await self.conversation_engine.end_conversation(session.session_id)
    
    async def run_web_interface(self, host: str = "localhost", port: int = 8000):
        """Run the web-based chat interface."""
        print(f"\n🌐 Starting web interface at http://{host}:{port}")
        print("💡 Open your browser and navigate to the URL above")
        print("🔄 The interface will auto-reload on code changes")

        # Import uvicorn server directly to avoid asyncio.run() conflict
        import uvicorn
        from uvicorn import Config, Server

        config = Config(
            app=self.web_interface.app,
            host=host,
            port=port,
            reload=False,
            log_level="info"
        )

        server = Server(config)
        await server.serve()
    
    async def run_learning_management(self):
        """Run the learning management interface."""
        print("\n📚 LEARNING MANAGEMENT INTERFACE")
        print("=" * 50)
        
        while True:
            print("\n🧠 Learning System Options:")
            print("1. Start learning session")
            print("2. View learning statistics")
            print("3. Review improvement proposals")
            print("4. Approve/reject proposals")
            print("5. View performance metrics")
            print("6. Export learning data")
            print("0. Exit")
            
            choice = input("\n🎯 Select option: ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                await self._start_learning_session()
            elif choice == "2":
                await self._show_learning_statistics()
            elif choice == "3":
                await self._review_proposals()
            elif choice == "4":
                await self._manage_proposals()
            elif choice == "5":
                await self._show_performance_metrics()
            elif choice == "6":
                await self._export_learning_data()
            else:
                print("❌ Invalid option. Please try again.")
    
    async def _background_learning(self):
        """Run background learning process."""
        while True:
            try:
                await asyncio.sleep(1800)  # 30 minutes
                
                if len(self.learning_engine.performance_metrics) >= 5:
                    print("\n🧠 Running background learning analysis...")
                    await self.learning_engine.start_learning_session()
                    
                    proposals = await self.learning_engine.get_pending_proposals()
                    if proposals:
                        print(f"💡 {len(proposals)} new improvement proposals available!")
                        print("💡 Type 'proposals' to review them.")
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Background learning error", error=str(e))
    
    async def _show_help(self):
        """Show help information."""
        print("\n🆘 HELP - Available Commands:")
        print("-" * 40)
        print("💬 Natural conversation:")
        print("  • Just type naturally in Romanian or English")
        print("  • Ask me to automate tasks")
        print("  • Request information")
        print("")
        print("🔧 Special commands:")
        print("  • help     - Show this help")
        print("  • learning - Show learning status")
        print("  • proposals - Show improvement proposals")
        print("  • quit     - Exit conversation")
    
    async def _show_learning_status(self):
        """Show learning system status."""
        stats = await self.learning_engine.get_learning_statistics()
        
        print("\n🧠 LEARNING SYSTEM STATUS:")
        print("-" * 35)
        print(f"📊 Performance Metrics: {stats['total_metrics']}")
        print(f"💡 Pending Proposals: {stats['pending_proposals']}")
        print(f"✅ Implemented Improvements: {stats['implemented_improvements']}")
        print(f"🔄 Learning Enabled: {stats['learning_enabled']}")
        print(f"📚 Active Session: {stats['active_session']}")
    
    async def _show_proposals(self):
        """Show improvement proposals."""
        proposals = await self.learning_engine.get_pending_proposals()
        
        print(f"\n💡 IMPROVEMENT PROPOSALS ({len(proposals)} pending):")
        print("-" * 45)
        
        if not proposals:
            print("✨ No pending proposals. Keep using the system to generate insights!")
            return
        
        for i, proposal in enumerate(proposals, 1):
            print(f"\n{i}. {proposal.title}")
            print(f"   Description: {proposal.description[:100]}...")
            print(f"   Timeline: {proposal.estimated_timeline} days")
            print(f"   Status: {proposal.status.value}")
    
    async def _start_learning_session(self):
        """Start a new learning session."""
        print("🧠 Starting learning session...")
        session = await self.learning_engine.start_learning_session()
        print(f"✅ Learning session started: {session.session_id[:12]}...")
    
    async def _show_learning_statistics(self):
        """Show detailed learning statistics."""
        stats = await self.learning_engine.get_learning_statistics()
        
        print("\n📊 DETAILED LEARNING STATISTICS:")
        print("-" * 40)
        for key, value in stats.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
    
    async def _review_proposals(self):
        """Review improvement proposals in detail."""
        proposals = await self.learning_engine.get_pending_proposals()
        
        for proposal in proposals:
            print(f"\n📋 PROPOSAL: {proposal.title}")
            print(f"Description: {proposal.description}")
            print(f"Rationale: {proposal.rationale}")
            print(f"Timeline: {proposal.estimated_timeline} days")
            print(f"Risks: {', '.join(proposal.risks)}")
            print("-" * 40)
    
    async def _manage_proposals(self):
        """Manage (approve/reject) proposals."""
        proposals = await self.learning_engine.get_pending_proposals()
        
        if not proposals:
            print("No pending proposals to manage.")
            return
        
        for i, proposal in enumerate(proposals, 1):
            print(f"{i}. {proposal.title}")
        
        try:
            choice = int(input("Select proposal number: ")) - 1
            if 0 <= choice < len(proposals):
                proposal = proposals[choice]
                action = input("Approve (a) or Reject (r)? ").lower()
                
                if action == 'a':
                    await self.learning_engine.approve_proposal(proposal.proposal_id, "user")
                    print("✅ Proposal approved!")
                elif action == 'r':
                    reason = input("Rejection reason: ")
                    await self.learning_engine.reject_proposal(proposal.proposal_id, reason)
                    print("❌ Proposal rejected.")
        except (ValueError, IndexError):
            print("Invalid selection.")
    
    async def _show_performance_metrics(self):
        """Show performance metrics."""
        metrics = self.learning_engine.performance_metrics[-10:]  # Last 10
        
        print("\n📈 RECENT PERFORMANCE METRICS:")
        print("-" * 40)
        
        for metric in metrics:
            print(f"{metric.metric_name}: {metric.value} {metric.unit} ({metric.task_type})")
    
    async def _export_learning_data(self):
        """Export learning data."""
        print("📤 Learning data export functionality coming soon!")
        print("💡 This will export insights, proposals, and metrics to JSON/CSV")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Advanced Desktop AI Agent")
    parser.add_argument(
        "--interface", 
        choices=["cli", "web", "learning"], 
        default="cli",
        help="Interface to run (default: cli)"
    )
    parser.add_argument("--host", default="localhost", help="Web interface host")
    parser.add_argument("--port", type=int, default=8000, help="Web interface port")
    parser.add_argument("--user-id", default="user", help="User ID for CLI interface")
    
    args = parser.parse_args()
    
    agent = AdvancedDesktopAgent()
    
    try:
        await agent.initialize()
        
        if args.interface == "cli":
            await agent.run_cli_interface(args.user_id)
        elif args.interface == "web":
            await agent.run_web_interface(args.host, args.port)
        elif args.interface == "learning":
            await agent.run_learning_management()
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if agent.agent:
            await agent.agent.stop()


if __name__ == "__main__":
    print("🚀 Starting Advanced Desktop AI Agent...")
    asyncio.run(main())
