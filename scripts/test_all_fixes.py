#!/usr/bin/env python3
"""
Comprehensive test script to verify all fixes are working.

This script tests:
1. Real AI model integration (not mocks)
2. Fixed safety system (proper risk assessment)
3. Fixed database health checks
4. Fixed attribute errors
5. Real task execution
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.ai.models import InferenceRequest
from desktop_ai_agent.core.orchestrator import Task, TaskPriority


async def test_real_ai_integration():
    """Test 1: Real AI model integration (not mocks)."""
    print("🧠 TEST 1: Real AI Model Integration")
    print("-" * 50)
    
    settings = get_settings('config.json')
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        
        # Test real AI inference
        request = InferenceRequest(
            model_id='local_codellama-7b-instruct.Q4_K_M',
            prompt='You are a Desktop AI Agent. List 3 specific desktop automation tasks you can help with.',
            max_tokens=80,
            temperature=0.7
        )
        
        response = await app.ai_engine.generate_response(request)
        
        if response.success and "Mock response" not in response.text:
            print("✅ REAL AI inference working!")
            print(f"   Response: {response.text[:100]}...")
            print(f"   Time: {response.inference_time_ms:.1f}ms")
            return True
        else:
            print("❌ Still getting mock responses or failed")
            return False
            
    except Exception as e:
        print(f"❌ AI test failed: {e}")
        return False
    finally:
        await app.stop()


async def test_fixed_safety_system():
    """Test 2: Fixed safety system (proper risk assessment)."""
    print("\n🔒 TEST 2: Fixed Safety System")
    print("-" * 50)
    
    settings = get_settings('config.json')
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        
        # Test dangerous command detection
        dangerous_permission = await app.safety_engine.request_permission(
            operation='system_command_execution',
            resource='sudo rm -rf /',
            justification='Testing dangerous command detection'
        )
        
        # Test safe operation
        safe_permission = await app.safety_engine.request_permission(
            operation='read_file',
            resource='/home/<USER>/document.txt',
            justification='Reading user document'
        )
        
        dangerous_correct = (dangerous_permission and 
                           dangerous_permission.risk_level.name == 'CRITICAL' and
                           dangerous_permission.status.name == 'DENIED')
        
        safe_correct = (safe_permission and 
                       safe_permission.risk_level.name == 'LOW')
        
        if dangerous_correct and safe_correct:
            print("✅ Safety system working correctly!")
            print(f"   Dangerous command: {dangerous_permission.risk_level} / {dangerous_permission.status}")
            print(f"   Safe operation: {safe_permission.risk_level} / {safe_permission.status}")
            return True
        else:
            print("❌ Safety system not working correctly")
            return False
            
    except Exception as e:
        print(f"❌ Safety test failed: {e}")
        return False
    finally:
        await app.stop()


async def test_fixed_database_health():
    """Test 3: Fixed database health checks."""
    print("\n🏥 TEST 3: Fixed Database Health")
    print("-" * 50)
    
    settings = get_settings('config.json')
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        
        # Wait a moment for health checks to run
        await asyncio.sleep(2)
        
        # Check if database health is working
        if app.health_monitor:
            health_check = await app.health_monitor._check_database_health()
            
            if health_check.status.name == 'HEALTHY':
                print("✅ Database health check working!")
                print(f"   Status: {health_check.status}")
                print(f"   Message: {health_check.message}")
                return True
            else:
                print(f"❌ Database health check failed: {health_check.message}")
                return False
        else:
            print("❌ Health monitor not available")
            return False
            
    except Exception as e:
        print(f"❌ Database health test failed: {e}")
        return False
    finally:
        await app.stop()


async def test_fixed_attribute_errors():
    """Test 4: Fixed attribute errors (storage_engine vs state_engine)."""
    print("\n🐛 TEST 4: Fixed Attribute Errors")
    print("-" * 50)
    
    settings = get_settings('config.json')
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        
        # Test that storage_engine attribute exists and works
        if hasattr(app, 'storage_engine') and app.storage_engine:
            # Test session creation
            session = await app.storage_engine.session_manager.create_session('test_user')
            
            # Test configuration storage
            test_config = {'test_key': 'test_value'}
            app.storage_engine.save_configuration('test_config', str(test_config))
            retrieved = app.storage_engine.load_configuration('test_config')
            
            if session and retrieved == str(test_config):
                print("✅ Attribute errors fixed!")
                print(f"   Session created: {session.session_id[:12]}...")
                print(f"   Config storage working: {retrieved}")
                return True
            else:
                print("❌ Storage operations failed")
                return False
        else:
            print("❌ storage_engine attribute missing")
            return False
            
    except Exception as e:
        print(f"❌ Attribute test failed: {e}")
        return False
    finally:
        await app.stop()


async def test_real_task_execution():
    """Test 5: Real task execution (not just queuing)."""
    print("\n🔄 TEST 5: Real Task Execution")
    print("-" * 50)
    
    settings = get_settings('config.json')
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        
        # Create a real desktop automation task
        task = Task(
            name='Test Desktop Organization',
            description='Test real task execution with desktop automation',
            type='desktop_automation',
            priority=TaskPriority.NORMAL,
            parameters={
                'action': 'organize_files',
                'source': '/home/<USER>/Desktop',
                'create_folders': ['Documents', 'Images', 'Videos']
            }
        )
        
        # Submit task
        task_id = await app.orchestrator.submit_task(task)
        print(f"✅ Task submitted: {task_id}")
        
        # Wait for task to complete
        for i in range(10):  # Wait up to 5 seconds
            task_status = app.orchestrator.get_task_status(task_id)
            if task_status:
                print(f"   Status: {task_status.status}, Progress: {task_status.progress:.1%}")
                
                if task_status.status.value == 'completed':
                    print("✅ Real task execution working!")
                    return True
                elif task_status.status.value == 'failed':
                    print(f"❌ Task failed: {task_status.error_message}")
                    return False
            
            await asyncio.sleep(0.5)
        
        print("❌ Task did not complete in time")
        return False
        
    except Exception as e:
        print(f"❌ Task execution test failed: {e}")
        return False
    finally:
        await app.stop()


async def run_comprehensive_tests():
    """Run all comprehensive tests."""
    print("🚀 COMPREHENSIVE SYSTEM TESTS - ALL FIXES VERIFICATION")
    print("=" * 70)
    print("Testing all fixes to ensure everything is genuinely functional")
    print("=" * 70)
    
    tests = [
        ("Real AI Integration", test_real_ai_integration),
        ("Fixed Safety System", test_fixed_safety_system),
        ("Fixed Database Health", test_fixed_database_health),
        ("Fixed Attribute Errors", test_fixed_attribute_errors),
        ("Real Task Execution", test_real_task_execution),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
        
        # Small delay between tests
        await asyncio.sleep(1)
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST RESULTS:")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL FIXES VERIFIED - SYSTEM IS GENUINELY FUNCTIONAL!")
        return True
    else:
        print("⚠️  Some fixes still need work")
        return False


def main():
    """Main entry point."""
    try:
        result = asyncio.run(run_comprehensive_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Tests failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
