#!/usr/bin/env python3
"""
Interactive Desktop AI Agent Interface

This script provides a practical interface to interact with the Desktop AI Agent
and see real desktop automation tasks being performed with visible movements.
"""

import asyncio
import sys
import json
from pathlib import Path
from typing import Dict, Any, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.ai.models import InferenceRequest
from desktop_ai_agent.core.orchestrator import Task, TaskPriority
from desktop_ai_agent.desktop.models import DesktopAction


class InteractiveDesktopAgent:
    """Interactive interface for the Desktop AI Agent."""
    
    def __init__(self):
        self.agent = None
        self.running = False
        
    async def initialize(self):
        """Initialize the Desktop AI Agent."""
        print("🚀 DESKTOP AI AGENT - INTERACTIVE INTERFACE")
        print("=" * 60)
        print("Initializing agent with real AI capabilities...")
        
        settings = get_settings('config.json')
        self.agent = DesktopAIAgent(settings)
        
        await self.agent.initialize()
        print("✅ Desktop AI Agent ready for commands!")
        print(f"🤖 AI Model: {settings.ai_model.model_name}")
        print(f"🔒 Safe Mode: {settings.security.safe_mode}")
        
    async def show_capabilities(self):
        """Show what the agent can do."""
        print("\n🎯 AVAILABLE DESKTOP AUTOMATION TASKS:")
        print("-" * 40)
        
        capabilities = [
            "📱 Window Management - List, focus, resize, move windows",
            "📸 Screenshots - Capture full screen or specific regions", 
            "🖱️  Mouse Control - Click, drag, scroll at specific coordinates",
            "⌨️  Keyboard Input - Type text, send key combinations",
            "📁 File Operations - Create folders, organize files",
            "🔍 Screen Analysis - Find elements, read text from screen",
            "🤖 AI Planning - Generate step-by-step automation plans"
        ]
        
        for capability in capabilities:
            print(f"  {capability}")
            
    async def list_windows(self):
        """List all open windows."""
        print("\n🖥️  CURRENT OPEN WINDOWS:")
        print("-" * 30)
        
        try:
            windows = await self.agent.desktop_engine.list_windows()
            
            if not windows:
                print("  No windows detected")
                return
                
            for i, window in enumerate(windows, 1):
                status = "🎯 ACTIVE" if window.is_active else "  "
                print(f"  {i}. {status} {window.title[:50]}")
                print(f"     📱 App: {window.application}")
                print(f"     📐 Size: {window.size.width}x{window.size.height}")
                print(f"     📍 Position: ({window.position.x}, {window.position.y})")
                print()
                
        except Exception as e:
            print(f"❌ Error listing windows: {e}")
            
    async def take_screenshot(self):
        """Take a screenshot."""
        print("\n📸 TAKING SCREENSHOT...")
        print("-" * 25)
        
        try:
            action = DesktopAction(
                action_id="screenshot_001",
                action_type="screenshot",
                parameters={"format": "PNG", "quality": 95}
            )
            
            result = await self.agent.desktop_engine.execute_desktop_action(action)
            
            if result.success:
                print("✅ Screenshot captured successfully!")
                print(f"📁 Saved to: {result.result.get('file_path', 'screenshot.png')}")
            else:
                print(f"❌ Screenshot failed: {result.error_message}")
                
        except Exception as e:
            print(f"❌ Error taking screenshot: {e}")
            
    async def ai_conversation(self, user_input: str):
        """Have a conversation with the AI."""
        print(f"\n💭 AI THINKING ABOUT: '{user_input}'")
        print("-" * 50)
        
        try:
            request = InferenceRequest(
                model_id='local_codellama-7b-instruct.Q4_K_M',
                prompt=f"""You are a helpful Desktop AI Agent. The user said: "{user_input}"

Respond helpfully and suggest specific desktop automation tasks you can perform. Be practical and actionable.""",
                max_tokens=150,
                temperature=0.7
            )
            
            print("🤖 AI generating response...")
            response = await self.agent.ai_engine.generate_response(request)
            
            if response.success:
                print(f"\n🤖 AI Response:")
                print(f"   {response.text}")
                print(f"\n⚡ Response time: {response.inference_time_ms:.1f}ms")
                print(f"🔢 Tokens: {response.total_tokens}")
            else:
                print(f"❌ AI response failed: {response.error_message}")
                
        except Exception as e:
            print(f"❌ Error in AI conversation: {e}")
            
    async def execute_task(self, task_description: str):
        """Execute a desktop automation task."""
        print(f"\n🔄 EXECUTING TASK: '{task_description}'")
        print("-" * 50)
        
        try:
            # Create task based on description
            task_type = self.determine_task_type(task_description)
            parameters = self.parse_task_parameters(task_description, task_type)
            
            task = Task(
                name=f"User Task: {task_description[:30]}...",
                description=task_description,
                type=task_type,
                priority=TaskPriority.HIGH,
                parameters=parameters
            )
            
            # Submit task
            task_id = await self.agent.orchestrator.submit_task(task)
            print(f"✅ Task submitted: {task_id[:12]}...")
            
            # Monitor execution
            print("🔄 Monitoring task execution...")
            for i in range(20):  # Wait up to 10 seconds
                task_status = self.agent.orchestrator.get_task_status(task_id)
                if task_status:
                    print(f"   Status: {task_status.status}, Progress: {task_status.progress:.1%}")
                    
                    if task_status.status.value == 'completed':
                        print("✅ Task completed successfully!")
                        return True
                    elif task_status.status.value == 'failed':
                        print(f"❌ Task failed: {task_status.error_message}")
                        return False
                        
                await asyncio.sleep(0.5)
                
            print("⏰ Task execution timeout")
            return False
            
        except Exception as e:
            print(f"❌ Error executing task: {e}")
            return False
            
    def determine_task_type(self, description: str) -> str:
        """Determine task type from description."""
        description_lower = description.lower()
        
        if any(word in description_lower for word in ['window', 'focus', 'resize', 'move']):
            return 'desktop_automation'
        elif any(word in description_lower for word in ['screenshot', 'capture', 'image']):
            return 'desktop_automation'
        elif any(word in description_lower for word in ['click', 'mouse', 'drag']):
            return 'desktop_automation'
        elif any(word in description_lower for word in ['type', 'keyboard', 'key']):
            return 'desktop_automation'
        elif any(word in description_lower for word in ['file', 'folder', 'organize']):
            return 'file_operation'
        else:
            return 'desktop_automation'
            
    def parse_task_parameters(self, description: str, task_type: str) -> Dict[str, Any]:
        """Parse task parameters from description."""
        description_lower = description.lower()
        
        if 'screenshot' in description_lower:
            return {'action': 'screenshot', 'format': 'PNG'}
        elif 'window' in description_lower:
            return {'action': 'list_windows'}
        elif 'organize' in description_lower:
            return {
                'action': 'organize_files',
                'source': '/home/<USER>/Desktop',
                'create_folders': ['Documents', 'Images', 'Videos']
            }
        else:
            return {'action': 'general_automation', 'description': description}
            
    async def show_menu(self):
        """Show interactive menu."""
        print("\n" + "=" * 60)
        print("🎮 INTERACTIVE COMMANDS:")
        print("=" * 60)
        print("1. 📱 list-windows     - Show all open windows")
        print("2. 📸 screenshot       - Take a screenshot")
        print("3. 🤖 ask <question>   - Ask the AI a question")
        print("4. 🔄 task <description> - Execute a desktop task")
        print("5. 🎯 capabilities     - Show what I can do")
        print("6. ❌ quit            - Exit the agent")
        print("=" * 60)
        
    async def run_interactive(self):
        """Run the interactive interface."""
        await self.initialize()
        await self.show_capabilities()
        
        self.running = True
        
        while self.running:
            await self.show_menu()
            
            try:
                user_input = input("\n🎯 Enter command: ").strip()
                
                if not user_input:
                    continue
                    
                parts = user_input.split(' ', 1)
                command = parts[0].lower()
                args = parts[1] if len(parts) > 1 else ""
                
                if command in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    self.running = False
                    
                elif command == 'list-windows':
                    await self.list_windows()
                    
                elif command == 'screenshot':
                    await self.take_screenshot()
                    
                elif command == 'ask':
                    if args:
                        await self.ai_conversation(args)
                    else:
                        print("❌ Please provide a question after 'ask'")
                        
                elif command == 'task':
                    if args:
                        await self.execute_task(args)
                    else:
                        print("❌ Please provide a task description after 'task'")
                        
                elif command == 'capabilities':
                    await self.show_capabilities()
                    
                else:
                    print(f"❌ Unknown command: {command}")
                    print("💡 Type a number (1-6) or command name")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                self.running = False
            except Exception as e:
                print(f"❌ Error: {e}")
                
        # Cleanup
        if self.agent:
            await self.agent.stop()


async def main():
    """Main entry point."""
    agent_interface = InteractiveDesktopAgent()
    await agent_interface.run_interactive()


if __name__ == "__main__":
    print("🚀 Starting Interactive Desktop AI Agent...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Agent stopped by user")
    except Exception as e:
        print(f"❌ Agent failed: {e}")
        sys.exit(1)
