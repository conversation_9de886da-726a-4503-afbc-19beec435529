#!/usr/bin/env python3
"""
Desktop AI Agent Demonstration Script.

This script demonstrates the key capabilities of the Desktop AI Agent
with practical automation examples.
"""

import asyncio
import sys
import time
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.core.orchestrator import Task, TaskPriority
from desktop_ai_agent.ai.models import InferenceRequest


async def demo_ai_task_planning():
    """Demonstrate AI-powered task planning."""
    print("🧠 Demo: AI-Powered Task Planning")
    print("=" * 50)
    
    settings = get_settings('config.json')
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        
        # Create a task planning request
        planning_prompt = """You are a Desktop AI Agent. Break down this complex task into specific, executable steps:

Task: "Organize my desktop by creating folders for different file types and moving files accordingly"

Please provide a detailed step-by-step plan with specific actions like:
- Create folder
- Move files
- Check file types
- etc.

Be specific and actionable."""

        request = InferenceRequest(
            model_id="local_codellama-7b-instruct.Q4_K_M",
            prompt=planning_prompt,
            max_tokens=300,
            temperature=0.3
        )
        
        print("💭 Prompt:", planning_prompt[:100] + "...")
        print("🔄 Generating AI response...")
        
        response = await app.ai_engine.generate_response(request)
        
        if response.success:
            print("✅ AI Task Planning Response:")
            print("-" * 30)
            print(response.text)
            print("-" * 30)
            print(f"⚡ Generated in {response.inference_time_ms:.1f}ms")
            print(f"🔢 Tokens: {response.total_tokens} ({response.tokens_per_second:.1f} tokens/sec)")
            return True
        else:
            print(f"❌ AI planning failed: {response.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False
    finally:
        await app.stop()


async def demo_desktop_automation():
    """Demonstrate desktop automation capabilities."""
    print("\n🖥️  Demo: Desktop Automation")
    print("=" * 50)
    
    settings = get_settings('config.json')
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        
        # Demo 1: Window Management
        print("📋 Demo 1: Window Management")
        windows = await app.desktop_engine.list_windows()
        print(f"✅ Found {len(windows)} open windows:")
        
        for i, window in enumerate(windows[:3]):  # Show first 3 windows
            print(f"  {i+1}. {window.title} ({window.application})")
            print(f"     Size: {window.size.width}x{window.size.height}")
            print(f"     Position: ({window.position.x}, {window.position.y})")
            print(f"     Active: {window.is_active}")
        
        # Demo 2: Task Creation and Management
        print("\n📋 Demo 2: Task Creation and Management")
        
        # Create a screenshot task
        screenshot_task = Task(
            name="Desktop Screenshot",
            description="Capture a screenshot of the current desktop",
            type="desktop_action",
            priority=TaskPriority.NORMAL,
            parameters={
                "action": "screenshot",
                "format": "PNG",
                "quality": 95
            }
        )
        
        task_id = await app.orchestrator.submit_task(screenshot_task)
        print(f"✅ Screenshot task created: {task_id}")
        
        # Monitor task progress
        for i in range(5):
            task_status = app.orchestrator.get_task_status(task_id)
            if task_status:
                print(f"   Status: {task_status.status}, Progress: {task_status.progress:.1%}")
                if task_status.status.value in ['completed', 'failed']:
                    break
            await asyncio.sleep(0.5)
        
        # Create a window management task
        window_task = Task(
            name="Window Management",
            description="List and analyze open windows",
            type="desktop_action",
            parameters={
                "action": "list_windows",
                "analyze": True
            }
        )
        
        task_id2 = await app.orchestrator.submit_task(window_task)
        print(f"✅ Window management task created: {task_id2}")
        
        return True
        
    except Exception as e:
        print(f"❌ Desktop automation demo failed: {e}")
        return False
    finally:
        await app.stop()


async def demo_safety_and_permissions():
    """Demonstrate safety and permission system."""
    print("\n🔒 Demo: Safety and Permission System")
    print("=" * 50)
    
    settings = get_settings('config.json')
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        
        # Demo 1: Permission Request
        print("📋 Demo 1: Permission Request")
        
        permission = await app.safety_engine.request_permission(
            operation="file_system_access",
            resource="/home/<USER>/documents",
            justification="Organizing desktop files by type"
        )
        
        if permission:
            print(f"✅ Permission requested: {permission.permission_id}")
            print(f"   Operation: {permission.operation}")
            print(f"   Resource: {permission.resource}")
            print(f"   Status: {permission.status}")
            print(f"   Risk Level: {permission.risk_level}")
            print(f"   Justification: {permission.justification}")
        
        # Demo 2: Risk Assessment
        print("\n📋 Demo 2: Risk Assessment")
        
        high_risk_permission = await app.safety_engine.request_permission(
            operation="system_command_execution",
            resource="sudo rm -rf /",
            justification="Testing high-risk operation"
        )
        
        if high_risk_permission:
            print(f"⚠️  High-risk operation detected:")
            print(f"   Risk Level: {high_risk_permission.risk_level}")
            print(f"   Status: {high_risk_permission.status}")
        
        # Demo 3: Audit Log
        print("\n📋 Demo 3: Audit Logging")
        audit_events = app.safety_engine.get_recent_audit_events(limit=3)
        print(f"✅ Recent audit events ({len(audit_events)}):")
        
        for event in audit_events:
            print(f"   - {event.event_type}: {event.description}")
            print(f"     Time: {event.timestamp}")
            print(f"     User: {event.user_id or 'system'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Safety demo failed: {e}")
        return False
    finally:
        await app.stop()


async def demo_ai_conversation():
    """Demonstrate AI conversation capabilities."""
    print("\n💬 Demo: AI Conversation and Context")
    print("=" * 50)
    
    settings = get_settings('config.json')
    app = DesktopAIAgent(settings)
    
    try:
        await app.initialize()
        
        # Conversation examples
        conversations = [
            "Hello! I'm a new user. What can you help me automate on my desktop?",
            "How would you help me organize my messy Downloads folder?",
            "Can you create a task to take a screenshot and save it with today's date?",
            "What safety measures do you have in place to prevent harmful operations?"
        ]
        
        for i, prompt in enumerate(conversations, 1):
            print(f"\n💭 Question {i}: {prompt}")
            
            request = InferenceRequest(
                model_id="local_codellama-7b-instruct.Q4_K_M",
                prompt=f"You are a helpful Desktop AI Agent. User asks: {prompt}",
                max_tokens=150,
                temperature=0.7
            )
            
            response = await app.ai_engine.generate_response(request)
            
            if response.success:
                print(f"🤖 AI Response: {response.text}")
                print(f"   ⚡ {response.inference_time_ms:.1f}ms, {response.tokens_per_second:.1f} tokens/sec")
            else:
                print(f"❌ Response failed: {response.error_message}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI conversation demo failed: {e}")
        return False
    finally:
        await app.stop()


async def run_comprehensive_demo():
    """Run comprehensive demonstration of Desktop AI Agent."""
    print("🚀 Desktop AI Agent - Comprehensive Demonstration")
    print("=" * 60)
    print("This demo showcases the key capabilities of the Desktop AI Agent:")
    print("- AI-powered task planning and decomposition")
    print("- Desktop automation and window management")
    print("- Safety and permission management")
    print("- Intelligent conversation and assistance")
    print("=" * 60)
    
    demos = [
        ("AI Task Planning", demo_ai_task_planning),
        ("Desktop Automation", demo_desktop_automation),
        ("Safety & Permissions", demo_safety_and_permissions),
        ("AI Conversation", demo_ai_conversation),
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        print(f"\n🎯 Running {demo_name} Demo...")
        try:
            result = await demo_func()
            results.append((demo_name, result))
            
            if result:
                print(f"✅ {demo_name} demo completed successfully!")
            else:
                print(f"❌ {demo_name} demo failed!")
                
        except Exception as e:
            print(f"❌ {demo_name} demo error: {e}")
            results.append((demo_name, False))
        
        # Small delay between demos
        await asyncio.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Demonstration Results Summary:")
    
    passed = 0
    total = len(results)
    
    for demo_name, result in results:
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"  {status} {demo_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} demos successful")
    
    if passed == total:
        print("🎉 All demonstrations completed successfully!")
        print("🚀 Desktop AI Agent is fully functional and ready for use!")
        return True
    elif passed >= total * 0.75:  # 75% success rate
        print("⚠️  Most demonstrations successful. System is functional.")
        return True
    else:
        print("❌ Multiple demonstration failures. Please check system configuration.")
        return False


def main():
    """Main entry point."""
    try:
        result = asyncio.run(run_comprehensive_demo())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
