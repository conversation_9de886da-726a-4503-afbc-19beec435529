#!/usr/bin/env python3
"""
Startup script for Desktop AI Agent.

This script provides an easy way to start the Desktop AI Agent with
automatic model discovery and setup.
"""

import argparse
import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from desktop_ai_agent.core.config import get_settings
from desktop_ai_agent.main import DesktopAIAgent
from desktop_ai_agent.utils.model_downloader import setup_model_for_agent


async def main():
    parser = argparse.ArgumentParser(description="Start Desktop AI Agent")
    parser.add_argument("--host", default="127.0.0.1", help="API server host")
    parser.add_argument("--port", type=int, default=8000, help="API server port")
    parser.add_argument("--config", type=str, help="Configuration file path")
    parser.add_argument("--auto-setup-model", action="store_true", help="Automatically set up AI model")
    parser.add_argument("--skip-model-check", action="store_true", help="Skip AI model availability check")
    
    args = parser.parse_args()
    
    print("🚀 Starting Desktop AI Agent...")
    
    # Load settings
    settings = get_settings()
    
    # Override settings from command line
    if args.host:
        settings.api.host = args.host
    if args.port:
        settings.api.port = args.port
    
    # Check for AI model if not skipping
    if not args.skip_model_check:
        print("🤖 Checking AI model availability...")
        
        found_model, result = setup_model_for_agent(settings)
        
        if found_model:
            print(f"✅ Using AI model: {result}")
        elif result and args.auto_setup_model:
            print(f"📥 Auto-downloading recommended model: {result}")
            from desktop_ai_agent.utils.model_downloader import ModelDownloader
            
            downloader = ModelDownloader(settings)
            success = downloader.download_model(result)
            
            if success:
                print("✅ Model downloaded successfully!")
                # Update settings to use the downloaded model
                recommended = downloader.list_recommended_models()
                model_info = recommended[result]
                model_path = downloader.models_dir / model_info["filename"]
                settings.ai_model.model_path = str(model_path)
            else:
                print("❌ Model download failed!")
                if not args.skip_model_check:
                    print("Use --skip-model-check to start without AI model")
                    return
        elif result:
            print(f"💡 Recommended model: {result}")
            print("To download automatically, use --auto-setup-model")
            print("Or download manually: python scripts/setup_models.py --download " + result)
            if not args.skip_model_check:
                print("Use --skip-model-check to start without AI model")
                return
        else:
            print("⚠️  No AI models found or recommended")
            if not args.skip_model_check:
                print("Use --skip-model-check to start without AI model")
                return
    
    # Create and initialize application
    app = DesktopAIAgent(settings)
    
    try:
        print("🔧 Initializing system components...")
        await app.initialize()
        
        print(f"🌐 Starting API server on {settings.api.host}:{settings.api.port}")
        print(f"📖 API documentation: http://{settings.api.host}:{settings.api.port}/docs")
        print("🛑 Press Ctrl+C to stop")
        
        await app.start()
        
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested by user")
    except Exception as e:
        print(f"❌ Application failed: {e}")
        sys.exit(1)
    finally:
        print("🔄 Shutting down...")
        await app.stop()
        print("✅ Desktop AI Agent stopped")


if __name__ == "__main__":
    asyncio.run(main())
