#!/usr/bin/env python3
"""
Quick WebSocket test for the web interface
"""

import asyncio
import websockets
import json
import sys

async def test_websocket():
    print('🌐 Testing WebSocket connection to web interface...')
    
    try:
        uri = 'ws://localhost:8000/ws/test_user'
        
        # Connect to WebSocket
        async with websockets.connect(uri) as websocket:
            print('✅ WebSocket connected successfully!')
            
            # Send a test message
            test_message = {
                'type': 'user_message',
                'content': 'Hello! Can you help me organize my desktop?'
            }
            
            await websocket.send(json.dumps(test_message))
            print('📤 Sent test message')
            
            # Wait for response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                response_data = json.loads(response)
                
                print('📥 Received response:')
                print(f'   Type: {response_data.get("type")}')
                print(f'   Content: {response_data.get("content", "No content")[:100]}...')
                
                print('✅ WebSocket communication working!')
                
            except asyncio.TimeoutError:
                print('⏰ Response timeout - but connection is working!')
                
    except ConnectionRefusedError:
        print('❌ Connection refused - make sure web server is running on port 8000')
    except Exception as e:
        print(f'❌ WebSocket test failed: {e}')

if __name__ == "__main__":
    asyncio.run(test_websocket())
