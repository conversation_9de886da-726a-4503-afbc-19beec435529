# Core framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database and storage
sqlalchemy>=2.0.0
alembic>=1.13.0
redis>=5.0.0

# AI and ML
llama-cpp-python>=0.2.20
transformers>=4.35.0
torch>=2.1.0
pillow>=10.1.0
opencv-python>=4.8.0

# Desktop interaction
pyautogui>=0.9.54
pynput>=1.7.6
psutil>=5.9.6
pygetwindow>=0.0.9

# Cross-platform support
pywin32>=306; sys_platform == "win32"
pyobjc-framework-Cocoa>=10.0; sys_platform == "darwin"
python-xlib>=0.33; sys_platform == "linux"

# Security and encryption
cryptography>=41.0.0
pyjwt>=2.8.0
bcrypt>=4.1.0

# Async and concurrency
asyncio-mqtt>=0.16.0
aioredis>=2.0.0
celery>=5.3.0

# Logging and monitoring
structlog>=23.2.0
prometheus-client>=0.19.0
opentelemetry-api>=1.21.0
opentelemetry-sdk>=1.21.0

# HTTP and networking
httpx>=0.25.0
websockets>=12.0
aiofiles>=23.2.0

# Configuration and utilities
click>=8.1.0
rich>=13.7.0
typer>=0.9.0
python-dotenv>=1.0.0
pyyaml>=6.0.1
toml>=0.10.2
