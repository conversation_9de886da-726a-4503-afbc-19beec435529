# Desktop AI Agent - API Specifications

## API Architecture Overview

The Desktop AI Agent uses a hybrid API architecture combining REST APIs for synchronous operations, WebSocket connections for real-time communication, and message queues for asynchronous processing. All APIs follow OpenAPI 3.0 specification and implement comprehensive authentication, authorization, and rate limiting.

## Core API Endpoints

### Central Orchestration Layer API

#### Task Management API

**Base URL**: `/api/v1/tasks`

```yaml
# Create Task
POST /api/v1/tasks
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "name": "string",
  "description": "string",
  "type": "enum[simple, complex, workflow]",
  "priority": "enum[low, normal, high, critical]",
  "parameters": {
    "key": "value"
  },
  "dependencies": ["task_id_1", "task_id_2"],
  "timeout": "integer (seconds)",
  "retry_policy": {
    "max_retries": "integer",
    "backoff_strategy": "enum[linear, exponential]"
  }
}

Response:
{
  "task_id": "uuid",
  "status": "enum[queued, running, completed, failed, cancelled]",
  "created_at": "timestamp",
  "estimated_duration": "integer (seconds)"
}
```

```yaml
# Get Task Status
GET /api/v1/tasks/{task_id}
Authorization: Bearer {token}

Response:
{
  "task_id": "uuid",
  "name": "string",
  "status": "enum[queued, running, completed, failed, cancelled]",
  "progress": "float (0.0-1.0)",
  "current_step": "string",
  "steps_completed": "integer",
  "total_steps": "integer",
  "started_at": "timestamp",
  "completed_at": "timestamp",
  "error_message": "string",
  "result": "object"
}
```

```yaml
# List Tasks
GET /api/v1/tasks
Authorization: Bearer {token}
Query Parameters:
  - status: enum[queued, running, completed, failed, cancelled]
  - limit: integer (default: 50, max: 200)
  - offset: integer (default: 0)
  - sort: enum[created_at, priority, status]
  - order: enum[asc, desc]

Response:
{
  "tasks": [
    {
      "task_id": "uuid",
      "name": "string",
      "status": "string",
      "progress": "float",
      "created_at": "timestamp"
    }
  ],
  "total": "integer",
  "limit": "integer",
  "offset": "integer"
}
```

#### Component Management API

**Base URL**: `/api/v1/components`

```yaml
# Get Component Status
GET /api/v1/components/{component_id}/status
Authorization: Bearer {token}

Response:
{
  "component_id": "string",
  "name": "string",
  "status": "enum[healthy, degraded, unhealthy, offline]",
  "version": "string",
  "uptime": "integer (seconds)",
  "last_heartbeat": "timestamp",
  "metrics": {
    "cpu_usage": "float",
    "memory_usage": "float",
    "active_connections": "integer"
  }
}
```

### Desktop Interaction Engine API

#### Window Management API

**Base URL**: `/api/v1/desktop/windows`

```yaml
# List Windows
GET /api/v1/desktop/windows
Authorization: Bearer {token}

Response:
{
  "windows": [
    {
      "window_id": "string",
      "title": "string",
      "application": "string",
      "process_id": "integer",
      "position": {
        "x": "integer",
        "y": "integer"
      },
      "size": {
        "width": "integer",
        "height": "integer"
      },
      "state": "enum[normal, minimized, maximized, fullscreen]",
      "is_active": "boolean",
      "is_visible": "boolean"
    }
  ]
}
```

```yaml
# Control Window
POST /api/v1/desktop/windows/{window_id}/actions
Authorization: Bearer {token}

Request Body:
{
  "action": "enum[focus, minimize, maximize, restore, close, move, resize]",
  "parameters": {
    "x": "integer",
    "y": "integer",
    "width": "integer",
    "height": "integer"
  }
}

Response:
{
  "success": "boolean",
  "message": "string"
}
```

#### Screen Capture API

**Base URL**: `/api/v1/desktop/screen`

```yaml
# Capture Screenshot
POST /api/v1/desktop/screen/capture
Authorization: Bearer {token}

Request Body:
{
  "region": {
    "x": "integer",
    "y": "integer",
    "width": "integer",
    "height": "integer"
  },
  "format": "enum[png, jpg, bmp]",
  "quality": "integer (1-100)",
  "include_cursor": "boolean"
}

Response:
{
  "image_id": "uuid",
  "image_data": "base64_string",
  "metadata": {
    "width": "integer",
    "height": "integer",
    "format": "string",
    "size_bytes": "integer",
    "captured_at": "timestamp"
  }
}
```

#### Input Simulation API

**Base URL**: `/api/v1/desktop/input`

```yaml
# Simulate Keyboard Input
POST /api/v1/desktop/input/keyboard
Authorization: Bearer {token}

Request Body:
{
  "action": "enum[key_press, key_release, key_combination, type_text]",
  "keys": ["string"],
  "text": "string",
  "modifiers": ["ctrl", "alt", "shift", "meta"],
  "delay": "integer (milliseconds)"
}

Response:
{
  "success": "boolean",
  "message": "string"
}
```

```yaml
# Simulate Mouse Input
POST /api/v1/desktop/input/mouse
Authorization: Bearer {token}

Request Body:
{
  "action": "enum[move, click, double_click, right_click, scroll, drag]",
  "position": {
    "x": "integer",
    "y": "integer"
  },
  "button": "enum[left, right, middle]",
  "scroll_direction": "enum[up, down, left, right]",
  "scroll_amount": "integer",
  "drag_to": {
    "x": "integer",
    "y": "integer"
  }
}

Response:
{
  "success": "boolean",
  "message": "string"
}
```

### Model Inference and Response Generation API

#### Model Management API

**Base URL**: `/api/v1/models`

```yaml
# List Available Models
GET /api/v1/models
Authorization: Bearer {token}

Response:
{
  "models": [
    {
      "model_id": "string",
      "name": "string",
      "type": "enum[llm, vlm, specialized]",
      "version": "string",
      "status": "enum[loaded, loading, unloaded, error]",
      "capabilities": ["text_generation", "image_analysis", "code_generation"],
      "resource_usage": {
        "memory_mb": "integer",
        "gpu_memory_mb": "integer"
      }
    }
  ]
}
```

#### Inference API

**Base URL**: `/api/v1/inference`

```yaml
# Text Generation
POST /api/v1/inference/generate
Authorization: Bearer {token}

Request Body:
{
  "model_id": "string",
  "prompt": "string",
  "max_tokens": "integer",
  "temperature": "float (0.0-2.0)",
  "top_p": "float (0.0-1.0)",
  "stop_sequences": ["string"],
  "stream": "boolean"
}

Response:
{
  "response_id": "uuid",
  "text": "string",
  "tokens_used": "integer",
  "finish_reason": "enum[stop, length, error]",
  "model_info": {
    "model_id": "string",
    "inference_time_ms": "integer"
  }
}
```

```yaml
# Image Analysis
POST /api/v1/inference/analyze_image
Authorization: Bearer {token}

Request Body:
{
  "model_id": "string",
  "image_data": "base64_string",
  "prompt": "string",
  "analysis_type": "enum[caption, vqa, ocr, object_detection]"
}

Response:
{
  "response_id": "uuid",
  "analysis": {
    "caption": "string",
    "objects": [
      {
        "label": "string",
        "confidence": "float",
        "bounding_box": {
          "x": "integer",
          "y": "integer",
          "width": "integer",
          "height": "integer"
        }
      }
    ],
    "text": "string",
    "answer": "string"
  }
}
```

### Safety and Security API

#### Permission Management API

**Base URL**: `/api/v1/security/permissions`

```yaml
# Request Permission
POST /api/v1/security/permissions/request
Authorization: Bearer {token}

Request Body:
{
  "operation": "string",
  "resource": "string",
  "justification": "string",
  "duration": "integer (seconds)",
  "risk_level": "enum[low, medium, high, critical]"
}

Response:
{
  "permission_id": "uuid",
  "status": "enum[granted, denied, pending]",
  "expires_at": "timestamp",
  "conditions": ["string"]
}
```

#### Audit API

**Base URL**: `/api/v1/security/audit`

```yaml
# Get Audit Logs
GET /api/v1/security/audit/logs
Authorization: Bearer {token}
Query Parameters:
  - start_time: timestamp
  - end_time: timestamp
  - user_id: string
  - action: string
  - resource: string
  - limit: integer

Response:
{
  "logs": [
    {
      "log_id": "uuid",
      "timestamp": "timestamp",
      "user_id": "string",
      "action": "string",
      "resource": "string",
      "result": "enum[success, failure, error]",
      "details": "object"
    }
  ]
}
```

## WebSocket API Specifications

### Real-Time Task Updates

**Endpoint**: `wss://api.example.com/ws/tasks`

```yaml
# Connection Authentication
Connection Headers:
  Authorization: Bearer {token}
  Sec-WebSocket-Protocol: daa-v1

# Subscribe to Task Updates
Send:
{
  "type": "subscribe",
  "task_ids": ["uuid1", "uuid2"],
  "events": ["status_change", "progress_update", "completion"]
}

# Task Status Update
Receive:
{
  "type": "task_update",
  "task_id": "uuid",
  "status": "string",
  "progress": "float",
  "message": "string",
  "timestamp": "timestamp"
}
```

### System Health Monitoring

**Endpoint**: `wss://api.example.com/ws/health`

```yaml
# System Health Update
Receive:
{
  "type": "health_update",
  "component": "string",
  "status": "enum[healthy, degraded, unhealthy]",
  "metrics": {
    "cpu_usage": "float",
    "memory_usage": "float",
    "response_time": "integer"
  },
  "timestamp": "timestamp"
}
```

## Message Queue Specifications

### Task Execution Queue

**Queue Name**: `task_execution`

```yaml
# Task Message Format
{
  "task_id": "uuid",
  "type": "string",
  "priority": "integer",
  "payload": {
    "operation": "string",
    "parameters": "object",
    "context": "object"
  },
  "metadata": {
    "created_at": "timestamp",
    "retry_count": "integer",
    "max_retries": "integer"
  }
}
```

### Event Notification Queue

**Queue Name**: `event_notifications`

```yaml
# Event Message Format
{
  "event_id": "uuid",
  "type": "string",
  "source": "string",
  "data": "object",
  "timestamp": "timestamp",
  "correlation_id": "uuid"
}
```

## Plugin API Specifications

### Plugin Registration API

**Base URL**: `/api/v1/plugins`

```yaml
# Register Plugin
POST /api/v1/plugins/register
Authorization: Bearer {token}

Request Body:
{
  "name": "string",
  "version": "string",
  "description": "string",
  "capabilities": ["string"],
  "endpoints": [
    {
      "path": "string",
      "method": "string",
      "description": "string"
    }
  ],
  "permissions": ["string"],
  "dependencies": ["string"]
}

Response:
{
  "plugin_id": "uuid",
  "api_key": "string",
  "status": "enum[registered, active, inactive, error]"
}
```

### Plugin Execution API

**Base URL**: `/api/v1/plugins/{plugin_id}`

```yaml
# Execute Plugin Function
POST /api/v1/plugins/{plugin_id}/execute
Authorization: Bearer {plugin_api_key}

Request Body:
{
  "function": "string",
  "parameters": "object",
  "context": {
    "user_id": "string",
    "session_id": "string",
    "task_id": "string"
  }
}

Response:
{
  "result": "object",
  "status": "enum[success, error]",
  "message": "string",
  "execution_time_ms": "integer"
}
```

## Error Handling and Status Codes

### HTTP Status Codes

- **200 OK**: Successful request
- **201 Created**: Resource created successfully
- **202 Accepted**: Request accepted for processing
- **400 Bad Request**: Invalid request format or parameters
- **401 Unauthorized**: Authentication required or invalid
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict or constraint violation
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error
- **503 Service Unavailable**: Service temporarily unavailable

### Error Response Format

```yaml
{
  "error": {
    "code": "string",
    "message": "string",
    "details": "object",
    "timestamp": "timestamp",
    "request_id": "uuid"
  }
}
```

## Authentication and Authorization

### JWT Token Format

```yaml
Header:
{
  "alg": "RS256",
  "typ": "JWT"
}

Payload:
{
  "sub": "user_id",
  "iss": "desktop_ai_agent",
  "aud": "api",
  "exp": "timestamp",
  "iat": "timestamp",
  "permissions": ["string"],
  "role": "string"
}
```

### API Key Authentication

```yaml
Header:
Authorization: Bearer {api_key}
X-API-Version: v1
```

## Rate Limiting

### Rate Limit Headers

```yaml
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 3600
```

### Rate Limit Tiers

- **Basic**: 100 requests/hour
- **Standard**: 1,000 requests/hour
- **Premium**: 10,000 requests/hour
- **Enterprise**: Unlimited with fair use policy
```
