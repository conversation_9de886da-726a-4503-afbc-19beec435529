# Desktop AI Agent Configuration
# Copy this file to .env and modify the values as needed

# Application Settings
DAA_ENVIRONMENT=development
DAA_DEBUG=true
DAA_APP_NAME="Desktop AI Agent"
DAA_APP_VERSION="0.1.0"

# Database Settings
DAA_DB_URL=sqlite:///./daa.db
DAA_DB_ECHO=false
DAA_DB_POOL_SIZE=5
DAA_DB_MAX_OVERFLOW=10

# Redis Settings
DAA_REDIS_HOST=localhost
DAA_REDIS_PORT=6379
DAA_REDIS_DB=0
DAA_REDIS_PASSWORD=
DAA_REDIS_MAX_CONNECTIONS=20

# Security Settings
DAA_SECURITY_SECRET_KEY=your-secret-key-change-in-production
DAA_SECURITY_ALGORITHM=HS256
DAA_SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30
DAA_SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7
DAA_SECURITY_BCRYPT_ROUNDS=12

# AI Model Settings
DAA_AI_MODEL_PATH=
DAA_AI_MODEL_NAME=codellama-7b-instruct.gguf
DAA_AI_CONTEXT_LENGTH=4096
DAA_AI_MAX_TOKENS=512
DAA_AI_TEMPERATURE=0.7
DAA_AI_TOP_P=0.9
DAA_AI_N_GPU_LAYERS=0
DAA_AI_N_THREADS=

# Desktop Interaction Settings
DAA_DESKTOP_SCREENSHOT_FORMAT=PNG
DAA_DESKTOP_SCREENSHOT_QUALITY=95
DAA_DESKTOP_INPUT_DELAY=0.1
DAA_DESKTOP_WINDOW_DETECTION_TIMEOUT=5.0
DAA_DESKTOP_SAFE_MODE=true

# Logging Settings
DAA_LOG_LEVEL=INFO
DAA_LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
DAA_LOG_FILE_PATH=
DAA_LOG_MAX_FILE_SIZE=10485760
DAA_LOG_BACKUP_COUNT=5
DAA_LOG_STRUCTURED=true

# API Settings
DAA_API_HOST=127.0.0.1
DAA_API_PORT=8000
DAA_API_WORKERS=1
DAA_API_RELOAD=true
DAA_API_DEBUG=true
DAA_API_CORS_ORIGINS=["http://localhost:3000"]
DAA_API_RATE_LIMIT_REQUESTS=100
