#!/usr/bin/env python3
"""
Test script for the enhanced web interface with task execution capabilities
"""

import asyncio
import websockets
import json
import time

async def test_task_execution():
    """Test the task execution capabilities of the web interface."""
    print('🧪 Testing Enhanced Web Interface with Task Execution')
    print('=' * 60)
    
    try:
        uri = 'ws://localhost:8000/ws/test_user'
        
        async with websockets.connect(uri) as websocket:
            print('✅ WebSocket connected successfully!')
            
            # Test 1: Regular conversation
            print('\n📝 Test 1: Regular Conversation')
            await send_message(websocket, 'Hello! How are you today?')
            await receive_and_print_response(websocket, 'Regular conversation')
            
            # Test 2: Screenshot task
            print('\n📸 Test 2: Screenshot Task')
            await send_message(websocket, 'take a screenshot')
            await receive_and_print_response(websocket, 'Screenshot task')
            
            # Test 3: File organization task
            print('\n📁 Test 3: File Organization Task')
            await send_message(websocket, 'organize my desktop files')
            await receive_and_print_response(websocket, 'File organization task')
            
            # Test 4: Folder creation task
            print('\n📂 Test 4: Folder Creation Task')
            await send_message(websocket, 'create a new folder called "AI_Projects"')
            await receive_and_print_response(websocket, 'Folder creation task')
            
            # Test 5: Window management task
            print('\n🪟 Test 5: Window Management Task')
            await send_message(websocket, 'arrange all windows on my screen')
            await receive_and_print_response(websocket, 'Window management task')
            
            print('\n🎉 All tests completed!')
            
    except ConnectionRefusedError:
        print('❌ Connection refused - make sure web server is running on port 8000')
    except Exception as e:
        print(f'❌ Test failed: {e}')

async def send_message(websocket, content):
    """Send a message to the websocket."""
    message = {
        'type': 'user_message',
        'content': content,
        'user_id': 'test_user'
    }
    
    await websocket.send(json.dumps(message))
    print(f'📤 Sent: "{content}"')

async def receive_and_print_response(websocket, test_name):
    """Receive and print responses from the websocket."""
    print(f'📥 Waiting for responses from {test_name}...')
    
    timeout = 30  # 30 seconds timeout
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            response_data = json.loads(response)
            
            message_type = response_data.get('type', 'unknown')
            content = response_data.get('content', 'No content')
            
            print(f'   📨 {message_type}: {content[:100]}{"..." if len(content) > 100 else ""}')
            
            # Check for task completion or conversation end
            if message_type in ['agent_message', 'task_completed', 'task_failed']:
                break
                
        except asyncio.TimeoutError:
            print('   ⏰ Response timeout - continuing...')
            break
        except Exception as e:
            print(f'   ❌ Error receiving response: {e}')
            break
    
    print(f'✅ {test_name} completed\n')

if __name__ == "__main__":
    asyncio.run(test_task_execution())
