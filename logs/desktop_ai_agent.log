2025-07-20 10:02:17,135 - desktop_ai_agent.monitoring.logger - INFO - {"level": "info", "structured": true, "file_logging": true, "event": "Logging configured", "logger": "desktop_ai_agent.monitoring.logger", "timestamp": "2025-07-20T15:02:17.134989Z"}
2025-07-20 10:02:17,135 - desktop_ai_agent.main - INFO - {"version": "0.1.0", "event": "Initializing Desktop AI Agent", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.135333Z"}
2025-07-20 10:02:17,135 - desktop_ai_agent.main - INFO - {"event": "Initializing monitoring components", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.135450Z"}
2025-07-20 10:02:17,135 - desktop_ai_agent.monitoring.metrics - INFO - {"retention_hours": 24, "event": "Metrics collector initialized", "logger": "desktop_ai_agent.monitoring.metrics", "level": "info", "timestamp": "2025-07-20T15:02:17.135537Z"}
2025-07-20 10:02:17,135 - desktop_ai_agent.monitoring.metrics - INFO - {"event": "Metrics collection started", "logger": "desktop_ai_agent.monitoring.metrics", "level": "info", "timestamp": "2025-07-20T15:02:17.135632Z"}
2025-07-20 10:02:17,135 - desktop_ai_agent.monitoring.health - INFO - {"check_interval": 30, "event": "Health monitor initialized", "logger": "desktop_ai_agent.monitoring.health", "level": "info", "timestamp": "2025-07-20T15:02:17.135710Z"}
2025-07-20 10:02:17,135 - desktop_ai_agent.monitoring.health - INFO - {"event": "Health monitoring started", "logger": "desktop_ai_agent.monitoring.health", "level": "info", "timestamp": "2025-07-20T15:02:17.135781Z"}
2025-07-20 10:02:17,135 - desktop_ai_agent.main - INFO - {"event": "Monitoring components initialized", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.135837Z"}
2025-07-20 10:02:17,135 - desktop_ai_agent.main - INFO - {"event": "Initializing core engines", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.135890Z"}
2025-07-20 10:02:17,142 - desktop_ai_agent.storage.database - INFO - {"url": "sqlite:///data/daa.db", "event": "Database manager initialized", "logger": "desktop_ai_agent.storage.database", "level": "info", "timestamp": "2025-07-20T15:02:17.142324Z"}
2025-07-20 10:02:17,142 - desktop_ai_agent.storage.session_manager - INFO - {"event": "Session manager initialized", "logger": "desktop_ai_agent.storage.session_manager", "level": "info", "timestamp": "2025-07-20T15:02:17.142563Z"}
2025-07-20 10:02:17,142 - desktop_ai_agent.storage.engine - INFO - {"event": "State Management Engine initialized", "logger": "desktop_ai_agent.storage.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.142692Z"}
2025-07-20 10:02:17,159 - desktop_ai_agent.storage.database - INFO - {"event": "Database schema initialized", "logger": "desktop_ai_agent.storage.database", "level": "info", "timestamp": "2025-07-20T15:02:17.159668Z"}
2025-07-20 10:02:17,160 - desktop_ai_agent.storage.engine - INFO - {"event": "State Management Engine started", "logger": "desktop_ai_agent.storage.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.160015Z"}
2025-07-20 10:02:17,160 - desktop_ai_agent.safety.engine - INFO - {"event": "Default security policies loaded", "logger": "desktop_ai_agent.safety.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.160350Z"}
2025-07-20 10:02:17,160 - desktop_ai_agent.safety.engine - INFO - {"event": "Safety Engine initialized", "logger": "desktop_ai_agent.safety.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.160511Z"}
2025-07-20 10:02:17,160 - desktop_ai_agent.safety.engine - INFO - {"event": "Safety Engine started", "logger": "desktop_ai_agent.safety.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.160635Z"}
2025-07-20 10:02:17,163 - desktop_ai_agent.desktop.window_manager - INFO - {"platform": "linux", "event": "Window manager initialized", "logger": "desktop_ai_agent.desktop.window_manager", "level": "info", "timestamp": "2025-07-20T15:02:17.163769Z"}
2025-07-20 10:02:17,211 - desktop_ai_agent.desktop.input_controller - INFO - {"platform": "linux", "safe_mode": true, "event": "Input controller initialized", "logger": "desktop_ai_agent.desktop.input_controller", "level": "info", "timestamp": "2025-07-20T15:02:17.211560Z"}
2025-07-20 10:02:17,211 - desktop_ai_agent.desktop.screen_analyzer - INFO - {"platform": "linux", "event": "Screen analyzer initialized", "logger": "desktop_ai_agent.desktop.screen_analyzer", "level": "info", "timestamp": "2025-07-20T15:02:17.211917Z"}
2025-07-20 10:02:17,212 - desktop_ai_agent.desktop.engine - INFO - {"event": "Desktop Interaction Engine initialized", "logger": "desktop_ai_agent.desktop.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.212107Z"}
2025-07-20 10:02:17,212 - desktop_ai_agent.desktop.engine - INFO - {"event": "Desktop Interaction Engine started", "logger": "desktop_ai_agent.desktop.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.212270Z"}
2025-07-20 10:02:17,212 - desktop_ai_agent.ai.model_manager - INFO - {"model_paths": 2, "event": "Model manager initialized", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:02:17.212554Z"}
2025-07-20 10:02:17,212 - desktop_ai_agent.ai.context_manager - INFO - {"max_contexts": 100, "event": "Context manager initialized", "logger": "desktop_ai_agent.ai.context_manager", "level": "info", "timestamp": "2025-07-20T15:02:17.212698Z"}
2025-07-20 10:02:17,212 - desktop_ai_agent.ai.engine - INFO - {"event": "Model Inference Engine initialized", "logger": "desktop_ai_agent.ai.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.212824Z"}
2025-07-20 10:02:17,213 - desktop_ai_agent.ai.model_manager - INFO - {"discovered": 3, "event": "Model discovery completed", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:02:17.213421Z"}
2025-07-20 10:02:17,213 - desktop_ai_agent.ai.model_manager - WARNING - {"event": "llama-cpp-python not available, using mock model", "logger": "desktop_ai_agent.ai.model_manager", "level": "warning", "timestamp": "2025-07-20T15:02:17.213666Z"}
2025-07-20 10:02:17,213 - desktop_ai_agent.ai.model_manager - INFO - {"model_id": "local_codellama-7b-instruct.Q4_K_M", "load_time_ms": 0.16427040100097656, "event": "Model loaded successfully", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:02:17.213753Z"}
2025-07-20 10:02:17,213 - desktop_ai_agent.ai.model_manager - INFO - {"available_models": 4, "event": "Model manager initialized", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:02:17.213814Z"}
2025-07-20 10:02:17,213 - desktop_ai_agent.ai.engine - INFO - {"event": "Model Inference Engine started", "logger": "desktop_ai_agent.ai.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.213872Z"}
2025-07-20 10:02:17,213 - desktop_ai_agent.planning.decomposer - INFO - {"event": "Task decomposer initialized", "logger": "desktop_ai_agent.planning.decomposer", "level": "info", "timestamp": "2025-07-20T15:02:17.213963Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.planning.scheduler - INFO - {"max_concurrent": 3, "event": "Execution scheduler initialized", "logger": "desktop_ai_agent.planning.scheduler", "level": "info", "timestamp": "2025-07-20T15:02:17.214046Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.planning.engine - INFO - {"event": "Task Planning Engine initialized", "logger": "desktop_ai_agent.planning.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.214119Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.planning.engine - INFO - {"event": "Task Planning Engine started", "logger": "desktop_ai_agent.planning.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.214197Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.main - INFO - {"event": "Core engines initialized", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.214255Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.main - INFO - {"event": "Initializing central orchestrator", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.214313Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Central Orchestrator initialized", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.214434Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "state_management_engine", "name": "State Management and Context Retention Engine", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.214541Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "safety_engine", "name": "Safety and Sandboxing Layer", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.214609Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "desktop_interaction_engine", "name": "Desktop Interaction Engine", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.214682Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "model_inference_engine", "name": "Model Inference and Response Generation Engine", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.214751Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "task_planning_engine", "name": "Task Planning and Execution Engine", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.214820Z"}
2025-07-20 10:02:17,214 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Starting Central Orchestrator", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.214889Z"}
2025-07-20 10:02:17,215 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Central Orchestrator started successfully", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.214983Z"}
2025-07-20 10:02:17,215 - desktop_ai_agent.main - INFO - {"event": "Central orchestrator initialized", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.215041Z"}
2025-07-20 10:02:17,215 - desktop_ai_agent.main - INFO - {"event": "Setting up component integrations", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.215111Z"}
2025-07-20 10:02:17,215 - desktop_ai_agent.planning.engine - INFO - {"event": "Executor callback set", "logger": "desktop_ai_agent.planning.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.215205Z"}
2025-07-20 10:02:17,215 - desktop_ai_agent.main - INFO - {"event": "Component integrations set up", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.215346Z"}
2025-07-20 10:02:17,215 - desktop_ai_agent.main - INFO - {"event": "Initializing API application", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.215402Z"}
2025-07-20 10:02:17,221 - desktop_ai_agent.api.app - INFO - {"event": "FastAPI application created", "logger": "desktop_ai_agent.api.app", "level": "info", "timestamp": "2025-07-20T15:02:17.221292Z"}
2025-07-20 10:02:17,221 - desktop_ai_agent.main - INFO - {"event": "API application initialized", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.221497Z"}
2025-07-20 10:02:17,221 - desktop_ai_agent.main - INFO - {"event": "Desktop AI Agent initialization completed", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.221607Z"}
2025-07-20 10:02:17,221 - desktop_ai_agent.core.orchestrator - INFO - {"task_id": "a16f305b-89a0-4b8d-9081-6e8e113a23f5", "name": "System Test Task", "priority": "normal", "event": "Task added to queue", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.221807Z"}
2025-07-20 10:02:17,224 - desktop_ai_agent.desktop.screen_analyzer - ERROR - {"region": null, "error": "To take screenshots, you must install Pillow version 9.2.0 or greater and gnome-screenshot by running `sudo apt install gnome-screenshot`", "event": "Failed to capture screenshot", "logger": "desktop_ai_agent.desktop.screen_analyzer", "level": "error", "timestamp": "2025-07-20T15:02:17.224381Z"}
2025-07-20 10:02:17,240 - desktop_ai_agent.storage.session_manager - INFO - {"session_id": "0e650ed7-d08a-46c5-a153-545cbf19e3fe", "user_id": "test_user", "event": "Session created", "logger": "desktop_ai_agent.storage.session_manager", "level": "info", "timestamp": "2025-07-20T15:02:17.240651Z"}
2025-07-20 10:02:17,241 - desktop_ai_agent.safety.engine - INFO - {"permission_id": "93c05ddb-0db6-4e8a-9ab1-04ad271295f0", "operation": "test_operation", "resource": "test_resource", "status": "pending", "risk_level": "low", "event": "Permission requested", "logger": "desktop_ai_agent.safety.engine", "level": "info", "timestamp": "2025-07-20T15:02:17.241200Z"}
2025-07-20 10:02:17,241 - desktop_ai_agent.main - INFO - {"event": "Stopping Desktop AI Agent", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:17.241345Z"}
2025-07-20 10:02:17,241 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Stopping Central Orchestrator", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:17.241418Z"}
2025-07-20 10:02:18,242 - desktop_ai_agent.storage.session_manager - INFO - {"count": 0, "event": "Expired sessions cleaned up", "logger": "desktop_ai_agent.storage.session_manager", "level": "info", "timestamp": "2025-07-20T15:02:18.242553Z"}
2025-07-20 10:02:18,343 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Central Orchestrator stopped", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:02:18.343468Z"}
2025-07-20 10:02:18,343 - desktop_ai_agent.planning.engine - INFO - {"event": "Task Planning Engine stopped", "logger": "desktop_ai_agent.planning.engine", "level": "info", "timestamp": "2025-07-20T15:02:18.343658Z"}
2025-07-20 10:02:18,343 - desktop_ai_agent.ai.model_manager - INFO - {"model_id": "local_codellama-7b-instruct.Q4_K_M", "event": "Model unloaded", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:02:18.343799Z"}
2025-07-20 10:02:18,343 - desktop_ai_agent.ai.model_manager - INFO - {"event": "Model manager cleanup completed", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:02:18.343870Z"}
2025-07-20 10:02:18,343 - desktop_ai_agent.ai.engine - INFO - {"event": "Model Inference Engine stopped", "logger": "desktop_ai_agent.ai.engine", "level": "info", "timestamp": "2025-07-20T15:02:18.343961Z"}
2025-07-20 10:02:18,344 - desktop_ai_agent.desktop.engine - INFO - {"event": "Desktop Interaction Engine stopped", "logger": "desktop_ai_agent.desktop.engine", "level": "info", "timestamp": "2025-07-20T15:02:18.344038Z"}
2025-07-20 10:02:18,344 - desktop_ai_agent.safety.engine - INFO - {"event": "Safety Engine stopped", "logger": "desktop_ai_agent.safety.engine", "level": "info", "timestamp": "2025-07-20T15:02:18.344111Z"}
2025-07-20 10:02:18,344 - desktop_ai_agent.storage.database - INFO - {"event": "Database cleanup completed", "logger": "desktop_ai_agent.storage.database", "level": "info", "timestamp": "2025-07-20T15:02:18.344494Z"}
2025-07-20 10:02:18,344 - desktop_ai_agent.storage.engine - INFO - {"event": "State Management Engine stopped", "logger": "desktop_ai_agent.storage.engine", "level": "info", "timestamp": "2025-07-20T15:02:18.344598Z"}
2025-07-20 10:02:18,344 - desktop_ai_agent.monitoring.health - INFO - {"event": "Health monitoring stopped", "logger": "desktop_ai_agent.monitoring.health", "level": "info", "timestamp": "2025-07-20T15:02:18.344743Z"}
2025-07-20 10:02:18,344 - desktop_ai_agent.monitoring.metrics - INFO - {"event": "Metrics collection stopped", "logger": "desktop_ai_agent.monitoring.metrics", "level": "info", "timestamp": "2025-07-20T15:02:18.344866Z"}
2025-07-20 10:02:18,344 - desktop_ai_agent.main - INFO - {"event": "Desktop AI Agent stopped", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:02:18.344935Z"}
2025-07-20 10:05:38,063 - desktop_ai_agent.monitoring.logger - INFO - {"level": "info", "structured": true, "file_logging": true, "event": "Logging configured", "logger": "desktop_ai_agent.monitoring.logger", "timestamp": "2025-07-20T15:05:38.062943Z"}
2025-07-20 10:05:38,063 - desktop_ai_agent.main - INFO - {"version": "0.1.0", "event": "Initializing Desktop AI Agent", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.063174Z"}
2025-07-20 10:05:38,063 - desktop_ai_agent.main - INFO - {"event": "Initializing monitoring components", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.063288Z"}
2025-07-20 10:05:38,063 - desktop_ai_agent.monitoring.metrics - INFO - {"retention_hours": 24, "event": "Metrics collector initialized", "logger": "desktop_ai_agent.monitoring.metrics", "level": "info", "timestamp": "2025-07-20T15:05:38.063418Z"}
2025-07-20 10:05:38,063 - desktop_ai_agent.monitoring.metrics - INFO - {"event": "Metrics collection started", "logger": "desktop_ai_agent.monitoring.metrics", "level": "info", "timestamp": "2025-07-20T15:05:38.063534Z"}
2025-07-20 10:05:38,063 - desktop_ai_agent.monitoring.health - INFO - {"check_interval": 30, "event": "Health monitor initialized", "logger": "desktop_ai_agent.monitoring.health", "level": "info", "timestamp": "2025-07-20T15:05:38.063630Z"}
2025-07-20 10:05:38,063 - desktop_ai_agent.monitoring.health - INFO - {"event": "Health monitoring started", "logger": "desktop_ai_agent.monitoring.health", "level": "info", "timestamp": "2025-07-20T15:05:38.063699Z"}
2025-07-20 10:05:38,063 - desktop_ai_agent.main - INFO - {"event": "Monitoring components initialized", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.063756Z"}
2025-07-20 10:05:38,063 - desktop_ai_agent.main - INFO - {"event": "Initializing core engines", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.063811Z"}
2025-07-20 10:05:38,069 - desktop_ai_agent.storage.database - INFO - {"url": "sqlite:///data/daa.db", "event": "Database manager initialized", "logger": "desktop_ai_agent.storage.database", "level": "info", "timestamp": "2025-07-20T15:05:38.069661Z"}
2025-07-20 10:05:38,069 - desktop_ai_agent.storage.session_manager - INFO - {"event": "Session manager initialized", "logger": "desktop_ai_agent.storage.session_manager", "level": "info", "timestamp": "2025-07-20T15:05:38.069839Z"}
2025-07-20 10:05:38,069 - desktop_ai_agent.storage.engine - INFO - {"event": "State Management Engine initialized", "logger": "desktop_ai_agent.storage.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.069948Z"}
2025-07-20 10:05:38,071 - desktop_ai_agent.storage.database - INFO - {"event": "Database schema initialized", "logger": "desktop_ai_agent.storage.database", "level": "info", "timestamp": "2025-07-20T15:05:38.071173Z"}
2025-07-20 10:05:38,071 - desktop_ai_agent.storage.engine - INFO - {"event": "State Management Engine started", "logger": "desktop_ai_agent.storage.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.071307Z"}
2025-07-20 10:05:38,071 - desktop_ai_agent.safety.engine - INFO - {"event": "Default security policies loaded", "logger": "desktop_ai_agent.safety.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.071486Z"}
2025-07-20 10:05:38,071 - desktop_ai_agent.safety.engine - INFO - {"event": "Safety Engine initialized", "logger": "desktop_ai_agent.safety.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.071556Z"}
2025-07-20 10:05:38,071 - desktop_ai_agent.safety.engine - INFO - {"event": "Safety Engine started", "logger": "desktop_ai_agent.safety.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.071641Z"}
2025-07-20 10:05:38,073 - desktop_ai_agent.desktop.window_manager - INFO - {"platform": "linux", "event": "Window manager initialized", "logger": "desktop_ai_agent.desktop.window_manager", "level": "info", "timestamp": "2025-07-20T15:05:38.073965Z"}
2025-07-20 10:05:38,107 - desktop_ai_agent.desktop.input_controller - INFO - {"platform": "linux", "safe_mode": true, "event": "Input controller initialized", "logger": "desktop_ai_agent.desktop.input_controller", "level": "info", "timestamp": "2025-07-20T15:05:38.107426Z"}
2025-07-20 10:05:38,107 - desktop_ai_agent.desktop.screen_analyzer - INFO - {"platform": "linux", "event": "Screen analyzer initialized", "logger": "desktop_ai_agent.desktop.screen_analyzer", "level": "info", "timestamp": "2025-07-20T15:05:38.107644Z"}
2025-07-20 10:05:38,107 - desktop_ai_agent.desktop.engine - INFO - {"event": "Desktop Interaction Engine initialized", "logger": "desktop_ai_agent.desktop.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.107744Z"}
2025-07-20 10:05:38,107 - desktop_ai_agent.desktop.engine - INFO - {"event": "Desktop Interaction Engine started", "logger": "desktop_ai_agent.desktop.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.107838Z"}
2025-07-20 10:05:38,108 - desktop_ai_agent.ai.model_manager - INFO - {"model_paths": 2, "event": "Model manager initialized", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:05:38.107983Z"}
2025-07-20 10:05:38,108 - desktop_ai_agent.ai.context_manager - INFO - {"max_contexts": 100, "event": "Context manager initialized", "logger": "desktop_ai_agent.ai.context_manager", "level": "info", "timestamp": "2025-07-20T15:05:38.108063Z"}
2025-07-20 10:05:38,108 - desktop_ai_agent.ai.engine - INFO - {"event": "Model Inference Engine initialized", "logger": "desktop_ai_agent.ai.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.108134Z"}
2025-07-20 10:05:38,108 - desktop_ai_agent.ai.model_manager - INFO - {"discovered": 3, "event": "Model discovery completed", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:05:38.108657Z"}
2025-07-20 10:05:38,108 - desktop_ai_agent.ai.model_manager - WARNING - {"event": "llama-cpp-python not available, using mock model", "logger": "desktop_ai_agent.ai.model_manager", "level": "warning", "timestamp": "2025-07-20T15:05:38.108889Z"}
2025-07-20 10:05:38,108 - desktop_ai_agent.ai.model_manager - INFO - {"model_id": "local_codellama-7b-instruct.Q4_K_M", "load_time_ms": 0.1513957977294922, "event": "Model loaded successfully", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:05:38.108970Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.ai.model_manager - INFO - {"available_models": 4, "event": "Model manager initialized", "logger": "desktop_ai_agent.ai.model_manager", "level": "info", "timestamp": "2025-07-20T15:05:38.109029Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.ai.engine - INFO - {"event": "Model Inference Engine started", "logger": "desktop_ai_agent.ai.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.109080Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.planning.decomposer - INFO - {"event": "Task decomposer initialized", "logger": "desktop_ai_agent.planning.decomposer", "level": "info", "timestamp": "2025-07-20T15:05:38.109149Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.planning.scheduler - INFO - {"max_concurrent": 3, "event": "Execution scheduler initialized", "logger": "desktop_ai_agent.planning.scheduler", "level": "info", "timestamp": "2025-07-20T15:05:38.109229Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.planning.engine - INFO - {"event": "Task Planning Engine initialized", "logger": "desktop_ai_agent.planning.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.109297Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.planning.engine - INFO - {"event": "Task Planning Engine started", "logger": "desktop_ai_agent.planning.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.109356Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.main - INFO - {"event": "Core engines initialized", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.109405Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.main - INFO - {"event": "Initializing central orchestrator", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.109464Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Central Orchestrator initialized", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:38.109539Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "state_management_engine", "name": "State Management and Context Retention Engine", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:38.109612Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "safety_engine", "name": "Safety and Sandboxing Layer", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:38.109667Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "desktop_interaction_engine", "name": "Desktop Interaction Engine", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:38.109717Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "model_inference_engine", "name": "Model Inference and Response Generation Engine", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:38.109766Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.core.orchestrator - INFO - {"component_id": "task_planning_engine", "name": "Task Planning and Execution Engine", "version": "0.1.0", "event": "Component registered", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:38.109814Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Starting Central Orchestrator", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:38.109862Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Central Orchestrator started successfully", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:38.109922Z"}
2025-07-20 10:05:38,109 - desktop_ai_agent.main - INFO - {"event": "Central orchestrator initialized", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.109971Z"}
2025-07-20 10:05:38,110 - desktop_ai_agent.main - INFO - {"event": "Setting up component integrations", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.110022Z"}
2025-07-20 10:05:38,110 - desktop_ai_agent.planning.engine - INFO - {"event": "Executor callback set", "logger": "desktop_ai_agent.planning.engine", "level": "info", "timestamp": "2025-07-20T15:05:38.110070Z"}
2025-07-20 10:05:38,110 - desktop_ai_agent.main - INFO - {"event": "Component integrations set up", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.110198Z"}
2025-07-20 10:05:38,110 - desktop_ai_agent.main - INFO - {"event": "Initializing API application", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.110252Z"}
2025-07-20 10:05:38,116 - desktop_ai_agent.api.app - INFO - {"event": "FastAPI application created", "logger": "desktop_ai_agent.api.app", "level": "info", "timestamp": "2025-07-20T15:05:38.116170Z"}
2025-07-20 10:05:38,116 - desktop_ai_agent.main - INFO - {"event": "API application initialized", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.116334Z"}
2025-07-20 10:05:38,116 - desktop_ai_agent.main - INFO - {"event": "Desktop AI Agent initialization completed", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.116400Z"}
2025-07-20 10:05:38,116 - desktop_ai_agent.main - INFO - {"event": "Starting Desktop AI Agent", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.116473Z"}
2025-07-20 10:05:38,116 - desktop_ai_agent.main - INFO - {"host": "127.0.0.1", "port": 8000, "environment": "development", "event": "Desktop AI Agent started", "logger": "desktop_ai_agent.main", "level": "info", "timestamp": "2025-07-20T15:05:38.116955Z"}
2025-07-20 10:05:39,118 - desktop_ai_agent.storage.session_manager - INFO - {"count": 0, "event": "Expired sessions cleaned up", "logger": "desktop_ai_agent.storage.session_manager", "level": "info", "timestamp": "2025-07-20T15:05:39.117985Z"}
2025-07-20 10:05:39,118 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Task worker started", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:39.118191Z"}
2025-07-20 10:05:39,118 - desktop_ai_agent.core.orchestrator - INFO - {"event": "Health monitor started", "logger": "desktop_ai_agent.core.orchestrator", "level": "info", "timestamp": "2025-07-20T15:05:39.118281Z"}
2025-07-20 10:05:39,227 - desktop_ai_agent.api.app - INFO - {"event": "FastAPI application starting up", "logger": "desktop_ai_agent.api.app", "level": "info", "timestamp": "2025-07-20T15:05:39.227667Z"}
2025-07-20 10:05:39,228 - desktop_ai_agent.monitoring.health - WARNING - {"component": "database", "status": "unhealthy", "message": "Database check failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')", "event": "Component health issue", "logger": "desktop_ai_agent.monitoring.health", "level": "warning", "timestamp": "2025-07-20T15:05:39.227999Z"}
2025-07-20 10:06:03,018 - desktop_ai_agent.api.app - INFO - {"method": "GET", "url": "http://127.0.0.1:8000/", "client_ip": "127.0.0.1", "event": "Request started", "logger": "desktop_ai_agent.api.app", "level": "info", "timestamp": "2025-07-20T15:06:03.018546Z"}
2025-07-20 10:06:03,019 - desktop_ai_agent.api.app - INFO - {"method": "GET", "url": "http://127.0.0.1:8000/", "status_code": 200, "process_time": 0.001149, "event": "Request completed", "logger": "desktop_ai_agent.api.app", "level": "info", "timestamp": "2025-07-20T15:06:03.019666Z"}
2025-07-20 10:06:03,105 - desktop_ai_agent.api.app - INFO - {"method": "GET", "url": "http://127.0.0.1:8000/favicon.ico", "client_ip": "127.0.0.1", "event": "Request started", "logger": "desktop_ai_agent.api.app", "level": "info", "timestamp": "2025-07-20T15:06:03.104994Z"}
2025-07-20 10:06:03,105 - desktop_ai_agent.api.app - INFO - {"method": "GET", "url": "http://127.0.0.1:8000/favicon.ico", "status_code": 404, "process_time": 0.000809, "event": "Request completed", "logger": "desktop_ai_agent.api.app", "level": "info", "timestamp": "2025-07-20T15:06:03.105799Z"}
2025-07-20 10:06:09,329 - desktop_ai_agent.monitoring.health - WARNING - {"component": "database", "status": "unhealthy", "message": "Database check failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')", "event": "Component health issue", "logger": "desktop_ai_agent.monitoring.health", "level": "warning", "timestamp": "2025-07-20T15:06:09.329805Z"}
