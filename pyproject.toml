[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "desktop-ai-agent"
version = "0.1.0"
description = "Advanced autonomous desktop AI agent for task automation"
authors = [
    {name = "Desktop AI Agent Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Systems Administration",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
keywords = ["ai", "automation", "desktop", "agent", "llm"]

dependencies = [
    # Core framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # Database and storage
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "redis>=5.0.0",
    
    # AI and ML
    "llama-cpp-python>=0.2.20",
    "transformers>=4.35.0",
    "torch>=2.1.0",
    "pillow>=10.1.0",
    "opencv-python>=4.8.0",
    
    # Desktop interaction
    "pyautogui>=0.9.54",
    "pynput>=1.7.6",
    "psutil>=5.9.6",
    "pygetwindow>=0.0.9",
    
    # Cross-platform support
    "pywin32>=306; sys_platform == 'win32'",
    "pyobjc-framework-Cocoa>=10.0; sys_platform == 'darwin'",
    "python-xlib>=0.33; sys_platform == 'linux'",
    
    # Security and encryption
    "cryptography>=41.0.0",
    "pyjwt>=2.8.0",
    "bcrypt>=4.1.0",
    
    # Async and concurrency
    "asyncio-mqtt>=0.16.0",
    "aioredis>=2.0.0",
    "celery>=5.3.0",
    
    # Logging and monitoring
    "structlog>=23.2.0",
    "prometheus-client>=0.19.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    
    # HTTP and networking
    "httpx>=0.25.0",
    "websockets>=12.0",
    "aiofiles>=23.2.0",
    
    # Configuration and utilities
    "click>=8.1.0",
    "rich>=13.7.0",
    "typer>=0.9.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0.1",
    "toml>=0.10.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
]

docs = [
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
    "sphinx-autodoc-typehints>=1.25.0",
]

[project.urls]
Homepage = "https://github.com/desktop-ai-agent/desktop-ai-agent"
Documentation = "https://desktop-ai-agent.readthedocs.io"
Repository = "https://github.com/desktop-ai-agent/desktop-ai-agent"
"Bug Tracker" = "https://github.com/desktop-ai-agent/desktop-ai-agent/issues"

[project.scripts]
daa = "desktop_ai_agent.cli:main"
desktop-ai-agent = "desktop_ai_agent.main:main"

[tool.setuptools.packages.find]
where = ["src"]
include = ["desktop_ai_agent*"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["desktop_ai_agent"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pyautogui.*",
    "pynput.*",
    "pygetwindow.*",
    "llama_cpp.*",
    "cv2.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "security: marks tests as security tests",
]

[tool.coverage.run]
source = ["src/desktop_ai_agent"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
