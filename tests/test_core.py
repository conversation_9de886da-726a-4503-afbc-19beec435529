"""
Tests for core components of Desktop AI Agent.
"""

import pytest

from desktop_ai_agent.core.config import Settings
from desktop_ai_agent.core.orchestrator import CentralOrchestrator, Task, TaskPriority, TaskStatus


class TestSettings:
    """Test configuration management."""
    
    def test_default_settings(self):
        """Test default settings creation."""
        settings = Settings()
        
        assert settings.app_name == "Desktop AI Agent"
        assert settings.app_version == "0.1.0"
        assert settings.environment == "development"
        assert settings.platform in ["windows", "darwin", "linux"]
    
    def test_database_url_generation(self, test_settings):
        """Test database URL generation."""
        url = test_settings.get_database_url()
        assert url.startswith("sqlite://")
    
    def test_is_development(self, test_settings):
        """Test development environment detection."""
        assert test_settings.is_development is True
        
        test_settings.environment = "production"
        assert test_settings.is_production is True


class TestTask:
    """Test task model."""
    
    def test_task_creation(self):
        """Test task creation with defaults."""
        task = Task(name="Test Task", description="A test task")
        
        assert task.name == "Test Task"
        assert task.description == "A test task"
        assert task.type == "simple"
        assert task.priority == TaskPriority.NORMAL
        assert task.status == TaskStatus.QUEUED
        assert task.progress == 0.0
        assert len(task.task_id) > 0
    
    def test_task_with_parameters(self):
        """Test task creation with parameters."""
        parameters = {"param1": "value1", "param2": 42}
        dependencies = ["task1", "task2"]
        
        task = Task(
            name="Complex Task",
            type="complex",
            priority=TaskPriority.HIGH,
            parameters=parameters,
            dependencies=dependencies
        )
        
        assert task.type == "complex"
        assert task.priority == TaskPriority.HIGH
        assert task.parameters == parameters
        assert task.dependencies == dependencies


@pytest.mark.asyncio
class TestCentralOrchestrator:
    """Test central orchestrator."""
    
    async def test_orchestrator_initialization(self, test_settings):
        """Test orchestrator initialization."""
        orchestrator = CentralOrchestrator(test_settings)
        
        assert orchestrator.settings == test_settings
        assert orchestrator.is_running is False
        
        await orchestrator.start()
        assert orchestrator.is_running is True
        
        await orchestrator.stop()
        assert orchestrator.is_running is False
    
    async def test_task_submission(self, mock_orchestrator):
        """Test task submission."""
        task = Task(name="Test Task", description="Test task submission")
        
        task_id = await mock_orchestrator.submit_task(task)
        assert task_id is not None
        assert len(task_id) > 0
        
        # Check task status
        task_status = mock_orchestrator.get_task_status(task_id)
        assert task_status is not None
        assert task_status.task_id == task_id
        assert task_status.name == "Test Task"
    
    async def test_task_listing(self, mock_orchestrator):
        """Test task listing."""
        # Submit multiple tasks
        task1 = Task(name="Task 1")
        task2 = Task(name="Task 2")
        
        await mock_orchestrator.submit_task(task1)
        await mock_orchestrator.submit_task(task2)
        
        # List all tasks
        all_tasks = mock_orchestrator.list_tasks()
        assert len(all_tasks) >= 2
        
        # List queued tasks
        queued_tasks = mock_orchestrator.list_tasks(TaskStatus.QUEUED)
        assert len(queued_tasks) >= 2
    
    async def test_component_registration(self, mock_orchestrator):
        """Test component registration."""
        from desktop_ai_agent.core.orchestrator import Component, ComponentStatus
        
        component = Component(
            component_id="test_component",
            name="Test Component",
            version="1.0.0",
            status=ComponentStatus.HEALTHY,
            capabilities=["test_capability"]
        )
        
        success = mock_orchestrator.register_component(component)
        assert success is True
        
        # Check component status
        component_status = mock_orchestrator.get_component_status("test_component")
        assert component_status is not None
        assert component_status.name == "Test Component"
        
        # List components
        components = mock_orchestrator.list_components()
        assert len(components) >= 1
        
        # List healthy components
        healthy_components = mock_orchestrator.list_components(ComponentStatus.HEALTHY)
        assert len(healthy_components) >= 1
