"""
Pytest configuration and fixtures for Desktop AI Agent tests.
"""

import asyncio
import tempfile
from pathlib import Path
from typing import Generator

import pytest

from desktop_ai_agent.core.config import Settings


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def test_settings(temp_dir: Path) -> Settings:
    """Create test settings with temporary directories."""
    settings = Settings()
    
    # Use temporary directory for data
    settings.data_dir = temp_dir
    
    # Use in-memory SQLite for testing
    settings.database.url = "sqlite:///:memory:"
    
    # Disable external services for testing
    settings.api.debug = True
    settings.logging.level = "DEBUG"
    settings.desktop.safe_mode = True
    
    return settings


@pytest.fixture
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def mock_orchestrator(test_settings):
    """Create a mock orchestrator for testing."""
    from desktop_ai_agent.core.orchestrator import CentralOrchestrator
    
    orchestrator = CentralOrchestrator(test_settings)
    await orchestrator.start()
    
    yield orchestrator
    
    await orchestrator.stop()
