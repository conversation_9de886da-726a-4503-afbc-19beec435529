"""
Tests for desktop interaction components.
"""

import pytest

from desktop_ai_agent.desktop.models import (
    KeyboardInput,
    KeyAction,
    MouseInput,
    Position,
    Rectangle,
    Size,
    WindowInfo,
    WindowState,
)


class TestDesktopModels:
    """Test desktop interaction models."""
    
    def test_position_model(self):
        """Test Position model."""
        pos = Position(x=100, y=200)
        assert pos.x == 100
        assert pos.y == 200
    
    def test_size_model(self):
        """Test Size model."""
        size = Size(width=800, height=600)
        assert size.width == 800
        assert size.height == 600
    
    def test_rectangle_model(self):
        """Test Rectangle model."""
        rect = Rectangle(x=10, y=20, width=300, height=400)
        
        assert rect.x == 10
        assert rect.y == 20
        assert rect.width == 300
        assert rect.height == 400
        
        # Test properties
        assert rect.position.x == 10
        assert rect.position.y == 20
        assert rect.size.width == 300
        assert rect.size.height == 400
        assert rect.center.x == 160  # 10 + 300/2
        assert rect.center.y == 220  # 20 + 400/2
    
    def test_window_info_model(self):
        """Test WindowInfo model."""
        window = WindowInfo(
            window_id="123",
            title="Test Window",
            application="test_app",
            process_id=456,
            position=Position(x=100, y=100),
            size=Size(width=800, height=600),
            state=WindowState.NORMAL,
            is_active=True,
            is_visible=True
        )
        
        assert window.window_id == "123"
        assert window.title == "Test Window"
        assert window.application == "test_app"
        assert window.process_id == 456
        assert window.state == WindowState.NORMAL
        assert window.is_active is True
        assert window.is_visible is True
        
        # Test rectangle property
        rect = window.rectangle
        assert rect.x == 100
        assert rect.y == 100
        assert rect.width == 800
        assert rect.height == 600
    
    def test_keyboard_input_model(self):
        """Test KeyboardInput model."""
        # Test key press
        key_input = KeyboardInput(
            action=KeyAction.PRESS,
            keys=["ctrl", "c"],
            modifiers=["ctrl"]
        )
        
        assert key_input.action == KeyAction.PRESS
        assert key_input.keys == ["ctrl", "c"]
        assert key_input.modifiers == ["ctrl"]
        
        # Test text typing
        text_input = KeyboardInput(
            action=KeyAction.TYPE,
            text="Hello, World!"
        )
        
        assert text_input.action == KeyAction.TYPE
        assert text_input.text == "Hello, World!"
    
    def test_mouse_input_model(self):
        """Test MouseInput model."""
        mouse_input = MouseInput(
            action="click",
            position=Position(x=200, y=300),
            button="left"
        )
        
        assert mouse_input.action == "click"
        assert mouse_input.position.x == 200
        assert mouse_input.position.y == 300
        assert mouse_input.button == "left"


@pytest.mark.asyncio
class TestDesktopInteractionEngine:
    """Test desktop interaction engine."""
    
    async def test_engine_initialization(self, test_settings):
        """Test desktop interaction engine initialization."""
        from desktop_ai_agent.desktop.engine import DesktopInteractionEngine
        
        engine = DesktopInteractionEngine(test_settings)
        
        assert engine.settings == test_settings
        assert engine.window_manager is not None
        assert engine.input_controller is not None
        assert engine.screen_analyzer is not None
        
        # Test component info
        component_info = engine.get_component_info()
        assert component_info.component_id == "desktop_interaction_engine"
        assert component_info.name == "Desktop Interaction Engine"
        assert "window_management" in component_info.capabilities
        assert "input_simulation" in component_info.capabilities
        assert "screen_capture" in component_info.capabilities
    
    async def test_engine_lifecycle(self, test_settings):
        """Test desktop interaction engine lifecycle."""
        from desktop_ai_agent.desktop.engine import DesktopInteractionEngine
        
        engine = DesktopInteractionEngine(test_settings)
        
        # Start engine
        await engine.start()
        component_info = engine.get_component_info()
        assert component_info.status == "healthy"
        
        # Stop engine
        await engine.stop()
        component_info = engine.get_component_info()
        assert component_info.status == "offline"


class TestWindowManager:
    """Test window manager (basic tests only due to platform dependencies)."""
    
    def test_window_manager_initialization(self, test_settings):
        """Test window manager initialization."""
        # Skip on systems without display
        try:
            from desktop_ai_agent.desktop.window_manager import WindowManager
            
            # This might fail on headless systems, which is expected
            manager = WindowManager()
            assert manager.platform in ["windows", "darwin", "linux"]
        except (RuntimeError, ImportError):
            # Expected on headless systems or missing dependencies
            pytest.skip("Window manager requires display system")


class TestInputController:
    """Test input controller (basic tests only due to platform dependencies)."""
    
    def test_input_controller_initialization(self, test_settings):
        """Test input controller initialization."""
        from desktop_ai_agent.desktop.input_controller import InputController
        
        controller = InputController(safe_mode=True, default_delay=0.1)
        assert controller.safe_mode is True
        assert controller.default_delay == 0.1
        assert controller.platform in ["windows", "darwin", "linux"]
    
    def test_mouse_position_safety(self, test_settings):
        """Test mouse position safety checks."""
        from desktop_ai_agent.desktop.input_controller import InputController
        from desktop_ai_agent.desktop.models import Position
        
        controller = InputController(safe_mode=True)
        
        # Test safe position (this might fail on headless systems)
        try:
            pos = Position(x=100, y=100)
            is_safe = controller.is_safe_position(pos)
            # Result depends on screen size, just check it returns a boolean
            assert isinstance(is_safe, bool)
        except:
            # Expected on headless systems
            pytest.skip("Input controller requires display system")


class TestScreenAnalyzer:
    """Test screen analyzer (basic tests only due to platform dependencies)."""
    
    def test_screen_analyzer_initialization(self, test_settings):
        """Test screen analyzer initialization."""
        from desktop_ai_agent.desktop.screen_analyzer import ScreenAnalyzer
        
        analyzer = ScreenAnalyzer(default_format="PNG", default_quality=95)
        assert analyzer.default_format == "PNG"
        assert analyzer.default_quality == 95
        assert analyzer.platform in ["windows", "darwin", "linux"]
